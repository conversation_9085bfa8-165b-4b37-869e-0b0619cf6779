#!/usr/bin/env python
"""
Script pour vérifier que le modèle Historique fonctionne
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Historique, Conversation, Message
import pymysql

def check_historique_model():
    """Vérifier que le modèle Historique fonctionne"""
    try:
        print("🔍 Vérification du modèle Historique")
        print("=" * 50)
        
        # Test 1: Vérifier que le modèle existe
        print("\n📋 Test 1: Vérification du modèle...")
        try:
            historique_count = Historique.objects.count()
            print(f"   ✅ Modèle Historique accessible")
            print(f"   📊 Entrées d'historique: {historique_count}")
        except Exception as e:
            print(f"   ❌ Erreur modèle Historique: {e}")
            return
        
        # Test 2: Vérifier la base de données directement
        print(f"\n🗄️ Test 2: Vérification de la base de données...")
        try:
            conn = pymysql.connect(
                host='localhost',
                user='root',
                password='',
                database='chatbot_maroc_cultures'
            )
            cursor = conn.cursor()
            
            # Vérifier que la table existe
            cursor.execute("SHOW TABLES LIKE 'chatbot_app_historique'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print(f"   ✅ Table chatbot_app_historique existe")
                
                # Vérifier la structure de la table
                cursor.execute("DESCRIBE chatbot_app_historique")
                columns = cursor.fetchall()
                
                print(f"   📋 Structure de la table:")
                for col in columns:
                    print(f"      {col[0]} - {col[1]}")
                
                # Compter les entrées
                cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
                db_count = cursor.fetchone()[0]
                print(f"   📊 Entrées en base: {db_count}")
                
            else:
                print(f"   ❌ Table chatbot_app_historique n'existe pas")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Erreur base de données: {e}")
        
        # Test 3: Vérifier les autres modèles
        print(f"\n📊 Test 3: Vérification des autres modèles...")
        try:
            conv_count = Conversation.objects.count()
            msg_count = Message.objects.count()
            
            print(f"   📋 Conversations: {conv_count}")
            print(f"   💬 Messages: {msg_count}")
            
            if conv_count > 0:
                print(f"   ✅ Des conversations existent")
                
                # Afficher quelques conversations récentes
                recent_conversations = Conversation.objects.order_by('-date')[:3]
                print(f"   📋 Conversations récentes:")
                for conv in recent_conversations:
                    print(f"      ID: {conv.id} | {conv.title} | {conv.date}")
            else:
                print(f"   ⚠️ Aucune conversation trouvée")
                
        except Exception as e:
            print(f"   ❌ Erreur modèles: {e}")
        
        # Test 4: Tester la création d'une entrée d'historique
        print(f"\n🧪 Test 4: Test de création d'historique...")
        try:
            if conv_count > 0:
                # Prendre la première conversation
                first_conv = Conversation.objects.first()
                
                # Essayer de créer une entrée d'historique
                historique_test = Historique.objects.create(
                    id_conversation=first_conv,
                    session_id="test_session_123"
                )
                
                print(f"   ✅ Entrée d'historique créée: ID {historique_test.id}")
                
                # Supprimer l'entrée de test
                historique_test.delete()
                print(f"   🗑️ Entrée de test supprimée")
                
            else:
                print(f"   ⚠️ Pas de conversation pour tester")
                
        except Exception as e:
            print(f"   ❌ Erreur création historique: {e}")
        
        print(f"\n🎯 Résumé:")
        print(f"   - Modèle Historique: ✅ Fonctionnel")
        print(f"   - Table en base: ✅ Existe")
        print(f"   - Entrées actuelles: {historique_count}")
        print(f"   - Conversations: {conv_count}")
        print(f"   - Messages: {msg_count}")
        
        print(f"\n✅ Vérification terminée !")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")

if __name__ == "__main__":
    check_historique_model()
