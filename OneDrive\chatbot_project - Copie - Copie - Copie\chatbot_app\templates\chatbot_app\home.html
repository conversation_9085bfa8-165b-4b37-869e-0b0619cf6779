{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Accueil - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* General Styles */
    body {
        font-family: 'Poppins', sans-serif;
        color: #333;
    }

    .section {
        padding: 60px 0; /* Réduit de 80px à 60px */
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #c0392b; /* Changed to red */
        position: relative;
        display: inline-block;
    }

    .section-title::after {
        content: '';
        position: absolute;
        width: 50%;
        height: 3px;
        background-color: #27ae60;
        bottom: -10px;
        left: 0;
    }

    .text-highlight {
        color: #c0392b;
        position: relative;
        z-index: 1;
    }

    /* Moroccan-inspired decorative elements */
    .moroccan-pattern {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,20 L20,0 L40,20 L20,40 Z" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1.5"/><path d="M20,10 L30,20 L20,30 L10,20 Z" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="1"/></svg>');
        background-size: 40px 40px;
        opacity: 0.35;
        z-index: -1;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg,
            rgba(192, 57, 43, 0.85) 0%,
            rgba(192, 57, 43, 0.75) 60%,
            rgba(39, 174, 96, 0.65) 100%),
            url('https://images.unsplash.com/photo-1489493585363-d69421e0edd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        position: relative;
        color: white;
        padding: 120px 0; /* Reduced padding */
        text-align: center;
        overflow: hidden;
    }

    .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
    }

    .hero-section::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 50px; /* Reduced height */
        background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="%23ffffff" opacity="1"></path></svg>');
        background-size: cover;
        z-index: 2;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 700px; /* Reduced width */
        margin: 0 auto;
    }

    .hero-title {
        font-size: 3.5rem; /* Reduced font size */
        font-weight: 800;
        margin-bottom: 1rem; /* Reduced margin */
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6); /* Reduced shadow */
        position: relative;
        display: inline-block;
        animation: fadeInDown 0.8s ease-out; /* Faster animation */
    }

    .hero-title .text-highlight {
        color: #fff;
        position: relative;
        display: inline-block;
    }

    .hero-title .text-highlight::after {
        content: '';
        position: absolute;
        width: 0;
        height: 4px; /* Reduced height */
        background: linear-gradient(to right, #c0392b, #27ae60);
        bottom: -8px; /* Reduced bottom */
        left: 0;
        border-radius: 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Reduced shadow */
        animation: expandWidth 1s ease-out forwards; /* Faster animation */
        animation-delay: 0.4s;
    }

    @keyframes expandWidth {
        0% { width: 0; }
        100% { width: 100%; }
    }

    @keyframes fadeInDown {
        0% {
            opacity: 0;
            transform: translateY(-20px); /* Reduced distance */
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInUp {
        0% {
            opacity: 0;
            transform: translateY(20px); /* Reduced distance */
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero-description {
        font-size: 1.1rem; /* Reduced font size */
        max-width: 650px; /* Reduced width */
        margin: 0 auto 2rem auto; /* Reduced margin */
        line-height: 1.6; /* Reduced line height */
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4); /* Reduced shadow */
        animation: fadeInUp 0.8s ease-out; /* Faster animation */
        animation-delay: 0.2s;
        opacity: 0;
        animation-fill-mode: forwards;
    }

    .btn-action {
        background-color: #27ae60;
        color: white;
        padding: 0.6rem 1.2rem; /* Reduced padding */
        border-radius: 30px; /* Reduced border radius */
        font-weight: 600;
        font-size: 0.95rem; /* Reduced font size */
        text-decoration: none;
        display: inline-block;
        margin: 0 8px; /* Reduced margin */
        transition: all 0.3s ease;
    }

    .btn-action-outline {
        background-color: transparent;
        border: 2px solid white;
        color: white;
        padding: 0.6rem 1.2rem; /* Reduced padding */
        border-radius: 30px; /* Reduced border radius */
        font-weight: 600;
        font-size: 0.95rem; /* Reduced font size */
        text-decoration: none;
        display: inline-block;
        margin: 0 8px; /* Reduced margin */
        transition: all 0.3s ease;
    }

    .btn-action:hover, .btn-action-outline:hover {
        transform: translateY(-2px); /* Reduced transform */
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15); /* Reduced shadow */
        color: white;
        background-color: #219653;
    }

    .btn-action-outline:hover {
        background-color: rgba(39, 174, 96, 0.2);
        border-color: #27ae60;
    }

    /* Features Section */
    .features {
        background-color: #f9f9f9;
    }

    .feature-card {
        background-color: white;
        border-radius: 8px; /* Réduit de 10px à 8px */
        padding: 20px; /* Réduit de 30px à 20px */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Réduit l'ombre */
        transition: all 0.3s ease;
        height: 100%;
        text-align: center;
    }

    .feature-card:hover {
        transform: translateY(-5px); /* Réduit de -10px à -5px */
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); /* Réduit l'ombre */
    }

    .feature-icon {
        font-size: 2.2rem; /* Réduit de 2.5rem à 2.2rem */
        color: #27ae60;
        margin-bottom: 15px; /* Réduit de 20px à 15px */
    }

    .feature-title {
        font-size: 1.3rem; /* Réduit de 1.5rem à 1.3rem */
        font-weight: 600;
        margin-bottom: 10px; /* Réduit de 15px à 10px */
        color: #c0392b; /* Changed to red */
    }

    /* About Section */
    .about-img {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .about-img img {
        width: 100%;
        height: auto;
        transition: transform 0.5s ease;
    }

    .about-img:hover img {
        transform: scale(1.05);
    }

    /* Testimonials Section */
    .testimonials {
        background-color: #f9f9f9;
    }

    .testimonial-card {
        background-color: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin: 20px 10px;
        position: relative;
    }

    .testimonial-card::before {
        content: '\201C';
        font-size: 5rem;
        position: absolute;
        top: -20px;
        left: 10px;
        color: #f0f0f0;
        font-family: Georgia, serif;
    }

    .testimonial-text {
        font-style: italic;
        margin-bottom: 20px;
    }

    .testimonial-author {
        font-weight: 600;
        color: #1abc9c;
    }

    /* Events Section */
    .events {
        position: relative;
        background-color: #f9f9f9;
    }

    .events::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,20 L20,0 L40,20 L20,40 Z" fill="none" stroke="rgba(192, 57, 43, 0.1)" stroke-width="1"/></svg>');
        background-size: 40px 40px;
        opacity: 0.5;
        z-index: 0;
    }

    .events .container {
        position: relative;
        z-index: 1;
    }

    .event-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 30px;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        background-color: white;
        border: 1px solid rgba(0, 0, 0, 0.05);
        position: relative;
    }

    .event-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        box-shadow: 0 15px 35px rgba(192, 57, 43, 0.2);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        z-index: -1;
    }

    .event-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .event-card:hover::after {
        opacity: 1;
    }

    .event-img {
        height: 220px;
        overflow: hidden;
        position: relative;
        background-color: #f0f0f0;
    }

    .event-img::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        animation: loading 1.5s infinite;
        z-index: 0;
    }

    .event-img.image-loaded::before {
        animation: none;
        opacity: 0;
    }

    @keyframes loading {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .event-img::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
        z-index: 1;
    }

    .event-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
        position: relative;
        z-index: 1;
    }

    .event-card:hover .event-img img {
        transform: scale(1.1);
    }

    .event-content {
        padding: 25px;
        position: relative;
    }

    .event-date {
        font-size: 0.9rem;
        color: #777;
        margin-bottom: 12px;
        display: inline-block;
        padding: 5px 10px;
        background-color: rgba(39, 174, 96, 0.1);
        border-radius: 20px;
        font-weight: 500;
    }

    .event-date i {
        color: #27ae60;
    }

    .event-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 12px;
        color: #c0392b; /* Red color */
        position: relative;
        display: inline-block;
    }

    .event-title::after {
        content: '';
        position: absolute;
        width: 40%;
        height: 2px;
        background-color: #27ae60;
        bottom: -5px;
        left: 0;
        transition: width 0.3s ease;
    }

    .event-card:hover .event-title::after {
        width: 70%;
    }

    .event-description {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.6;
    }

    .event-action {
        margin-top: 15px;
    }

    .btn-event {
        background-color: #27ae60;
        color: white;
        padding: 8px 18px;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .btn-event::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background-color: #219653;
        transition: width 0.3s ease;
        z-index: -1;
    }

    .btn-event:hover {
        color: white;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 7px 15px rgba(39, 174, 96, 0.3);
    }

    .btn-event:hover::before {
        width: 100%;
    }

    .btn-event i {
        transition: transform 0.3s ease;
    }

    .btn-event:hover i {
        transform: translateX(3px);
    }

    /* Call to Action Section */
    .cta {
        background: linear-gradient(135deg,
            rgba(192, 57, 43, 0.9) 0%,
            rgba(192, 57, 43, 0.8) 60%,
            rgba(39, 174, 96, 0.7) 100%),
            url('https://images.unsplash.com/photo-1493246507139-91e8fad9978e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
        background-size: cover;
        background-position: center;
        color: white;
        text-align: center;
        padding: 60px 0; /* Reduced padding */
        position: relative;
    }

    .cta-title {
        font-size: 2rem; /* Reduced font size */
        font-weight: 700;
        margin-bottom: 1rem; /* Reduced margin */
        color: white;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3); /* Reduced shadow */
    }

    .cta-text {
        font-size: 1rem; /* Reduced font size */
        margin-bottom: 1.5rem; /* Reduced margin */
        max-width: 650px; /* Reduced width */
        margin-left: auto;
        margin-right: auto;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); /* Reduced shadow */
    }

    /* Stats Section */
    .stats {
        background-color: #f8f9fa;
        padding: 40px 0; /* Reduced padding */
        box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.03); /* Reduced shadow */
    }

    .stat-item {
        text-align: center;
        padding: 15px; /* Reduced padding */
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-3px); /* Reduced transform */
    }

    .stat-number {
        font-size: 2.5rem; /* Reduced font size */
        font-weight: 700;
        margin-bottom: 8px; /* Reduced margin */
        color: #1abc9c;
    }

    .stat-text {
        font-size: 0.95rem; /* Reduced font size */
        color: #666;
    }

    /* Partners Section */
    .partners {
        padding: 60px 0;
        background-color: #f9f9f9;
    }

    .partner-logo {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px 0;
        filter: grayscale(100%);
        opacity: 0.7;
        transition: all 0.3s ease;
    }

    .partner-logo:hover {
        filter: grayscale(0%);
        opacity: 1;
    }

    .partner-logo img {
        max-height: 100%;
        max-width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="moroccan-pattern"></div>
    <div class="container hero-content">
        <h1 class="hero-title">Bienvenue à <span class="text-highlight">Maroc Cultures</span></h1>
        <p class="hero-description">
            Découvrez et célébrez la richesse du patrimoine marocain au cœur du Canada. Notre association promeut la culture, les traditions et l'art marocains auprès de la diaspora et de tous les amateurs de diversité culturelle.
        </p>
        <div class="mt-3">
            <a href="#services" class="btn-action">
                <i class="fas fa-star"></i> Nos services
            </a>
            <a href="{% url 'register' %}" class="btn-action-outline">
                <i class="fas fa-user-plus"></i> Rejoignez-nous
            </a>
        </div>
    </div>
</div>

<!-- Stats Section -->
<section class="stats">
    <div class="container">
        <div class="row g-2">
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-text">Années d'expérience</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-text">Événements annuels</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-number">10K+</div>
                    <div class="stat-text">Membres actifs</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-text">Villes canadiennes</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="section features" id="services">
    <div class="container">
        <div class="text-center mb-4"> <!-- Réduit de mb-5 à mb-4 -->
            <h2 class="section-title">Nos Services</h2>
            <p class="lead">Nous proposons une variété de services pour promouvoir la culture marocaine</p>
        </div>

        <div class="row g-3"> <!-- Ajout de g-3 pour réduire l'espacement entre les cartes -->
            <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-theater-masks"></i>
                    </div>
                    <h3 class="feature-title">Événements Culturels</h3>
                    <p>Organisation de festivals, expositions et concerts mettant en valeur la richesse culturelle marocaine.</p>
                </div>
            </div>

            <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h3 class="feature-title">Soutien Communautaire</h3>
                    <p>Accompagnement et ressources pour faciliter l'intégration et l'épanouissement des Marocains au Canada.</p>
                </div>
            </div>

            <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="feature-title">Éducation & Formation</h3>
                    <p>Ateliers, cours et programmes éducatifs sur la langue, l'histoire et les traditions marocaines.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="section about" id="about">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="about-img">
                    <img src="{% static 'img/img_sv-768x659.png' %}" alt="Maroc Cultures">
                </div>
            </div>

            <div class="col-lg-6">
                <h2 class="section-title">À Propos de Nous</h2>
                <p class="lead mb-4">Maroc Cultures est une association culturelle et communautaire dédiée à la valorisation du patrimoine marocain et au soutien de la diaspora marocaine au Canada.</p>

                <p class="mb-4">Fondée avec la vision de créer des ponts entre les cultures, notre association œuvre pour :</p>

                <ul class="mb-4">
                    <li>Promouvoir la culture, l'art et les traditions marocaines</li>
                    <li>Accompagner l'intégration et l'épanouissement des Marocains au Canada</li>
                    <li>Renforcer les liens entre le Maroc et sa diaspora à travers des initiatives solidaires, culturelles et éducatives</li>
                </ul>

                <a href="{% url 'about' %}" class="btn btn-outline-success">En savoir plus</a>
            </div>
        </div>
    </div>
</section>

<!-- Events Section -->
<section class="section events">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Événements à Venir</h2>
            <div class="section-title-decoration" style="width: 80px; height: 3px; background: linear-gradient(to right, #c0392b, #27ae60); margin: 0 auto 20px; border-radius: 3px;"></div>
            <p class="lead">Rejoignez-nous pour nos prochains événements culturels</p>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="event-card">
                    <div class="event-img">
                        <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80" alt="Festival Mawazine">
                    </div>
                    <div class="event-content">
                        <p class="event-date"><i class="far fa-calendar-alt me-2"></i>15-23 Juin 2023</p>
                        <h3 class="event-title">Festival Mawazine</h3>
                        <p class="event-description">Le plus grand festival de musique d'Afrique, avec des artistes internationaux et marocains.</p>
                        <div class="event-action">
                            <a href="#" class="btn-event"><i class="fas fa-ticket-alt me-1"></i> Réserver</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="event-card">
                    <div class="event-img">
                        <img src="https://images.unsplash.com/photo-1578926375605-eaf7559b1458?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80" alt="Exposition d'Art Marocain">
                    </div>
                    <div class="event-content">
                        <p class="event-date"><i class="far fa-calendar-alt me-2"></i>10-20 Juillet 2023</p>
                        <h3 class="event-title">Exposition d'Art Marocain</h3>
                        <p class="event-description">Découvrez les œuvres d'artistes marocains contemporains et traditionnels.</p>
                        <div class="event-action">
                            <a href="#" class="btn-event"><i class="fas fa-info-circle me-1"></i> Plus d'infos</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="event-card">
                    <div class="event-img">
                        <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Festival Gastronomique">
                    </div>
                    <div class="event-content">
                        <p class="event-date"><i class="far fa-calendar-alt me-2"></i>5-7 Août 2023</p>
                        <h3 class="event-title">Festival Gastronomique</h3>
                        <p class="event-description">Savourez les délices de la cuisine marocaine lors de ce festival culinaire.</p>
                        <div class="event-action">
                            <a href="#" class="btn-event"><i class="fas fa-utensils me-1"></i> S'inscrire</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <a href="{% url 'events_list' %}" class="btn btn-outline-success btn-lg">
                <i class="fas fa-calendar-alt me-2"></i> Voir tous les événements
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="section testimonials">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Témoignages</h2>
            <p class="lead">Ce que disent nos membres et participants</p>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="testimonial-card">
                    <p class="testimonial-text">"Maroc Cultures m'a permis de rester connecté à mes racines tout en m'intégrant dans ma nouvelle vie au Canada. Les événements sont toujours riches en découvertes et en émotions."</p>
                    <p class="testimonial-author">- Mohammed L., Montréal</p>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="testimonial-card">
                    <p class="testimonial-text">"Grâce aux ateliers culturels, mes enfants apprennent la langue et les traditions marocaines dans un cadre ludique et enrichissant. Une vraie chance pour notre famille!"</p>
                    <p class="testimonial-author">- Fatima B., Toronto</p>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="testimonial-card">
                    <p class="testimonial-text">"Le Festival Mawazine organisé par Maroc Cultures est un événement incontournable qui met en valeur la diversité musicale marocaine et internationale. Une expérience unique!"</p>
                    <p class="testimonial-author">- Karim M., Ottawa</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Partners Section -->


<!-- Call to Action Section -->
<section class="cta">
    <div class="container">
        <h2 class="cta-title">Besoin d'informations?</h2>
        <p class="cta-text">Notre assistant virtuel est là pour répondre à toutes vos questions sur la culture marocaine, nos événements et nos services.</p>
        <a href="javascript:void(0);" class="btn-action" id="open-chat-btn">
            <i class="fas fa-comments"></i> Discuter avec notre assistant
        </a>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Open chat when the CTA button is clicked
    document.addEventListener('DOMContentLoaded', function() {
        const openChatBtn = document.getElementById('open-chat-btn');
        if (openChatBtn) {
            openChatBtn.addEventListener('click', function() {
                const chatToggleBtn = document.querySelector('.chat-toggle-btn');
                if (chatToggleBtn) {
                    chatToggleBtn.click();
                }
            });
        }

        // Handle image loading for event cards
        const eventImages = document.querySelectorAll('.event-img img');
        eventImages.forEach(img => {
            // When image is loaded, add a class to hide the loading animation
            img.addEventListener('load', function() {
                this.parentElement.classList.add('image-loaded');
            });

            // If image is already cached and loaded
            if (img.complete) {
                img.parentElement.classList.add('image-loaded');
            }
        });
    });
</script>
{% endblock %}