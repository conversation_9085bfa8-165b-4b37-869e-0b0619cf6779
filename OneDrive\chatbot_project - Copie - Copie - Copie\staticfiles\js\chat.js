$(document).ready(function() {
    // Faire défiler vers le bas pour voir les derniers messages
    function scrollToBottom() {
        var chatContainer = document.getElementById('chat-messages');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    scrollToBottom();
    
    // Gérer la soumission du formulaire
    $('#chat-form').submit(function(e) {
        e.preventDefault();
        
        var userInput = $('#user-input').val().trim();
        if (userInput === '') return;
        
        // Ajouter le message de l'utilisateur
        $('#chat-messages').append(`
            <div class="message user-message">
                <div class="message-content">
                    ${userInput}
                </div>
            </div>
        `);
        
        // Vider le champ de saisie
        $('#user-input').val('');
        
        // Faire défiler vers le bas
        scrollToBottom();
        
        // Envoyer la requête au serveur
        $.ajax({
            url: '/chat/',
            type: 'POST',
            data: {
                'user_input': userInput,
                'csrfmiddlewaretoken': $('input[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                // Ajouter la réponse du bot
                $('#chat-messages').append(`
                    <div class="message bot-message">
                        <div class="message-content">
                            ${response.bot_response}
                        </div>
                    </div>
                `);
                
                // Faire défiler vers le bas
                scrollToBottom();
            }
        });
    });
    
    // Permettre l'envoi avec la touche Entrée
    $('#user-input').keypress(function(e) {
        if (e.which === 13 && !e.shiftKey) {
            $('#chat-form').submit();
            return false;
        }
    });
});