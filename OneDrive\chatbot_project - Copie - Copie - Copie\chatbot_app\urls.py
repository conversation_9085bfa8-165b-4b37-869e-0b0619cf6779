from django.urls import path
from . import views

urlpatterns = [
    path('', views.landing, name='landing'),  # Landing page à la racine
    path('home/', views.home, name='home'),  # Page d'accueil après la landing page
    path('chat/', views.chat, name='chat'),
    path('about/', views.about, name='about'),
    path('contact/', views.contact, name='contact'),
    path('login/', views.login_view, name='login'),
    path('register/', views.register, name='register'),
    path('features/', views.features, name='features'),
    path('process_message/', views.process_message, name='process_message'),

    # Events URLs
    path('events/', views.events_list, name='events_list'),
    path('events/<int:event_id>/', views.event_detail, name='event_detail'),

    # Chat history URLs
    path('chat/history/', views.chat_history, name='chat_history'),
    path('chat/history/clear/', views.clear_chat_history, name='clear_chat_history'),

    # Admin Dashboard URLs (with non-obvious path for security)
    path('admin-maroc-cultures/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin-maroc-cultures/users/add/', views.admin_add_user, name='admin_add_user'),
    path('admin-maroc-cultures/users/<int:user_id>/delete/', views.admin_delete_user, name='admin_delete_user'),

    # Admin Events URLs
    path('admin-maroc-cultures/events/', views.admin_events_list, name='admin_events_list'),
    path('admin-maroc-cultures/events/add/', views.admin_add_event, name='admin_add_event'),
    path('admin-maroc-cultures/events/<int:event_id>/edit/', views.admin_edit_event, name='admin_edit_event'),
    path('admin-maroc-cultures/events/<int:event_id>/delete/', views.admin_delete_event, name='admin_delete_event'),
]