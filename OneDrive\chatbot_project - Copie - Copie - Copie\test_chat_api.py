#!/usr/bin/env python3
"""
Script pour tester l'API du chatbot avec urllib
"""

import urllib.request
import urllib.parse
import json

def test_chat_api():
    url = "http://127.0.0.1:8000/process_message/"

    # Test avec différents messages
    test_messages = [
        'bonjour',
        'services',
        'mawazine',
        'culture marocaine',
        'test message inconnu'
    ]

    for message in test_messages:
        print(f"\n{'='*50}")
        print(f"🧪 Test avec le message: '{message}'")
        print(f"{'='*50}")

        # Données à envoyer
        data = {
            'message': message
        }

        try:
            print(f"🚀 Test de l'API chatbot...")
            print(f"URL: {url}")
            print(f"Message: {data['message']}")

            # Encoder les données
            data_encoded = urllib.parse.urlencode(data).encode('utf-8')

            # Créer la requête
            req = urllib.request.Request(url, data=data_encoded, method='POST')
            req.add_header('Content-Type', 'application/x-www-form-urlencoded')

            # Envoyer la requête POST
            with urllib.request.urlopen(req) as response:
                status_code = response.getcode()
                response_text = response.read().decode('utf-8')

                print(f"📊 Status Code: {status_code}")

                if status_code == 200:
                    try:
                        json_response = json.loads(response_text)
                        print(f"✅ Réponse: {json_response['response']}")
                        print(f"🆔 Conversation ID: {json_response['conversation_id']}")
                    except:
                        print(f"⚠️ Réponse non-JSON: {response_text}")
                else:
                    print(f"❌ Erreur HTTP: {status_code}")
                    print(f"📝 Réponse: {response_text}")

        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_chat_api()
