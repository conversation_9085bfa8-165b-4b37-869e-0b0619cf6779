#!/usr/bin/env python3
"""
Manually insert migration records to fake that migrations have been applied
"""

import pymysql
from datetime import datetime

def fake_migrations():
    """Insert migration records manually"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🔄 Inserting fake migration records...")
        
        # Get current timestamp
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        
        # List of migrations to fake
        migrations = [
            ('contenttypes', '0001_initial'),
            ('auth', '0001_initial'),
            ('admin', '0001_initial'),
            ('admin', '0002_logentry_remove_auto_add'),
            ('admin', '0003_logentry_add_action_flag_choices'),
            ('contenttypes', '0002_remove_content_type_name'),
            ('auth', '0002_alter_permission_name_max_length'),
            ('auth', '0003_alter_user_email_max_length'),
            ('auth', '0004_alter_user_username_opts'),
            ('auth', '0005_alter_user_last_login_null'),
            ('auth', '0006_require_contenttypes_0002'),
            ('auth', '0007_alter_validators_add_error_messages'),
            ('auth', '0008_alter_user_username_max_length'),
            ('auth', '0009_alter_user_last_name_max_length'),
            ('auth', '0010_alter_group_name_max_length'),
            ('auth', '0011_update_proxy_permissions'),
            ('auth', '0012_alter_user_first_name_max_length'),
            ('sessions', '0001_initial'),
            ('chatbot_app', '0001_initial'),
        ]
        
        # Insert migration records
        for app, name in migrations:
            try:
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, %s)",
                    (app, name, now)
                )
                print(f"  ✅ {app}.{name}")
            except pymysql.IntegrityError:
                print(f"  ⚠️  {app}.{name} (already exists)")
        
        # Insert content types
        content_types = [
            ('admin', 'logentry'),
            ('auth', 'permission'),
            ('auth', 'group'),
            ('auth', 'user'),
            ('contenttypes', 'contenttype'),
            ('sessions', 'session'),
            ('chatbot_app', 'faq'),
            ('chatbot_app', 'user'),
            ('chatbot_app', 'admin'),
            ('chatbot_app', 'chatbot'),
            ('chatbot_app', 'conversation'),
            ('chatbot_app', 'message'),
            ('chatbot_app', 'event'),
        ]
        
        print("\n🔄 Inserting content types...")
        for app_label, model in content_types:
            try:
                cursor.execute(
                    "INSERT INTO django_content_type (app_label, model) VALUES (%s, %s)",
                    (app_label, model)
                )
                print(f"  ✅ {app_label}.{model}")
            except pymysql.IntegrityError:
                print(f"  ⚠️  {app_label}.{model} (already exists)")
        
        connection.commit()
        print("\n✅ All migration records inserted successfully!")
        
        # Verify
        cursor.execute("SELECT COUNT(*) FROM django_migrations")
        migration_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM django_content_type")
        content_type_count = cursor.fetchone()[0]
        
        print(f"📊 Migration records: {migration_count}")
        print(f"📊 Content types: {content_type_count}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    fake_migrations()
