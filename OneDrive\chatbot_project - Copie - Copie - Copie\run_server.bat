@echo off
echo 🇲🇦 Maroc Cultures - Démarrage du serveur Django avec Mistral AI
echo ================================================================

cd /d "%~dp0"
echo Répertoire actuel: %CD%

echo.
echo 🔧 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

echo.
echo 🚀 Démarrage du serveur Django...
echo Serveur accessible sur: http://127.0.0.1:8000/
echo.
echo 📝 Pages disponibles:
echo   - Page d'accueil: http://127.0.0.1:8000/home/
echo   - Chat Mistral AI: http://127.0.0.1:8000/chat/
echo   - Historique: http://127.0.0.1:8000/chat/history/
echo   - Admin: http://127.0.0.1:8000/admin-maroc-cultures/dashboard/
echo.
echo ⚠️  Appuyez sur Ctrl+C pour arrêter le serveur
echo.

python manage.py runserver

echo.
echo 👋 Serveur arrêté. Au revoir !
pause
