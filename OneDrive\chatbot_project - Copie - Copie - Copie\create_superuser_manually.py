#!/usr/bin/env python3
"""
Create superuser manually in MySQL database
"""

import pymysql
from datetime import datetime
from django.contrib.auth.hashers import make_password

def create_superuser_manually():
    """Create superuser manually in MySQL"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🔄 Creating superuser manually...")
        
        # User details
        username = 'admin'
        email = '<EMAIL>'
        password = 'admin123'
        
        # Hash the password using Django's method
        hashed_password = make_password(password)
        
        # Get current timestamp
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        
        # Insert superuser
        cursor.execute("""
            INSERT INTO auth_user (
                username, email, password, first_name, last_name,
                is_superuser, is_staff, is_active, date_joined, last_login
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            username, email, hashed_password, '', '',
            1, 1, 1, now, None
        ))
        
        connection.commit()
        print(f"✅ Superuser '{username}' created successfully!")
        print(f"📧 Email: {email}")
        print(f"🔑 Password: {password}")
        
        # Verify
        cursor.execute("SELECT id, username, email, is_superuser FROM auth_user WHERE username = %s", (username,))
        user = cursor.fetchone()
        
        if user:
            print(f"📊 User ID: {user[0]}")
            print(f"📊 Username: {user[1]}")
            print(f"📊 Email: {user[2]}")
            print(f"📊 Is Superuser: {'Yes' if user[3] else 'No'}")
        
    except pymysql.IntegrityError as e:
        if "Duplicate entry" in str(e):
            print("⚠️  Superuser 'admin' already exists!")
            # Get existing user info
            cursor.execute("SELECT id, username, email, is_superuser FROM auth_user WHERE username = 'admin'")
            user = cursor.fetchone()
            if user:
                print(f"📊 Existing User ID: {user[0]}")
                print(f"📊 Username: {user[1]}")
                print(f"📊 Email: {user[2]}")
                print(f"📊 Is Superuser: {'Yes' if user[3] else 'No'}")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    # We need to set up Django to use the password hasher
    import os
    import sys
    import django
    
    # Add project to path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    # Configure Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
    django.setup()
    
    create_superuser_manually()
