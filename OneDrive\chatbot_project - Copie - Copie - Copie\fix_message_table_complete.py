#!/usr/bin/env python
"""
Script pour corriger complètement la table Message
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def fix_message_table_complete():
    """Corrige complètement la structure de la table Message"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Correction complète de la table 'chatbot_app_message'...")
        
        # Afficher la structure actuelle
        cursor.execute("DESCRIBE chatbot_app_message")
        columns = cursor.fetchall()
        
        print("\n📋 Structure actuelle:")
        print("Column Name | Type | Null | Key | Default | Extra")
        print("-" * 60)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
        
        column_names = [col[0] for col in columns]
        
        # Supprimer la colonne user_id si elle existe (elle n'est pas nécessaire)
        if 'user_id' in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message DROP COLUMN user_id")
                print("✅ Colonne 'user_id' supprimée")
            except Exception as e:
                print(f"⚠️ Erreur lors de la suppression de 'user_id': {e}")
        
        # Ajouter la colonne 'content' si elle n'existe pas
        if 'content' not in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `content` LONGTEXT NOT NULL")
                print("✅ Colonne 'content' ajoutée")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'ajout de 'content': {e}")
        
        # Ajouter la colonne 'role' si elle n'existe pas
        if 'role' not in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `role` VARCHAR(10) NOT NULL DEFAULT 'user'")
                print("✅ Colonne 'role' ajoutée")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'ajout de 'role': {e}")
        
        # Ajouter la colonne 'time' si elle n'existe pas
        if 'time' not in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `time` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6)")
                print("✅ Colonne 'time' ajoutée")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'ajout de 'time': {e}")
        
        # Ajouter la colonne 'conversation_id' si elle n'existe pas
        if 'conversation_id' not in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `conversation_id` INT(11) NULL")
                print("✅ Colonne 'conversation_id' ajoutée")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'ajout de 'conversation_id': {e}")
        
        # Ajouter la colonne 'auth_user_id' si elle n'existe pas
        if 'auth_user_id' not in column_names:
            try:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `auth_user_id` INT(11) NULL")
                print("✅ Colonne 'auth_user_id' ajoutée")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'ajout de 'auth_user_id': {e}")
        
        # Valider les changements
        conn.commit()
        print("\n✅ Toutes les corrections ont été appliquées!")
        
        # Afficher la nouvelle structure
        cursor.execute("DESCRIBE chatbot_app_message")
        columns = cursor.fetchall()
        
        print("\n📋 Nouvelle structure de 'chatbot_app_message':")
        print("Column Name | Type | Null | Key | Default | Extra")
        print("-" * 60)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_message_table_complete()
