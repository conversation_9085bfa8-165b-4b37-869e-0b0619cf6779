@echo off
echo 🔧 Application des migrations Django - Maroc Cultures
echo ================================================

echo.
echo 📋 Étape 1: Activation de l'environnement virtuel...
call venv\Scripts\activate

echo.
echo 📋 Étape 2: Vérification de l'état des migrations...
python manage.py showmigrations chatbot_app

echo.
echo 📋 Étape 3: Application des migrations...
python manage.py migrate

echo.
echo 📋 Étape 4: Vérification finale...
python manage.py showmigrations chatbot_app

echo.
echo ✅ Migrations appliquées avec succès !
echo.
echo 🚀 Prochaines étapes :
echo    1. Redémarrer le serveur Django
echo    2. Tester l'interface admin
echo    3. Vérifier que l'erreur a disparu
echo.
echo 💡 Pour redémarrer le serveur :
echo    python manage.py runserver
echo.
pause
