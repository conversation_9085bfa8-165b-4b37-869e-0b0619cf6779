#!/usr/bin/env python3
"""
Create MySQL tables manually to avoid RETURNING clause issue
"""

import pymysql

def create_tables():
    """Create all necessary tables manually"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🔄 Creating Django tables manually...")
        
        # Django migrations table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS django_migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            app VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            applied DATETIME(6) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django content types
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS django_content_type (
            id INT AUTO_INCREMENT PRIMARY KEY,
            app_label VARCHAR(100) NOT NULL,
            model VARCHAR(100) NOT NULL,
            UNIQUE KEY django_content_type_app_label_model_76bd3d3b_uniq (app_label, model)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth permission
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_permission (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            content_type_id INT NOT NULL,
            codename VARCHAR(100) NOT NULL,
            UNIQUE KEY auth_permission_content_type_id_codename_01ab375a_uniq (content_type_id, codename),
            KEY auth_permission_content_type_id_2f476e4b (content_type_id),
            CONSTRAINT auth_permission_content_type_id_2f476e4b_fk_django_co FOREIGN KEY (content_type_id) REFERENCES django_content_type (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth group
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_group (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(150) NOT NULL UNIQUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth group permissions
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_group_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            group_id INT NOT NULL,
            permission_id INT NOT NULL,
            UNIQUE KEY auth_group_permissions_group_id_permission_id_0cd325b0_uniq (group_id, permission_id),
            KEY auth_group_permissions_group_id_b120cbf9 (group_id),
            KEY auth_group_permissions_permission_id_84c5c92e (permission_id),
            CONSTRAINT auth_group_permissions_group_id_b120cbf9_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES auth_group (id),
            CONSTRAINT auth_group_permissions_permission_id_84c5c92e_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES auth_permission (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth user
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            password VARCHAR(128) NOT NULL,
            last_login DATETIME(6),
            is_superuser TINYINT(1) NOT NULL,
            username VARCHAR(150) NOT NULL UNIQUE,
            first_name VARCHAR(150) NOT NULL,
            last_name VARCHAR(150) NOT NULL,
            email VARCHAR(254) NOT NULL,
            is_staff TINYINT(1) NOT NULL,
            is_active TINYINT(1) NOT NULL,
            date_joined DATETIME(6) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth user groups
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_user_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            group_id INT NOT NULL,
            UNIQUE KEY auth_user_groups_user_id_group_id_94350c0c_uniq (user_id, group_id),
            KEY auth_user_groups_user_id_6a12ed8b (user_id),
            KEY auth_user_groups_group_id_97559544 (group_id),
            CONSTRAINT auth_user_groups_user_id_6a12ed8b_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES auth_user (id),
            CONSTRAINT auth_user_groups_group_id_97559544_fk_auth_group_id FOREIGN KEY (group_id) REFERENCES auth_group (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django auth user permissions
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS auth_user_user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            permission_id INT NOT NULL,
            UNIQUE KEY auth_user_user_permissions_user_id_permission_id_14a6b632_uniq (user_id, permission_id),
            KEY auth_user_user_permissions_user_id_a95ead1b (user_id),
            KEY auth_user_user_permissions_permission_id_1fbb5f2c (permission_id),
            CONSTRAINT auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES auth_user (id),
            CONSTRAINT auth_user_user_permissions_permission_id_1fbb5f2c_fk_auth_perm FOREIGN KEY (permission_id) REFERENCES auth_permission (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django sessions
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS django_session (
            session_key VARCHAR(40) NOT NULL PRIMARY KEY,
            session_data LONGTEXT NOT NULL,
            expire_date DATETIME(6) NOT NULL,
            KEY django_session_expire_date_a5c62663 (expire_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Django admin log
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS django_admin_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            action_time DATETIME(6) NOT NULL,
            object_id LONGTEXT,
            object_repr VARCHAR(200) NOT NULL,
            action_flag SMALLINT UNSIGNED NOT NULL,
            change_message LONGTEXT NOT NULL,
            content_type_id INT,
            user_id INT NOT NULL,
            KEY django_admin_log_content_type_id_c4bce8eb (content_type_id),
            KEY django_admin_log_user_id_c564eba6 (user_id),
            CONSTRAINT django_admin_log_content_type_id_c4bce8eb_fk_django_co FOREIGN KEY (content_type_id) REFERENCES django_content_type (id),
            CONSTRAINT django_admin_log_user_id_c564eba6_fk_auth_user_id FOREIGN KEY (user_id) REFERENCES auth_user (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Chatbot app tables
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_faq (
            id INT AUTO_INCREMENT PRIMARY KEY,
            question LONGTEXT NOT NULL,
            answer LONGTEXT NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(150) NOT NULL UNIQUE,
            email VARCHAR(254) NOT NULL,
            password VARCHAR(128) NOT NULL,
            privilege VARCHAR(20) NOT NULL DEFAULT 'user'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_admin (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(150) NOT NULL UNIQUE,
            email VARCHAR(254) NOT NULL,
            password VARCHAR(128) NOT NULL,
            privilege VARCHAR(20) NOT NULL DEFAULT 'admin'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_chatbot (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description LONGTEXT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_conversation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            created_at DATETIME(6) NOT NULL,
            KEY chatbot_app_conversation_user_id_fk (user_id),
            CONSTRAINT chatbot_app_conversation_user_id_fk FOREIGN KEY (user_id) REFERENCES chatbot_app_user (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_message (
            id INT AUTO_INCREMENT PRIMARY KEY,
            conversation_id INT NOT NULL,
            content LONGTEXT NOT NULL,
            is_user TINYINT(1) NOT NULL,
            timestamp DATETIME(6) NOT NULL,
            KEY chatbot_app_message_conversation_id_fk (conversation_id),
            CONSTRAINT chatbot_app_message_conversation_id_fk FOREIGN KEY (conversation_id) REFERENCES chatbot_app_conversation (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatbot_app_event (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description LONGTEXT NOT NULL,
            date DATETIME(6) NOT NULL,
            location VARCHAR(200) NOT NULL,
            image VARCHAR(100)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        connection.commit()
        print("✅ All tables created successfully!")
        
        # Show created tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"📋 Created {len(tables)} tables:")
        for table in tables:
            print(f"  - {table[0]}")
            
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    create_tables()
