#!/usr/bin/env python3
"""
Script de configuration MySQL pour le projet Chatbot Maroc Cultures
Ce script aide à configurer la base de données MySQL et à effectuer les migrations.
"""

import os
import sys
import subprocess
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

def load_environment():
    """Charge les variables d'environnement depuis le fichier .env"""
    load_dotenv()
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '3306'),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', ''),
        'database': os.getenv('DB_NAME', 'chatbot_maroc_cultures')
    }

def test_mysql_connection(config):
    """Teste la connexion MySQL"""
    try:
        # Test de connexion sans spécifier la base de données
        connection = mysql.connector.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password']
        )
        
        if connection.is_connected():
            print("✅ Connexion MySQL réussie!")
            connection.close()
            return True
    except Error as e:
        print(f"❌ Erreur de connexion MySQL: {e}")
        return False

def create_database(config):
    """Crée la base de données si elle n'existe pas"""
    try:
        connection = mysql.connector.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password']
        )
        
        cursor = connection.cursor()
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ Base de données '{config['database']}' créée ou existe déjà")
        
        cursor.close()
        connection.close()
        return True
    except Error as e:
        print(f"❌ Erreur lors de la création de la base de données: {e}")
        return False

def run_django_migrations():
    """Exécute les migrations Django"""
    try:
        print("🔄 Exécution des migrations Django...")
        subprocess.run([sys.executable, "manage.py", "makemigrations"], check=True)
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True)
        print("✅ Migrations Django terminées avec succès!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors des migrations: {e}")
        return False

def create_superuser():
    """Propose de créer un superutilisateur Django"""
    response = input("Voulez-vous créer un superutilisateur Django? (y/n): ")
    if response.lower() in ['y', 'yes', 'oui']:
        try:
            subprocess.run([sys.executable, "manage.py", "createsuperuser"], check=True)
            print("✅ Superutilisateur créé avec succès!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur lors de la création du superutilisateur: {e}")

def main():
    """Fonction principale"""
    print("🚀 Configuration MySQL pour Chatbot Maroc Cultures")
    print("=" * 50)
    
    # Charger la configuration
    config = load_environment()
    
    print(f"Configuration détectée:")
    print(f"  Host: {config['host']}")
    print(f"  Port: {config['port']}")
    print(f"  User: {config['user']}")
    print(f"  Database: {config['database']}")
    print()
    
    # Tester la connexion MySQL
    if not test_mysql_connection(config):
        print("❌ Impossible de se connecter à MySQL. Vérifiez:")
        print("  1. MySQL est installé et en cours d'exécution")
        print("  2. Les paramètres dans le fichier .env sont corrects")
        print("  3. L'utilisateur MySQL a les bonnes permissions")
        return False
    
    # Créer la base de données
    if not create_database(config):
        return False
    
    # Exécuter les migrations
    if not run_django_migrations():
        return False
    
    # Proposer de créer un superutilisateur
    create_superuser()
    
    print("\n🎉 Configuration MySQL terminée avec succès!")
    print("Vous pouvez maintenant démarrer le serveur Django avec:")
    print("  python manage.py runserver")
    
    return True

if __name__ == "__main__":
    main()
