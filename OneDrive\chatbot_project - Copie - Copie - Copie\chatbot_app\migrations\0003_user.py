# Generated by Django 5.2.1 on 2025-05-21 09:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot_app', '0002_alter_faq_options_alter_faq_answer_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('email', models.Email<PERSON>ield(max_length=254, unique=True)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-date_joined'],
            },
        ),
    ]
