#!/usr/bin/env python
"""
Script pour ajouter des événements d'exemple avec la nouvelle structure
"""
import os
import sys
import django
from datetime import date, time

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def add_sample_events():
    """Ajoute des événements d'exemple"""
    try:
        # Supprimer les événements existants
        Event.objects.all().delete()
        print("🗑️ Événements existants supprimés")
        
        # Événement 1: Festival Marocain de Montréal
        event1 = Event.objects.create(
            title="Festival Marocain de Montréal",
            description="Un festival célébrant la riche culture marocaine avec de la musique traditionnelle, de la danse, de l'artisanat et de la cuisine authentique. Venez découvrir les traditions du Maroc dans une ambiance festive et familiale.",
            date_start=date(2025, 7, 15),
            date_end=date(2025, 7, 17),
            time=time(10, 0),
            location="Parc <PERSON>-Drapeau, Montréal",
            image_url="https://example.com/festival-marocain.jpg",
            registration_url="https://example.com/inscription-festival"
        )
        print(f"✅ Événement créé: {event1.title}")
        
        # Événement 2: Soirée Culturelle Marocaine
        event2 = Event.objects.create(
            title="Soirée Culturelle Marocaine",
            description="Une soirée dédiée à la découverte de la culture marocaine à travers des spectacles de musique andalouse, des contes traditionnels et une dégustation de thé à la menthe.",
            date_start=date(2025, 6, 20),
            time=time(19, 0),
            location="Centre Culturel de Montréal",
            image_url="https://example.com/soiree-culturelle.jpg",
            registration_url="https://example.com/inscription-soiree"
        )
        print(f"✅ Événement créé: {event2.title}")
        
        # Événement 3: Atelier de Cuisine Marocaine
        event3 = Event.objects.create(
            title="Atelier de Cuisine Marocaine",
            description="Apprenez à préparer des plats traditionnels marocains comme le tajine, le couscous et les pâtisseries orientales. Un chef expérimenté vous guidera dans la préparation de ces délices.",
            date_start=date(2025, 6, 10),
            time=time(14, 0),
            location="École de Cuisine Montréal",
            image_url="https://example.com/atelier-cuisine.jpg",
            registration_url="https://example.com/inscription-cuisine"
        )
        print(f"✅ Événement créé: {event3.title}")
        
        print(f"\n🎉 {Event.objects.count()} événements ajoutés avec succès!")
        
        # Afficher tous les événements
        print("\n📅 Liste des événements:")
        for event in Event.objects.all().order_by('date_start'):
            print(f"- {event.title} ({event.date_start})")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_sample_events()
