#!/usr/bin/env python3
"""
Complete the remaining fake migrations
"""

import pymysql
from datetime import datetime

def complete_fake_migrations():
    """Insert remaining migration records manually"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🔄 Inserting remaining fake migration records...")
        
        # Get current timestamp
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        
        # Remaining chatbot_app migrations
        remaining_migrations = [
            ('chatbot_app', '0002_alter_faq_options_alter_faq_answer_and_more'),
            ('chatbot_app', '0003_user'),
            ('chatbot_app', '0004_chatmessage'),
            ('chatbot_app', '0005_admin_chatbot_alter_user_options_and_more'),
            ('chatbot_app', '0006_remove_user_img'),
            ('chatbot_app', '0007_event'),
        ]
        
        # Insert migration records
        for app, name in remaining_migrations:
            try:
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, %s)",
                    (app, name, now)
                )
                print(f"  ✅ {app}.{name}")
            except pymysql.IntegrityError:
                print(f"  ⚠️  {app}.{name} (already exists)")
        
        connection.commit()
        print("\n✅ All remaining migration records inserted successfully!")
        
        # Verify
        cursor.execute("SELECT COUNT(*) FROM django_migrations WHERE app = 'chatbot_app'")
        chatbot_migrations = cursor.fetchone()[0]
        
        print(f"📊 Chatbot app migrations: {chatbot_migrations}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    complete_fake_migrations()
