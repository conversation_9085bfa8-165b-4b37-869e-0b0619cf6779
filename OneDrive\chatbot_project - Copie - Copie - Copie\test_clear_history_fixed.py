#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import django
from django.test import Client
from django.contrib.sessions.models import Session
import pymysql

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

def test_clear_history():
    """Test de la fonction de suppression d'historique corrigée"""
    
    print("🧪 Test de la suppression d'historique...")
    
    # Créer un client de test
    client = Client()
    
    # 1. Vérifier l'état initial
    print(f"\n📊 Étape 1: État initial de la base de données")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        # Compter les données avant
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
        hist_before = cursor.fetchone()[0]
        
        print(f"   - Conversations: {conv_before}")
        print(f"   - Messages: {msg_before}")
        print(f"   - Historiques: {hist_before}")
        
        # Vérifier les sessions existantes
        cursor.execute("SELECT DISTINCT session_id FROM chatbot_app_conversation WHERE session_id IS NOT NULL")
        sessions = cursor.fetchall()
        print(f"   - Sessions actives: {len(sessions)}")
        
        if sessions:
            test_session = sessions[0][0]
            print(f"   - Session de test: {test_session}")
            
            # Compter les données pour cette session
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation WHERE session_id = %s", (test_session,))
            session_conv = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique WHERE session_id = %s", (test_session,))
            session_hist = cursor.fetchone()[0]
            
            print(f"   - Conversations pour cette session: {session_conv}")
            print(f"   - Historiques pour cette session: {session_hist}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    # 2. Simuler une session utilisateur
    print(f"\n🔧 Étape 2: Simulation d'une session utilisateur")
    
    if sessions:
        # Utiliser une session existante
        session = client.session
        session['chat_session_id'] = test_session
        session.save()
        print(f"   ✅ Session configurée: {test_session}")
    else:
        print(f"   ❌ Aucune session trouvée pour le test")
        return False
    
    # 3. Tester la suppression
    print(f"\n🗑️ Étape 3: Test de suppression")
    
    try:
        # Faire une requête POST vers l'URL de suppression
        response = client.post('/chat/history/clear/')
        
        print(f"   - Code de réponse: {response.status_code}")
        
        if response.status_code == 302:  # Redirection après succès
            print(f"   ✅ Requête de suppression acceptée")
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"   Contenu: {response.content.decode()[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception lors de la requête: {e}")
        return False
    
    # 4. Vérifier les résultats
    print(f"\n🔍 Étape 4: Vérification des résultats")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        # Compter les données après
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
        hist_after = cursor.fetchone()[0]
        
        print(f"   - Conversations après: {conv_after} (avant: {conv_before})")
        print(f"   - Messages après: {msg_after} (avant: {msg_before})")
        print(f"   - Historiques après: {hist_after} (avant: {hist_before})")
        
        # Vérifier spécifiquement pour la session de test
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation WHERE session_id = %s", (test_session,))
        session_conv_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique WHERE session_id = %s", (test_session,))
        session_hist_after = cursor.fetchone()[0]
        
        print(f"   - Conversations session après: {session_conv_after} (avant: {session_conv})")
        print(f"   - Historiques session après: {session_hist_after} (avant: {session_hist})")
        
        # Évaluer le succès
        if session_conv_after == 0 and session_hist_after == 0:
            print(f"   ✅ Suppression réussie pour la session!")
        elif session_conv_after < session_conv or session_hist_after < session_hist:
            print(f"   ⚠️ Suppression partielle")
        else:
            print(f"   ❌ Aucune suppression détectée")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur vérification: {e}")
        return False
    
    print(f"\n✅ Test terminé")
    return True

if __name__ == "__main__":
    test_clear_history()
