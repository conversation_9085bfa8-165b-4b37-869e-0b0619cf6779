"""
Custom MySQL backend to handle MariaDB compatibility issues with Django 5.2.1
This fixes the RETURNING clause issue with MariaDB 10.4.32
"""

from django.db.backends.mysql.base import DatabaseWrapper as MySQLDatabaseWrapper
from django.db.backends.mysql.operations import DatabaseOperations as MySQLDatabaseOperations
from django.db.backends.mysql.features import DatabaseFeatures as MySQLDatabaseFeatures


class DatabaseFeatures(MySQLDatabaseFeatures):
    """Custom database features to disable RETURNING clause for MariaDB"""

    # Disable RETURNING clause support for MariaDB compatibility
    can_return_columns_from_insert = False
    can_return_rows_from_bulk_insert = False
    supports_over_clause = False
    supports_frame_range_fixed_distance = False


class DatabaseOperations(MySQLDatabaseOperations):
    """Custom database operations to handle MariaDB compatibility"""

    def bulk_insert_sql(self, fields, placeholder_rows):
        """Override to avoid RETURNING clause in bulk inserts"""
        placeholder_rows_sql = (", ".join(row) for row in placeholder_rows)
        values_sql = ", ".join("(%s)" % sql for sql in placeholder_rows_sql)
        return "VALUES " + values_sql

    def insert_statement(self, on_conflict=None, update_fields=None, unique_fields=None):
        """Override to avoid RETURNING clause in inserts"""
        if on_conflict == 'ignore':
            return "INSERT IGNORE INTO"
        elif on_conflict == 'update':
            return "INSERT INTO"
        return "INSERT INTO"


class DatabaseWrapper(MySQLDatabaseWrapper):
    """Custom MySQL database wrapper for MariaDB compatibility"""

    features_class = DatabaseFeatures
    ops_class = DatabaseOperations

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Override connection options for better MariaDB compatibility
        if 'OPTIONS' not in self.settings_dict:
            self.settings_dict['OPTIONS'] = {}

        # Ensure proper charset and SQL mode
        self.settings_dict['OPTIONS'].update({
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'isolation_level': None,
        })

    def get_connection_params(self):
        """Get connection parameters with MariaDB optimizations"""
        params = super().get_connection_params()

        # Add specific parameters for MariaDB compatibility
        params.update({
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True,
        })

        return params
