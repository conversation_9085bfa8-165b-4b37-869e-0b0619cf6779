#!/usr/bin/env python
"""
Script pour corriger toutes les tables du chatbot
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def fix_message_table():
    """Corrige la structure de la table Message"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Vérification de la table 'chatbot_app_message'...")
        
        # Vérifier si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_message'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ Table 'chatbot_app_message' existe")
            
            # Afficher la structure actuelle
            cursor.execute("DESCRIBE chatbot_app_message")
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]
            
            # Ajouter les colonnes manquantes
            if 'content' not in column_names:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `content` LONGTEXT NOT NULL")
                print("✅ Colonne 'content' ajoutée")
            
            if 'role' not in column_names:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `role` VARCHAR(10) NOT NULL DEFAULT 'user'")
                print("✅ Colonne 'role' ajoutée")
            
            if 'time' not in column_names:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `time` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6)")
                print("✅ Colonne 'time' ajoutée")
            
            if 'conversation_id' not in column_names:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `conversation_id` INT(11) NOT NULL")
                print("✅ Colonne 'conversation_id' ajoutée")
            
            if 'auth_user_id' not in column_names:
                cursor.execute("ALTER TABLE chatbot_app_message ADD COLUMN `auth_user_id` INT(11) NULL")
                print("✅ Colonne 'auth_user_id' ajoutée")
                
        else:
            print("❌ Table 'chatbot_app_message' n'existe pas")
            print("🔧 Création de la table...")
            
            create_table_sql = """
            CREATE TABLE `chatbot_app_message` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `content` longtext NOT NULL,
                `role` varchar(10) NOT NULL DEFAULT 'user',
                `time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                `conversation_id` int(11) NOT NULL,
                `auth_user_id` int(11) NULL,
                PRIMARY KEY (`id`),
                KEY `chatbot_app_message_conversation_id` (`conversation_id`),
                KEY `chatbot_app_message_auth_user_id` (`auth_user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            
            cursor.execute(create_table_sql)
            print("✅ Table 'chatbot_app_message' créée")
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur Message: {e}")

def fix_chatbot_table():
    """Corrige la structure de la table Chatbot"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Vérification de la table 'chatbot_app_chatbot'...")
        
        # Vérifier si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_chatbot'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Table 'chatbot_app_chatbot' n'existe pas")
            print("🔧 Création de la table...")
            
            create_table_sql = """
            CREATE TABLE `chatbot_app_chatbot` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `description` longtext,
                `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            
            cursor.execute(create_table_sql)
            print("✅ Table 'chatbot_app_chatbot' créée")
        else:
            print("✅ Table 'chatbot_app_chatbot' existe déjà")
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur Chatbot: {e}")

def fix_conversation_date_column():
    """Corrige la colonne date de la table Conversation"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Vérification de la colonne 'date' dans 'chatbot_app_conversation'...")
        
        # Vérifier la structure
        cursor.execute("DESCRIBE chatbot_app_conversation")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        if 'date' not in column_names:
            cursor.execute("ALTER TABLE chatbot_app_conversation ADD COLUMN `date` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6)")
            print("✅ Colonne 'date' ajoutée")
        else:
            print("✅ Colonne 'date' existe déjà")
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur date: {e}")

if __name__ == "__main__":
    print("🛠️ Correction de toutes les tables du chatbot")
    print("=" * 50)
    
    fix_conversation_date_column()
    fix_message_table()
    fix_chatbot_table()
    
    print("\n✅ Toutes les corrections terminées!")
