#!/usr/bin/env python3
"""
🔑 Générateur de Clés API pour Maroc Cultures
Génère automatiquement les clés de sécurité nécessaires
"""

import secrets
import string
import os
from datetime import datetime

def generate_secret_key(length=50):
    """Génère une clé secrète Django sécurisée"""
    chars = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(chars) for _ in range(length))

def generate_jwt_key(length=32):
    """Génère une clé JWT sécurisée"""
    return secrets.token_urlsafe(length)

def generate_encryption_key(length=32):
    """Génère une clé de chiffrement sécurisée"""
    return secrets.token_urlsafe(length)

def update_env_file():
    """Met à jour le fichier .env avec les nouvelles clés"""
    env_path = '.env'
    
    # G<PERSON>érer les clés
    django_secret = generate_secret_key()
    jwt_secret = generate_jwt_key()
    encryption_key = generate_encryption_key()
    
    print("🔑 Génération des clés de sécurité...")
    print("=" * 50)
    
    # Lire le fichier .env existant
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacer les clés existantes
        if 'SECRET_KEY=votre_cle_secrete_django_ici' in content:
            content = content.replace(
                'SECRET_KEY=votre_cle_secrete_django_ici',
                f'SECRET_KEY={django_secret}'
            )
            print("✅ Clé Django SECRET_KEY mise à jour")
        
        if 'JWT_SECRET_KEY=your_jwt_secret_key_here' in content:
            content = content.replace(
                'JWT_SECRET_KEY=your_jwt_secret_key_here',
                f'JWT_SECRET_KEY={jwt_secret}'
            )
            print("✅ Clé JWT_SECRET_KEY mise à jour")
        
        if 'ENCRYPTION_KEY=your_encryption_key_here' in content:
            content = content.replace(
                'ENCRYPTION_KEY=your_encryption_key_here',
                f'ENCRYPTION_KEY={encryption_key}'
            )
            print("✅ Clé ENCRYPTION_KEY mise à jour")
        
        # Sauvegarder le fichier mis à jour
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("\n🎉 Fichier .env mis à jour avec succès!")
    else:
        print("❌ Fichier .env non trouvé!")
        return
    
    # Afficher les clés générées
    print("\n📋 Clés générées:")
    print("-" * 30)
    print(f"Django SECRET_KEY: {django_secret[:20]}...")
    print(f"JWT_SECRET_KEY: {jwt_secret}")
    print(f"ENCRYPTION_KEY: {encryption_key}")
    
    # Créer un fichier de sauvegarde
    backup_filename = f"keys_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(backup_filename, 'w', encoding='utf-8') as f:
        f.write(f"# Clés générées le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"SECRET_KEY={django_secret}\n")
        f.write(f"JWT_SECRET_KEY={jwt_secret}\n")
        f.write(f"ENCRYPTION_KEY={encryption_key}\n")
    
    print(f"\n💾 Sauvegarde créée: {backup_filename}")
    print("\n⚠️  IMPORTANT:")
    print("- Gardez ces clés secrètes")
    print("- Ne les partagez jamais")
    print("- Ajoutez .env au .gitignore")

def display_api_status():
    """Affiche le statut des APIs configurées"""
    print("\n📊 Statut des APIs configurées:")
    print("=" * 40)
    
    env_path = '.env'
    if not os.path.exists(env_path):
        print("❌ Fichier .env non trouvé")
        return
    
    with open(env_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    apis = {
        'OpenWeather': 'OPENWEATHER_API_KEY=your_openweather_api_key_here',
        'Google Maps': 'GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here',
        'Gmail SMTP': 'EMAIL_HOST_USER=<EMAIL>',
        'Hugging Face': 'HUGGINGFACE_API_KEY=your_huggingface_api_key_here',
        'Firebase': 'FIREBASE_API_KEY=your_firebase_api_key_here',
        'Google Analytics': 'GOOGLE_ANALYTICS_ID=your_ga_tracking_id_here'
    }
    
    for api_name, placeholder in apis.items():
        if placeholder in content:
            print(f"⚠️  {api_name}: Non configuré")
        else:
            print(f"✅ {api_name}: Configuré")

def main():
    """Fonction principale"""
    print("🇲🇦 Maroc Cultures - Générateur de Clés API")
    print("=" * 50)
    
    choice = input("\nQue voulez-vous faire?\n"
                  "1. Générer les clés de sécurité\n"
                  "2. Afficher le statut des APIs\n"
                  "3. Les deux\n"
                  "Choix (1-3): ")
    
    if choice in ['1', '3']:
        update_env_file()
    
    if choice in ['2', '3']:
        display_api_status()
    
    print("\n🎯 Prochaines étapes:")
    print("1. Consultez API_KEYS_GUIDE.md pour obtenir les clés API")
    print("2. Remplacez les placeholders dans .env")
    print("3. Testez les fonctionnalités une par une")
    print("\n🚀 Bon développement!")

if __name__ == "__main__":
    main()
