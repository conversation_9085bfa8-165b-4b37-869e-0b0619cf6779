import pymysql
from datetime import datetime

def add_3_events_sql():
    """Ajoute 3 événements directement via SQL"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🎭 Ajout de 3 nouveaux événements culturels marocains...")
        print("=" * 60)
        
        # Supprimer les événements existants avec ces titres
        titles_to_delete = [
            'Festival Gnawa de Casablanca',
            'Exposition d\'Art Berbère Contemporain',
            'Soirée Gastronomique Marocaine'
        ]
        
        for title in titles_to_delete:
            cursor.execute("DELETE FROM chatbot_app_event WHERE title = %s", (title,))
        
        # Les 3 événements à ajouter
        events = [
            (
                'Festival Gnawa de Casablanca',
                'Découvrez la musique spirituelle Gnawa, patrimoine ancestral du Maroc. Concerts de maîtres musiciens, ateliers de percussion traditionnelle et cérémonies mystiques. Un voyage musical unique au cœur de la culture afro-marocaine.',
                '2025-08-10',
                '2025-08-12',
                '19:00:00',
                'Théâtre Mohammed V, Casablanca',
                'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800',
                'https://festival-gnawa-casablanca.ma/inscription',
                datetime.now(),
                datetime.now()
            ),
            (
                'Exposition d\'Art Berbère Contemporain',
                'Une exposition exceptionnelle mettant en valeur l\'art berbère moderne. Peintures, sculptures, bijoux traditionnels revisités par des artistes contemporains. Rencontres avec les artistes et ateliers de calligraphie tifinagh.',
                '2025-09-05',
                '2025-09-25',
                '10:00:00',
                'Galerie d\'Art Moderne, Rabat',
                'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
                'https://galerie-art-rabat.ma/expo-berbere',
                datetime.now(),
                datetime.now()
            ),
            (
                'Soirée Gastronomique Marocaine',
                'Une soirée dégustation exceptionnelle avec les meilleurs chefs marocains. Menu traditionnel revisité : tajines, couscous royal, pâtisseries orientales. Spectacle de danse orientale et musique andalouse en accompagnement.',
                '2025-10-15',
                '2025-10-15',
                '18:30:00',
                'Restaurant La Mamounia, Marrakech',
                'https://images.unsplash.com/photo-1544025162-d76694265947?w=800',
                'https://lamounia-marrakech.com/soiree-gastronomique',
                datetime.now(),
                datetime.now()
            )
        ]
        
        # Insérer les nouveaux événements
        insert_query = """
        INSERT INTO chatbot_app_event 
        (title, description, date_start, date_end, time, location, image_url, registration_url, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        created_count = 0
        for event in events:
            try:
                cursor.execute(insert_query, event)
                print(f"✅ Événement créé: {event[0]}")
                print(f"   📅 Date: {event[2]} - {event[3]}")
                print(f"   🕐 Heure: {event[4]}")
                print(f"   📍 Lieu: {event[5]}")
                print(f"   🖼️ Image: {event[6]}")
                print(f"   🔗 Inscription: {event[7]}")
                print("-" * 60)
                created_count += 1
            except Exception as e:
                print(f"❌ Erreur lors de la création de '{event[0]}': {e}")
        
        connection.commit()
        
        # Vérifier le nombre total d'événements
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_event")
        total_events = cursor.fetchone()[0]
        
        print(f"\n🎉 {created_count} événements créés avec succès!")
        print(f"📊 Total d'événements dans la base: {total_events}")
        
        # Afficher tous les événements
        cursor.execute("SELECT id, title, date_start, location FROM chatbot_app_event ORDER BY date_start")
        all_events = cursor.fetchall()
        
        if all_events:
            print(f"\n📋 Liste de tous les événements:")
            for event in all_events:
                print(f"   • ID: {event[0]} | {event[1]} | {event[2]} | {event[3]}")
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    add_3_events_sql()
