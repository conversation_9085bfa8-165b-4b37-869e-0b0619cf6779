from django.db.backends.mysql.base import DatabaseWrapper as MySQLDatabaseWrapper
from django.db.backends.mysql.features import DatabaseFeatures as MySQLDatabaseFeatures

class DatabaseFeatures(MySQLDatabaseFeatures):
    # Désactiver la fonctionnalité RETURNING qui n'est pas supportée par MariaDB 10.4
    can_return_columns_from_insert = False
    can_return_rows_from_bulk_insert = False
    has_insert_returning = False

class DatabaseWrapper(MySQLDatabaseWrapper):
    features_class = DatabaseFeatures
