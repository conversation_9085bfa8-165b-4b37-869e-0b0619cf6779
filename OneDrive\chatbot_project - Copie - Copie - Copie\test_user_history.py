#!/usr/bin/env python
"""
Test pour vérifier que l'historique est bien séparé par utilisateur
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
import json
import pymysql

def test_user_specific_history():
    """Test pour vérifier que chaque utilisateur a son propre historique"""
    print("👥 Test de l'historique spécifique par utilisateur")
    print("=" * 60)
    
    # Test 1: Créer des conversations avec différentes sessions
    print("\n📝 Étape 1: Création de conversations avec différentes sessions...")
    
    # Simuler deux utilisateurs différents avec des sessions différentes
    client1 = Client()
    client2 = Client()
    
    # Messages pour l'utilisateur 1
    messages_user1 = ["Bonjour utilisateur 1", "Comment ça va ?"]
    print(f"   👤 Utilisateur 1 - Envoi de {len(messages_user1)} messages:")
    
    for i, message in enumerate(messages_user1, 1):
        print(f"      Message {i}: '{message}'")
        response = client1.post('/process_message/', {'message': message})
        if response.status_code == 200:
            print(f"      ✅ Envoyé")
        else:
            print(f"      ❌ Erreur: {response.status_code}")
    
    # Messages pour l'utilisateur 2
    messages_user2 = ["Salut utilisateur 2", "Quoi de neuf ?", "Merci"]
    print(f"\n   👤 Utilisateur 2 - Envoi de {len(messages_user2)} messages:")
    
    for i, message in enumerate(messages_user2, 1):
        print(f"      Message {i}: '{message}'")
        response = client2.post('/process_message/', {'message': message})
        if response.status_code == 200:
            print(f"      ✅ Envoyé")
        else:
            print(f"      ❌ Erreur: {response.status_code}")
    
    # Test 2: Vérifier la base de données
    print(f"\n🔍 Étape 2: Vérification de la base de données...")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        # Compter les conversations
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count = cursor.fetchone()[0]
        print(f"   📊 Total conversations: {conv_count}")
        
        # Compter les messages
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count = cursor.fetchone()[0]
        print(f"   📊 Total messages: {msg_count}")
        
        # Compter les entrées d'historique
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
        hist_count = cursor.fetchone()[0]
        print(f"   📊 Total entrées historique: {hist_count}")
        
        # Afficher les sessions distinctes
        cursor.execute("SELECT DISTINCT session_id FROM chatbot_app_conversation WHERE session_id IS NOT NULL")
        sessions = cursor.fetchall()
        print(f"   📊 Sessions distinctes: {len(sessions)}")
        
        for i, (session_id,) in enumerate(sessions, 1):
            session_short = session_id[:8] + "..." if session_id else "N/A"
            print(f"      Session {i}: {session_short}")
            
            # Compter les conversations pour cette session
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation WHERE session_id = %s", (session_id,))
            session_conv_count = cursor.fetchone()[0]
            print(f"         Conversations: {session_conv_count}")
            
            # Compter les messages pour cette session
            cursor.execute("""
                SELECT COUNT(*) FROM chatbot_app_message m
                INNER JOIN chatbot_app_conversation c ON m.conversation_id = c.id
                WHERE c.session_id = %s
            """, (session_id,))
            session_msg_count = cursor.fetchone()[0]
            print(f"         Messages: {session_msg_count}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        return
    
    # Test 3: Vérifier l'historique pour chaque utilisateur
    print(f"\n🌐 Étape 3: Test des pages d'historique...")
    
    # Historique utilisateur 1
    print(f"   👤 Historique utilisateur 1:")
    try:
        response1 = client1.get('/chat/history/')
        if response1.status_code == 200:
            print(f"      ✅ Page accessible (Code: {response1.status_code})")
            content1 = response1.content.decode('utf-8')
            
            # Vérifier si les messages de l'utilisateur 1 sont présents
            user1_messages_found = sum(1 for msg in messages_user1 if msg in content1)
            print(f"      📝 Messages utilisateur 1 trouvés: {user1_messages_found}/{len(messages_user1)}")
            
            # Vérifier si les messages de l'utilisateur 2 sont absents
            user2_messages_found = sum(1 for msg in messages_user2 if msg in content1)
            print(f"      🚫 Messages utilisateur 2 trouvés: {user2_messages_found}/{len(messages_user2)} (devrait être 0)")
            
        else:
            print(f"      ❌ Erreur HTTP: {response1.status_code}")
    except Exception as e:
        print(f"      ❌ Exception: {e}")
    
    # Historique utilisateur 2
    print(f"\n   👤 Historique utilisateur 2:")
    try:
        response2 = client2.get('/chat/history/')
        if response2.status_code == 200:
            print(f"      ✅ Page accessible (Code: {response2.status_code})")
            content2 = response2.content.decode('utf-8')
            
            # Vérifier si les messages de l'utilisateur 2 sont présents
            user2_messages_found = sum(1 for msg in messages_user2 if msg in content2)
            print(f"      📝 Messages utilisateur 2 trouvés: {user2_messages_found}/{len(messages_user2)}")
            
            # Vérifier si les messages de l'utilisateur 1 sont absents
            user1_messages_found = sum(1 for msg in messages_user1 if msg in content2)
            print(f"      🚫 Messages utilisateur 1 trouvés: {user1_messages_found}/{len(messages_user1)} (devrait être 0)")
            
        else:
            print(f"      ❌ Erreur HTTP: {response2.status_code}")
    except Exception as e:
        print(f"      ❌ Exception: {e}")
    
    print(f"\n🎯 Résumé:")
    print(f"   - Utilisateurs testés: 2")
    print(f"   - Sessions distinctes créées: {len(sessions) if 'sessions' in locals() else 'N/A'}")
    print(f"   - Total conversations: {conv_count if 'conv_count' in locals() else 'N/A'}")
    print(f"   - Total messages: {msg_count if 'msg_count' in locals() else 'N/A'}")
    print(f"   - Entrées historique: {hist_count if 'hist_count' in locals() else 'N/A'}")
    
    print(f"\n✅ Test terminé !")
    print(f"   Chaque utilisateur devrait voir uniquement ses propres conversations.")

if __name__ == "__main__":
    test_user_specific_history()
