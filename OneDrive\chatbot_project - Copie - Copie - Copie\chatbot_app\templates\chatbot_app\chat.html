{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Chat - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Variables de couleurs marocaines */
    :root {
        --moroccan-red: #c0392b;
        --moroccan-green: #27ae60;
        --light-bg: #f8f9fa;
        --dark-text: #333;
        --light-text: #fff;
        --border-radius: 16px;
        --shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(39, 174, 96, 0); }
        100% { box-shadow: 0 0 0 0 rgba(39, 174, 96, 0); }
    }

    @keyframes typing {
        0% { transform: translateY(0); }
        50% { transform: translateY(-8px); }
        100% { transform: translateY(0); }
    }

    @keyframes floatStar {
        0% { transform: translateY(0) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(180deg); }
        100% { transform: translateY(0) rotate(360deg); }
    }

    body {
        background-color: var(--light-bg);
        font-family: 'Poppins', sans-serif;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    /* Conteneur principal */
    .chat-container {
        max-width: 750px;
        width: 85%;
        margin: 2rem auto;
        background: var(--light-text);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        overflow: hidden;
        position: relative;
        animation: fadeIn 0.6s ease-out;
    }

    /* Étoiles flottantes */
    .star {
        position: absolute;
        opacity: 0.15;
        z-index: 1;
        animation: floatStar 15s infinite linear;
    }

    .star:nth-child(1) {
        top: 15%;
        left: 10%;
        font-size: 18px;
        color: var(--moroccan-red);
        animation-duration: 20s;
    }

    .star:nth-child(2) {
        top: 25%;
        right: 15%;
        font-size: 16px;
        color: var(--moroccan-green);
        animation-duration: 25s;
        animation-delay: 2s;
    }

    .star:nth-child(3) {
        bottom: 20%;
        left: 20%;
        font-size: 20px;
        color: var(--moroccan-red);
        animation-duration: 18s;
        animation-delay: 5s;
    }

    .star:nth-child(4) {
        bottom: 35%;
        right: 20%;
        font-size: 17px;
        color: var(--moroccan-green);
        animation-duration: 22s;
        animation-delay: 1s;
    }

    /* En-tête du chat */
    .chat-header {
        background: linear-gradient(135deg, var(--moroccan-red) 30%, var(--moroccan-green) 100%);
        color: var(--light-text);
        padding: 1rem;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.6rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 10;
    }

    .chat-header i {
        font-size: 1.4rem;
    }

    /* Zone de messages */
    .chat-messages {
        height: 400px;
        overflow-y: auto;
        padding: 1.5rem;
        background-color: #f9f9f9;
        background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M11 18l4-4h2l-4 4h-2zm8-8l4-4h2l-4 4h-2zm8-8l2-2h2l-2 2h-2z" fill="%23f0f0f0" fill-rule="evenodd"/></svg>');
        position: relative;
        scrollbar-width: thin;
        scrollbar-color: #ddd transparent;
    }

    .chat-messages::-webkit-scrollbar {
        width: 6px;
    }

    .chat-messages::-webkit-scrollbar-track {
        background: transparent;
    }

    .chat-messages::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 10px;
    }

    /* Messages */
    .message {
        margin-bottom: 1.2rem;
        max-width: 80%;
        animation: fadeIn 0.3s ease-out;
        position: relative;
    }

    .bot-message {
        margin-right: auto;
    }

    .user-message {
        margin-left: auto;
        text-align: right;
    }

    .message-content {
        padding: 0.8rem 1rem;
        border-radius: 16px;
        display: inline-block;
        white-space: pre-line;
        line-height: 1.4;
        font-size: 1rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .bot-message .message-content {
        background-color: #f0f2f5;
        color: var(--dark-text);
        border-bottom-left-radius: 5px;
    }

    .user-message .message-content {
        background: linear-gradient(135deg, var(--moroccan-red) 30%, var(--moroccan-green) 100%);
        color: var(--light-text);
        border-bottom-right-radius: 5px;
    }

    /* Timestamp */
    .message-time {
        font-size: 0.7rem;
        color: #888;
        margin-top: 0.2rem;
        display: block;
    }

    /* Zone de saisie */
    .chat-input {
        display: flex;
        padding: 1rem;
        background-color: var(--light-text);
        border-top: 1px solid #eaeaea;
        position: relative;
        z-index: 10;
    }

    .chat-input input {
        flex: 1;
        padding: 0.8rem 1rem;
        border: 1px solid #ddd;
        border-radius: 20px;
        font-size: 1rem;
        outline: none;
        transition: all 0.3s;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .chat-input input:focus {
        border-color: var(--moroccan-green);
        box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
    }

    .chat-input input::placeholder {
        color: #aaa;
    }

    .chat-input button {
        background: var(--moroccan-green);
        color: var(--light-text);
        border: none;
        border-radius: 50%;
        width: 42px;
        height: 42px;
        margin-left: 0.6rem;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(39, 174, 96, 0.3);
    }

    .chat-input button:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(39, 174, 96, 0.4);
    }

    .chat-input button i {
        font-size: 1.1rem;
    }

    /* Indicateur de frappe */
    .typing-indicator {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.8rem 1rem;
        background-color: #f0f2f5;
        border-radius: 18px;
        border-bottom-left-radius: 5px;
        display: inline-flex;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .typing-indicator span {
        height: 10px;
        width: 10px;
        background-color: var(--moroccan-green);
        border-radius: 50%;
        display: inline-block;
        margin-right: 6px;
        opacity: 0.7;
        animation: typing 1s infinite;
    }

    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }

    /* Barre d'outils */
    .chat-toolbar {
        display: flex;
        justify-content: center;
        padding: 0.6rem;
        border-top: 1px solid #eaeaea;
        background-color: #f8f9fa;
    }

    .chat-tool-btn {
        background: none;
        border: none;
        color: #666;
        font-size: 0.8rem;
        margin: 0 0.4rem;
        padding: 0.4rem 0.6rem;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
    }

    .chat-tool-btn i {
        margin-right: 0.3rem;
        font-size: 0.9rem;
    }

    .chat-tool-btn:hover {
        background-color: #e9ecef;
        color: var(--moroccan-green);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .chat-container {
            width: 95%;
            margin: 1rem auto;
            max-width: 650px;
        }

        .chat-messages {
            height: 350px;
            padding: 1.2rem;
        }

        .message {
            max-width: 90%;
        }

        .message-content {
            padding: 0.7rem 0.9rem;
            font-size: 0.95rem;
        }

        .chat-header {
            padding: 0.8rem;
            font-size: 1.1rem;
        }

        .chat-input {
            padding: 0.8rem;
        }

        .chat-input input {
            padding: 0.7rem 0.9rem;
        }

        .chat-input button {
            width: 38px;
            height: 38px;
        }
    }

    @media (max-width: 480px) {
        .chat-container {
            width: 100%;
            margin: 0;
            border-radius: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: none;
        }

        .chat-messages {
            flex: 1;
            height: auto;
            padding: 1rem;
        }

        .message {
            max-width: 95%;
        }

        .chat-toolbar {
            padding: 0.4rem;
        }

        .chat-tool-btn {
            font-size: 0.75rem;
            padding: 0.3rem 0.5rem;
        }

        .chat-tool-btn i {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <!-- Étoiles flottantes -->
    <div class="star"><i class="fas fa-star"></i></div>
    <div class="star"><i class="fas fa-star"></i></div>
    <div class="star"><i class="fas fa-star"></i></div>
    <div class="star"><i class="fas fa-star"></i></div>

    <div class="chat-header">
        <i class="fas fa-robot"></i> Assistant Maroc Cultures
    </div>

    <div class="chat-messages" id="chat-messages">
        <div class="message bot-message">
            <div class="message-content">
                🎉 Bonjour ! Je suis l'assistant virtuel de Maroc Cultures. Comment puis-je vous aider aujourd'hui ?
            </div>
            <span class="message-time">Aujourd'hui, {{ current_time|time:"H:i" }}</span>
        </div>
        <!-- Les messages s'afficheront ici -->
    </div>

    <form id="chat-form" class="chat-input">
        {% csrf_token %}
        <input type="text" id="user-input" placeholder="Tapez votre message ici..." required aria-label="Message">
        <button type="submit" aria-label="Envoyer">
            <i class="fas fa-paper-plane"></i>
        </button>
    </form>

    <!-- Barre d'outils -->
    <div class="chat-toolbar">
        <button type="button" class="chat-tool-btn" id="increaseFontBtn" aria-label="Augmenter la taille du texte">
            <i class="fas fa-text-height"></i> Agrandir
        </button>
        <button type="button" class="chat-tool-btn" id="decreaseFontBtn" aria-label="Diminuer la taille du texte">
            <i class="fas fa-text-height fa-flip-vertical"></i> Réduire
        </button>
        <button type="button" class="chat-tool-btn" id="clearChatBtn" aria-label="Effacer la conversation">
            <i class="fas fa-trash-alt"></i> Effacer
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatForm = document.getElementById('chat-form');
    const userInput = document.getElementById('user-input');
    const chatMessages = document.getElementById('chat-messages');
    const increaseFontBtn = document.getElementById('increaseFontBtn');
    const decreaseFontBtn = document.getElementById('decreaseFontBtn');
    const clearChatBtn = document.getElementById('clearChatBtn');

    // Variable pour suivre la taille de police actuelle
    let currentFontSize = 1; // 1rem par défaut

    // Fonction pour obtenir l'heure actuelle formatée
    function getCurrentTime() {
        const now = new Date();
        return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    }

    // Fonction pour ajouter un message à la conversation
    function addMessage(content, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = isUser ? 'message user-message' : 'message bot-message';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;
        contentDiv.style.fontSize = `${currentFontSize}rem`;

        const timeSpan = document.createElement('span');
        timeSpan.className = 'message-time';
        timeSpan.textContent = `Aujourd'hui, ${getCurrentTime()}`;

        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timeSpan);
        chatMessages.appendChild(messageDiv);

        // Animation d'entrée
        setTimeout(() => {
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 50);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Fonction pour afficher l'indicateur de frappe
    function showTypingIndicator() {
        // Vérifier si l'indicateur existe déjà
        if (document.getElementById('typing-indicator')) return;

        const indicator = document.createElement('div');
        indicator.className = 'message bot-message';
        indicator.id = 'typing-indicator';

        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';

        // Ajouter trois points
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('span');
            typingIndicator.appendChild(dot);
        }

        indicator.appendChild(typingIndicator);
        chatMessages.appendChild(indicator);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Fonction pour masquer l'indicateur de frappe
    function hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    // Mettre à jour la taille de la police
    function updateFontSize() {
        const messages = document.querySelectorAll('.message-content');
        messages.forEach(message => {
            message.style.fontSize = `${currentFontSize}rem`;
        });
    }

    // Augmenter la taille de la police
    increaseFontBtn.addEventListener('click', function() {
        if (currentFontSize < 1.5) { // Limite maximale
            currentFontSize += 0.1;
            updateFontSize();
        }
    });

    // Diminuer la taille de la police
    decreaseFontBtn.addEventListener('click', function() {
        if (currentFontSize > 0.8) { // Limite minimale
            currentFontSize -= 0.1;
            updateFontSize();
        }
    });

    // Effacer la conversation
    clearChatBtn.addEventListener('click', function() {
        if (confirm('Voulez-vous vraiment effacer toute la conversation?')) {
            // Garder uniquement le premier message (message d'accueil)
            const welcomeMessage = chatMessages.firstElementChild;
            chatMessages.innerHTML = '';
            if (welcomeMessage) {
                chatMessages.appendChild(welcomeMessage);
            }
        }
    });

    // Gestion de l'envoi du formulaire
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const message = userInput.value.trim();
        if (!message) return;

        // Afficher le message de l'utilisateur
        addMessage(message, true);
        userInput.value = '';

        // Afficher l'indicateur de frappe
        showTypingIndicator();

        // Envoyer la requête au serveur
        fetch('{% url "process_message" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: new URLSearchParams({
                'message': message
            })
        })
        .then(response => response.json())
        .then(data => {
            // Masquer l'indicateur de frappe
            hideTypingIndicator();

            // Ajouter un délai pour simuler la réflexion
            setTimeout(() => {
                // Afficher la réponse du chatbot
                addMessage(data.response);
            }, 500);
        })
        .catch(error => {
            hideTypingIndicator();
            addMessage("Désolé, une erreur s'est produite. Veuillez réessayer plus tard.");
            console.error('Error:', error);
        });
    });

    // Focus sur le champ de saisie au chargement
    userInput.focus();

    // Appliquer la taille de police aux messages existants
    updateFontSize();
});
</script>
{% endblock %}
