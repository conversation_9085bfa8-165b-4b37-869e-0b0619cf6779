#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def simple_fix():
    """Simple fix for the JSON file"""
    
    # Read the file
    with open('chatbot_app/faq_data.json', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("File content length:", len(content))
    print("First 500 characters:")
    print(content[:500])
    
    # Find all JSON-like objects
    pattern = r'\{"instruction":[^}]+\}'
    matches = re.findall(pattern, content)
    
    print(f"Found {len(matches)} potential JSON objects")
    
    # Try to parse each match
    valid_objects = []
    for i, match in enumerate(matches):
        try:
            obj = json.loads(match)
            valid_objects.append(obj)
            print(f"Object {i+1}: {obj.get('instruction', 'No instruction')[:50]}...")
        except json.JSONDecodeError as e:
            print(f"Failed to parse object {i+1}: {e}")
            print(f"Content: {match[:100]}...")
    
    print(f"Successfully parsed {len(valid_objects)} objects")
    
    # Write the fixed JSON file
    if valid_objects:
        with open('chatbot_app/faq_data.json', 'w', encoding='utf-8') as f:
            json.dump(valid_objects, f, ensure_ascii=False, indent=2)
        print(f"Wrote {len(valid_objects)} objects to file")
    else:
        print("No valid objects found, not overwriting file")

if __name__ == "__main__":
    simple_fix()
