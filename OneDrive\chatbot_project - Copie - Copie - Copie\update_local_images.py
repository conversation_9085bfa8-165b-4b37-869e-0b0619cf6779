#!/usr/bin/env python
"""
Script pour mettre à jour certains événements avec des images locales
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def update_with_local_images():
    """Met à jour certains événements avec des images locales"""
    
    # Mapping des événements vers des images locales
    local_image_mapping = {
        "Festival Marocain de Montréal": "/static/img/events/festival_marocain.svg",
        "Soir<PERSON>: Iftar communautaire": "/static/img/events/ramadan_iftar.svg",
        "Concert de Musique Gnawa": "/static/img/events/concert_gnawa.svg",
        "Festival Gnawa de Rabat 2025 - Spiritualité et Rythmes Ancestraux": "/static/img/events/concert_gnawa.svg"
    }
    
    try:
        updated_count = 0
        
        print("🖼️ Mise à jour avec des images locales...")
        print("=" * 60)
        
        for title, local_image in local_image_mapping.items():
            try:
                # Chercher l'événement par titre (recherche partielle)
                events = Event.objects.filter(title__icontains=title.split()[0])
                
                for event in events:
                    if any(word in event.title for word in title.split()):
                        old_image = event.image_url
                        event.image_url = local_image
                        event.save()
                        updated_count += 1
                        
                        print(f"✅ {event.title}")
                        print(f"   📅 Date: {event.date_start}")
                        print(f"   🖼️ Ancienne image: {old_image}")
                        print(f"   🖼️ Nouvelle image: {local_image}")
                        print("-" * 60)
                        break
                
            except Exception as e:
                print(f"❌ Erreur pour '{title}': {e}")
        
        print(f"\n🎉 Mise à jour terminée!")
        print(f"📊 {updated_count} événement(s) mis à jour avec des images locales")
        
        if updated_count > 0:
            print("\n💡 Les nouvelles images locales sont maintenant actives!")
            print("🔄 Actualisez votre navigateur pour voir les changements.")
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = update_with_local_images()
    if success:
        print("\n✅ Script exécuté avec succès!")
    else:
        print("\n❌ Erreur lors de l'exécution du script!")
        sys.exit(1)
