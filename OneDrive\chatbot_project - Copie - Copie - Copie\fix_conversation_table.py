#!/usr/bin/env python
"""
Script pour corriger la structure de la table Conversation
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def fix_conversation_table():
    """Corrige la structure de la table Conversation"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Vérification de la structure de la table 'chatbot_app_conversation'...")
        
        # Vérifier si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_conversation'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ Table 'chatbot_app_conversation' existe")
            
            # Afficher la structure actuelle
            cursor.execute("DESCRIBE chatbot_app_conversation")
            columns = cursor.fetchall()
            
            print("\n📋 Structure actuelle de la table:")
            print("Column Name | Type | Null | Key | Default | Extra")
            print("-" * 60)
            for column in columns:
                print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
            
            # Vérifier les colonnes manquantes
            column_names = [col[0] for col in columns]
            
            # Ajouter la colonne 'title' si elle n'existe pas
            if 'title' not in column_names:
                try:
                    cursor.execute("ALTER TABLE chatbot_app_conversation ADD COLUMN `title` VARCHAR(200) NOT NULL DEFAULT 'Conversation'")
                    print("✅ Colonne 'title' ajoutée")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'ajout de 'title': {e}")
            else:
                print("✅ Colonne 'title' existe déjà")
            
            # Ajouter la colonne 'session_id' si elle n'existe pas
            if 'session_id' not in column_names:
                try:
                    cursor.execute("ALTER TABLE chatbot_app_conversation ADD COLUMN `session_id` VARCHAR(100) NULL")
                    print("✅ Colonne 'session_id' ajoutée")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'ajout de 'session_id': {e}")
            else:
                print("✅ Colonne 'session_id' existe déjà")
            
            # Ajouter la colonne 'auth_user_id' si elle n'existe pas
            if 'auth_user_id' not in column_names:
                try:
                    cursor.execute("ALTER TABLE chatbot_app_conversation ADD COLUMN `auth_user_id` INT(11) NULL")
                    print("✅ Colonne 'auth_user_id' ajoutée")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'ajout de 'auth_user_id': {e}")
            else:
                print("✅ Colonne 'auth_user_id' existe déjà")
            
            # Valider les changements
            conn.commit()
            print("\n✅ Toutes les modifications ont été appliquées avec succès!")
            
            # Afficher la nouvelle structure
            cursor.execute("DESCRIBE chatbot_app_conversation")
            columns = cursor.fetchall()
            
            print("\n📋 Nouvelle structure de la table:")
            print("Column Name | Type | Null | Key | Default | Extra")
            print("-" * 60)
            for column in columns:
                print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
                
        else:
            print("❌ Table 'chatbot_app_conversation' n'existe pas")
            print("🔧 Création de la table...")
            
            # Créer la table avec la structure correcte
            create_table_sql = """
            CREATE TABLE `chatbot_app_conversation` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(200) NOT NULL DEFAULT 'Conversation',
                `date` datetime(6) NOT NULL,
                `session_id` varchar(100) NULL,
                `auth_user_id` int(11) NULL,
                PRIMARY KEY (`id`),
                KEY `chatbot_app_conversation_auth_user_id` (`auth_user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            
            cursor.execute(create_table_sql)
            print("✅ Table 'chatbot_app_conversation' créée")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_conversation_table()
