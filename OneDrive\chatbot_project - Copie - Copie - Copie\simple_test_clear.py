#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def simple_test_clear():
    """Test simple de la suppression d'historique"""
    
    print("🧪 Test simple de suppression d'historique...")
    
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        print("✅ Connexion à la base de données réussie")
        
        # 1. État initial
        print(f"\n📊 État initial:")
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
        hist_total = cursor.fetchone()[0]
        
        print(f"   - Total conversations: {conv_total}")
        print(f"   - Total messages: {msg_total}")
        print(f"   - Total historiques: {hist_total}")
        
        # 2. Trouver une session de test
        cursor.execute("SELECT session_id, COUNT(*) as count FROM chatbot_app_conversation WHERE session_id IS NOT NULL GROUP BY session_id LIMIT 1")
        session_data = cursor.fetchone()
        
        if not session_data:
            print("❌ Aucune session trouvée pour le test")
            return False
            
        test_session = session_data[0]
        session_conv_count = session_data[1]
        
        print(f"\n🎯 Session de test: {test_session}")
        print(f"   - Conversations dans cette session: {session_conv_count}")
        
        # Compter les historiques pour cette session
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique WHERE session_id = %s", (test_session,))
        session_hist_count = cursor.fetchone()[0]
        print(f"   - Historiques dans cette session: {session_hist_count}")
        
        # Compter les messages pour cette session
        cursor.execute("""
            SELECT COUNT(*) FROM chatbot_app_message m 
            JOIN chatbot_app_conversation c ON m.conversation_id = c.id 
            WHERE c.session_id = %s
        """, (test_session,))
        session_msg_count = cursor.fetchone()[0]
        print(f"   - Messages dans cette session: {session_msg_count}")
        
        # 3. Simuler la suppression (comme dans la vue Django)
        print(f"\n🗑️ Simulation de suppression pour session: {test_session}")
        
        # Supprimer d'abord les entrées d'historique
        cursor.execute("DELETE FROM chatbot_app_historique WHERE session_id = %s", (test_session,))
        deleted_hist = cursor.rowcount
        print(f"   - Historiques supprimés: {deleted_hist}")
        
        # Récupérer les IDs des conversations de la session
        cursor.execute("SELECT id FROM chatbot_app_conversation WHERE session_id = %s", (test_session,))
        conversation_ids = [row[0] for row in cursor.fetchall()]
        print(f"   - IDs de conversations trouvés: {conversation_ids}")
        
        deleted_msg = 0
        deleted_conv = 0
        
        if conversation_ids:
            # Supprimer les messages de ces conversations
            placeholders = ','.join(['%s'] * len(conversation_ids))
            cursor.execute(f"DELETE FROM chatbot_app_message WHERE conversation_id IN ({placeholders})", conversation_ids)
            deleted_msg = cursor.rowcount
            print(f"   - Messages supprimés: {deleted_msg}")
            
            # Puis supprimer les conversations
            cursor.execute("DELETE FROM chatbot_app_conversation WHERE session_id = %s", (test_session,))
            deleted_conv = cursor.rowcount
            print(f"   - Conversations supprimées: {deleted_conv}")
        
        # Valider les changements
        conn.commit()
        print(f"   ✅ Changements validés")
        
        # 4. Vérifier les résultats
        print(f"\n🔍 Vérification des résultats:")
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique")
        hist_after = cursor.fetchone()[0]
        
        print(f"   - Conversations après: {conv_after} (était: {conv_total})")
        print(f"   - Messages après: {msg_after} (était: {msg_total})")
        print(f"   - Historiques après: {hist_after} (était: {hist_total})")
        
        # Vérifier que la session spécifique a été nettoyée
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation WHERE session_id = %s", (test_session,))
        remaining_conv = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique WHERE session_id = %s", (test_session,))
        remaining_hist = cursor.fetchone()[0]
        
        print(f"   - Conversations restantes pour la session: {remaining_conv}")
        print(f"   - Historiques restants pour la session: {remaining_hist}")
        
        # 5. Évaluation du succès
        total_deleted = deleted_conv + deleted_msg + deleted_hist
        
        if remaining_conv == 0 and remaining_hist == 0 and total_deleted > 0:
            print(f"\n✅ SUCCÈS! Suppression complète réussie")
            print(f"   - {deleted_conv} conversations supprimées")
            print(f"   - {deleted_msg} messages supprimés")
            print(f"   - {deleted_hist} historiques supprimés")
            success = True
        elif total_deleted > 0:
            print(f"\n⚠️ SUCCÈS PARTIEL")
            print(f"   - {total_deleted} éléments supprimés au total")
            success = True
        else:
            print(f"\n❌ ÉCHEC - Aucun élément supprimé")
            success = False
        
        cursor.close()
        conn.close()
        
        return success
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    simple_test_clear()
