#!/usr/bin/env python3
"""
🧪 Test de l'API Mistral pour Maroc Cultures
Teste la connexion et les réponses de l'API Mistral
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.mistral_api import (
    get_mistral_response, 
    test_mistral_connection, 
    get_cultural_suggestion,
    get_mistral_response_with_context
)

def test_basic_connection():
    """Test de connexion basique"""
    print("🔗 Test de connexion à l'API Mistral...")
    success, message = test_mistral_connection()
    
    if success:
        print(f"✅ {message}")
        return True
    else:
        print(f"❌ {message}")
        return False

def test_cultural_questions():
    """Test avec des questions culturelles marocaines"""
    print("\n🇲🇦 Test des questions culturelles...")
    
    questions = [
        "Parle-moi du tajine marocain",
        "Quelle est l'histoire de Marrakech ?",
        "Qu'est-ce que la musique gnawa ?",
        "Décris l'artisanat marocain",
        "Quelles sont les villes impériales du Maroc ?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Question {i}: {question}")
        try:
            response = get_mistral_response(question)
            print(f"🤖 Réponse: {response[:150]}...")
            
            if "🇲🇦" in response:
                print("✅ Signature Maroc Cultures détectée")
            else:
                print("⚠️  Pas de signature détectée")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_non_cultural_questions():
    """Test avec des questions non-culturelles"""
    print("\n🚫 Test des questions non-culturelles...")
    
    questions = [
        "Comment programmer en Python ?",
        "Quel temps fait-il aujourd'hui ?",
        "Explique-moi la physique quantique"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Question {i}: {question}")
        try:
            response = get_mistral_response(question)
            print(f"🤖 Réponse: {response[:150]}...")
            
            if "Maroc" in response or "culture" in response.lower():
                print("✅ Redirection vers la culture marocaine détectée")
            else:
                print("⚠️  Pas de redirection détectée")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_cultural_suggestions():
    """Test des suggestions culturelles"""
    print("\n💡 Test des suggestions culturelles...")
    
    topics = ["gastronomie", "musique", "artisanat", "histoire", "villes"]
    
    for topic in topics:
        print(f"\n🎯 Sujet: {topic}")
        try:
            response = get_cultural_suggestion(topic)
            print(f"🤖 Suggestion: {response[:100]}...")
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_conversation_context():
    """Test avec contexte de conversation"""
    print("\n💬 Test du contexte de conversation...")
    
    # Simuler un historique de conversation
    conversation_history = [
        {"role": "user", "content": "Bonjour"},
        {"role": "assistant", "content": "Bonjour ! Je suis votre guide culturel du Maroc."},
        {"role": "user", "content": "Parle-moi de Fès"}
    ]
    
    question = "Et Marrakech, qu'est-ce qui la rend spéciale ?"
    
    print(f"📝 Question avec contexte: {question}")
    try:
        response = get_mistral_response(question, conversation_history)
        print(f"🤖 Réponse: {response[:150]}...")
        
        if "Marrakech" in response:
            print("✅ Contexte pris en compte")
        else:
            print("⚠️  Contexte peut-être ignoré")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_api_key_validation():
    """Test de validation de la clé API"""
    print("\n🔑 Test de validation de la clé API...")
    
    # Vérifier que la clé API est configurée
    api_key = os.getenv('MISTRAL_API_KEY')
    
    if api_key and api_key != 'your_mistral_api_key_here':
        print(f"✅ Clé API configurée: {api_key[:10]}...")
        return True
    else:
        print("❌ Clé API non configurée ou par défaut")
        return False

def test_error_handling():
    """Test de gestion d'erreurs"""
    print("\n🛡️  Test de gestion d'erreurs...")
    
    # Test avec message vide
    print("📝 Test message vide:")
    try:
        response = get_mistral_response("")
        print(f"🤖 Réponse: {response[:100]}...")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test avec message très long
    print("\n📝 Test message très long:")
    long_message = "Parle-moi du Maroc " * 100
    try:
        response = get_mistral_response(long_message)
        print(f"🤖 Réponse: {response[:100]}...")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Fonction principale de test"""
    print("🇲🇦 Test de l'API Mistral - Maroc Cultures")
    print("=" * 50)
    
    # Test de validation de la clé API
    if not test_api_key_validation():
        print("\n⚠️  Attention: Clé API non configurée. Certains tests peuvent échouer.")
    
    # Tests principaux
    tests = [
        ("Connexion basique", test_basic_connection),
        ("Questions culturelles", test_cultural_questions),
        ("Questions non-culturelles", test_non_cultural_questions),
        ("Suggestions culturelles", test_cultural_suggestions),
        ("Contexte de conversation", test_conversation_context),
        ("Gestion d'erreurs", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, "✅" if result != False else "⚠️"))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, "❌"))
    
    # Résumé des résultats
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*60)
    
    for test_name, status in results:
        print(f"{status} {test_name}")
    
    print("\n🎯 RECOMMANDATIONS:")
    print("1. Vérifiez que votre clé API Mistral est valide")
    print("2. Testez avec des questions variées sur la culture marocaine")
    print("3. Surveillez les logs pour détecter les erreurs")
    print("4. Assurez-vous que le fallback FAQ fonctionne")
    
    print("\n🚀 Test terminé !")

if __name__ == "__main__":
    main()
