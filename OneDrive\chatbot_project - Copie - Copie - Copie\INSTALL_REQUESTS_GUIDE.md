# 🔧 Guide d'Installation du Module Requests

## 🎯 **Situation Actuelle**

Votre chatbot fonctionne maintenant avec un système intelligent à 3 niveaux :

1. **📋 Réponses JSON** (rapides et précises)
2. **🤖 Réponses Mistral AI** (intelligentes - nécessite `requests`)
3. **💡 Réponses prédéfinies** (fallback sans `requests`)

---

## ⚠️ **Problème : Mo<PERSON><PERSON> `requests` manquant**

Sans `requests`, le chatbot utilise des réponses prédéfinies au lieu de l'API Mistral.

---

## 🚀 **Solutions d'Installation**

### **Méthode 1 : Terminal VSCode (Recommandée)**

1. **Ouvrez le terminal VSCode** (`Ctrl + ù`)
2. **Exécutez ces commandes une par une** :

```bash
# Aller dans le répertoire du projet
cd "OneDrive\chatbot_project - Copie - Copie - Copie"

# Activer l'environnement virtuel
venv\Scripts\activate

# Installer requests
pip install requests

# Vérifier l'installation
python -c "import requests; print('✅ Requests installé avec succès!')"

# Redémarrer le serveur
python manage.py runserver
```

### **Méthode 2 : PowerShell Administrateur**

```powershell
# Ouvrir PowerShell en tant qu'administrateur
cd "C:\Users\<USER>\OneDrive\chatbot_project - Copie - Copie - Copie"
.\venv\Scripts\Activate.ps1
pip install requests
python manage.py runserver
```

### **Méthode 3 : Commande directe**

```bash
"OneDrive\chatbot_project - Copie - Copie - Copie\venv\Scripts\pip.exe" install requests
```

---

## 🧪 **Test du Système Actuel (Sans requests)**

Même sans `requests`, votre chatbot fonctionne ! Testez ces questions :

### **Réponses JSON (Rapides)**
```
✅ "Bonjour"
✅ "Qu'est-ce que Maroc Cultures ?"
✅ "Parle-moi de l'histoire du Maroc"
```

### **Réponses Prédéfinies (Fallback)**
```
✅ "Parle-moi du tajine"
✅ "Qu'est-ce que le couscous ?"
✅ "Décris Marrakech"
✅ "Parle-moi de Fès"
✅ "Qu'est-ce que l'artisanat marocain ?"
✅ "Décris la musique marocaine"
```

---

## 📊 **Logs Actuels**

### **Avec réponse JSON :**
```
📋 Recherche dans le fichier JSON pour: Bonjour...
✅ Réponse trouvée dans le JSON (score: 3)
📄 Réponse JSON: Bonjour ! Je suis votre assistant...
```

### **Avec réponse prédéfinie :**
```
📋 Recherche dans le fichier JSON pour: tajine...
⚠️ Aucune correspondance suffisante dans le JSON (meilleur score: 1)
🤖 Aucune réponse JSON trouvée, utilisation de Mistral AI...
⚠️ Module 'requests' non installé, utilisation de réponses prédéfinies...
✅ Réponse prédéfinie trouvée pour 'tajine'
```

### **Après installation de requests :**
```
📋 Recherche dans le fichier JSON pour: tajine aux olives...
⚠️ Aucune correspondance suffisante dans le JSON (meilleur score: 1)
🤖 Aucune réponse JSON trouvée, utilisation de Mistral AI...
📤 Réponse Mistral reçue: Le tajine aux olives est un délicieux...
✅ Mistral AI a répondu avec succès !
```

---

## 🎯 **Avantages du Système Actuel**

### **Sans requests (Fonctionnel)**
- ✅ Réponses JSON rapides
- ✅ Réponses prédéfinies pour 6 sujets principaux
- ✅ Système robuste et fiable
- ✅ Pas de dépendance externe

### **Avec requests (Optimal)**
- ✅ Tout ce qui précède +
- ✅ Réponses Mistral AI intelligentes
- ✅ Gestion de questions complexes
- ✅ Réponses contextuelles et naturelles

---

## 🔧 **Dépannage**

### **Si pip ne fonctionne pas :**
```bash
python -m pip install requests
```

### **Si l'environnement virtuel ne s'active pas :**
```bash
python -m venv venv --upgrade-deps
venv\Scripts\activate
pip install requests
```

### **Si vous avez des erreurs de permissions :**
- Exécutez PowerShell en tant qu'administrateur
- Ou utilisez : `pip install --user requests`

---

## 📈 **Prochaines Étapes**

### **Immédiat (Fonctionne déjà)**
1. ✅ Testez le chatbot avec les questions ci-dessus
2. ✅ Vérifiez les logs dans le terminal
3. ✅ Le système fonctionne sans `requests`

### **Optimal (Après installation)**
1. 🔧 Installez `requests`
2. 🚀 Redémarrez le serveur
3. 🧪 Testez l'API Mistral
4. 📊 Surveillez les performances

---

## 🏆 **Résultat**

**MAINTENANT :** Votre chatbot fonctionne avec JSON + réponses prédéfinies
**APRÈS REQUESTS :** Votre chatbot fonctionnera avec JSON + Mistral AI

**Dans tous les cas, votre chatbot Maroc Cultures répond intelligemment !** 🇲🇦✨

---

## 💡 **Réponses Prédéfinies Disponibles**

Le système inclut des réponses pour :
- 🍲 **Tajine** - Plat emblématique
- 🥘 **Couscous** - Plat national
- 🏛️ **Marrakech** - Ville impériale
- 🕌 **Fès** - Capitale spirituelle
- 🎨 **Artisanat** - Richesse culturelle
- 🎵 **Musique** - Diversité musicale

**Testez dès maintenant !** 🎯
