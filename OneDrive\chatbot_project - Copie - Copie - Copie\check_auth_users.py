#!/usr/bin/env python3
"""
Check Django auth users in the database
"""

import pymysql

def check_auth_users():
    """Check all Django auth users in the database"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🔐 Django Auth Users in Database")
        print("=" * 60)
        
        # Check Django auth users
        cursor.execute("""
            SELECT id, username, email, is_superuser, is_staff, is_active, date_joined 
            FROM auth_user 
            ORDER BY id
        """)
        auth_users = cursor.fetchall()
        
        if auth_users:
            print(f"\n✅ Found {len(auth_users)} Django Auth Users:")
            print("-" * 60)
            for user in auth_users:
                print(f"ID: {user[0]}")
                print(f"Username: {user[1]}")
                print(f"Email: {user[2]}")
                print(f"Superuser: {'Yes' if user[3] else 'No'}")
                print(f"Staff: {'Yes' if user[4] else 'No'}")
                print(f"Active: {'Yes' if user[5] else 'No'}")
                print(f"Date Joined: {user[6]}")
                print("-" * 60)
        else:
            print("❌ No Django auth users found")
        
        print(f"\n📊 Total Django Auth Users: {len(auth_users)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    check_auth_users()
