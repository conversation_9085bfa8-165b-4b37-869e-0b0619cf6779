from django.core.management.base import BaseCommand
from chatbot_app.models import Event
from datetime import date, time

class Command(BaseCommand):
    help = 'Ajoute les événements culturels 2025 pour Maroc Cultures'

    def handle(self, *args, **options):
        self.stdout.write('🎭 Ajout des événements culturels 2025 - Maroc Cultures')
        self.stdout.write('=' * 60)

        # Liste des événements 2025 à ajouter
        events = [
            {
                'title': 'Festival Mawazine 2025 - Rythmes du Monde',
                'description': '''Le plus grand festival musical d'Afrique revient pour sa 19ème édition !
Mawazine 2025 promet une programmation exceptionnelle avec des artistes
internationaux de renom et les plus grandes stars de la musique marocaine.
Plus de 2 millions de spectateurs attendus sur 7 scènes à travers Rabat.

🎵 Programmation :
- <PERSON><PERSON> Bouregreg : Artistes internationaux
- Scène OLM Souissi : Stars arabes et marocaines
- Scène Nahda : Musique du monde
- <PERSON>ène Chellah : Patrimoine musical marocain
- <PERSON><PERSON>s Salé : Découvertes et jeunes talents

🎫 Entrée gratuite sur la plupart des scènes
💫 Concerts payants pour les têtes d'affiche internationales''',
                'date_start': date(2025, 5, 23),
                'date_end': date(2025, 5, 31),
                'time': time(20, 0),
                'location': 'Rabat - Multiples scènes (Bouregreg, OLM Souissi, Nahda, Chellah)',
                'image_url': 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.festivalmawazine.ma/inscription'
            },
            {
                'title': 'Festival du Théâtre des Cultures - Édition 2025',
                'description': '''Rendez-vous incontournable des arts dramatiques au Maroc, le Festival du
Théâtre des Cultures célèbre la diversité culturelle à travers des spectacles
innovants mêlant tradition et modernité.

🎭 Au Programme :
- Théâtre classique marocain et arabe
- Créations contemporaines internationales
- Spectacles jeune public
- Ateliers de formation théâtrale
- Rencontres avec les artistes

🏛️ Lieux prestigieux :
- Théâtre Mohammed V de Rabat
- Théâtre National Mohammed VI
- Complexe Culturel Sidi Belyout

🎟️ Tarifs préférentiels pour étudiants et groupes''',
                'date_start': date(2025, 3, 15),
                'date_end': date(2025, 3, 25),
                'time': time(19, 30),
                'location': 'Rabat - Théâtre Mohammed V et autres lieux culturels',
                'image_url': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.theatre-cultures.ma/billetterie'
            },
            {
                'title': 'Génération Mawazine 2025 - Tremplin des Jeunes Talents',
                'description': '''Le concours qui révèle les futures stars de la musique marocaine !
Génération Mawazine offre une plateforme unique aux jeunes artistes
pour exprimer leur talent et se faire connaître du grand public.

🎤 Catégories :
- Musique traditionnelle marocaine
- Fusion et world music
- Pop et variétés
- Rap et hip-hop marocain
- Musique électronique

🏆 Prix :
- Grand Prix : 100 000 DH + contrat d'enregistrement
- Prix du Public : 50 000 DH
- Prix de la Création : 30 000 DH

📝 Inscription ouverte aux 16-30 ans
🎵 Finale en direct pendant Mawazine 2025''',
                'date_start': date(2025, 2, 1),
                'date_end': date(2025, 5, 30),
                'time': time(14, 0),
                'location': 'Rabat - Conservatoire National de Musique (auditions) / Scène Mawazine (finale)',
                'image_url': 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.generation-mawazine.ma/concours'
            },
            {
                'title': 'Exposition "Artisanat Royal Marocain - Patrimoine Millénaire"',
                'description': '''Découvrez les trésors de l'artisanat marocain à travers une exposition
exceptionnelle présentant les plus belles pièces des ateliers royaux
et des maîtres artisans du Royaume.

🏺 Collections présentées :
- Zellige et mosaïques de Fès
- Tapis berbères de l'Atlas
- Poteries de Salé et Safi
- Bijoux en argent du Sud
- Cuirs et maroquinerie de Marrakech
- Bois sculpté et marqueterie

👥 Visites guidées par des experts
🎓 Ateliers d'initiation aux techniques traditionnelles
📚 Conférences sur l'histoire de l'artisanat marocain

🎫 Entrée libre - Donations bienvenues''',
                'date_start': date(2025, 4, 10),
                'date_end': date(2025, 6, 30),
                'time': time(9, 0),
                'location': 'Rabat - Musée Mohammed VI d\'Art Moderne et Contemporain',
                'image_url': 'https://images.unsplash.com/photo-1582555172866-f73bb12a2ab3?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.musee-mohammed6.ma/expositions'
            },
            {
                'title': 'Festival Gnawa de Rabat 2025 - Spiritualité et Rythmes Ancestraux',
                'description': '''Célébration de la musique gnawa, patrimoine spirituel et culturel
inscrit au patrimoine immatériel de l'UNESCO. Trois jours de concerts,
de rituels et de rencontres autour de cette tradition millénaire.

🥁 Programmation :
- Maîtres gnawa du Maroc et d'Afrique de l'Ouest
- Fusions contemporaines gnawa-jazz
- Rituels traditionnels de guérison
- Ateliers de percussions et chant

🌍 Invités internationaux :
- Musiciens du Mali, Sénégal, Guinée
- Jazzmen européens et américains
- Artistes de world music

🎭 Spectacles en plein air dans la médina de Rabat
🎪 Village artisanal et gastronomique''',
                'date_start': date(2025, 7, 18),
                'date_end': date(2025, 7, 20),
                'time': time(21, 0),
                'location': 'Rabat - Médina et Kasbah des Oudayas',
                'image_url': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.festival-gnawa-rabat.ma'
            },
            {
                'title': 'Nuits Ramadan Culturelles 2025 - Traditions et Spiritualité',
                'description': '''Programme spécial pendant le mois sacré de Ramadan, célébrant les
traditions culturelles et spirituelles marocaines à travers des
soirées thématiques familiales.

🌙 Programme des soirées :
- Semaine 1 : Musique spirituelle et chants religieux
- Semaine 2 : Contes et légendes du Maroc
- Semaine 3 : Poésie arabe et amazighe
- Semaine 4 : Gastronomie traditionnelle du Ramadan

👨‍👩‍👧‍👦 Activités familiales :
- Ateliers calligraphie arabe pour enfants
- Démonstrations culinaires
- Spectacles de marionnettes traditionnelles
- Récitations coraniques

🍽️ Iftar communautaire offert chaque soir
🎁 Animations gratuites pour tous''',
                'date_start': date(2025, 3, 1),
                'date_end': date(2025, 3, 30),
                'time': time(22, 0),
                'location': 'Rabat - Centre Culturel Mohammed VI',
                'image_url': 'https://images.unsplash.com/photo-1542816417-0983c9c9ad53?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.centre-culturel-m6.ma/ramadan'
            },
            {
                'title': 'Journées du Patrimoine Marocain 2025 - Mémoire Vivante',
                'description': '''Événement national célébrant la richesse du patrimoine marocain
matériel et immatériel. Ouverture exceptionnelle de sites historiques
et animations culturelles dans tout le Royaume.

🏰 Sites ouverts à Rabat :
- Palais Royal (jardins uniquement)
- Tour Hassan et Mausolée Mohammed V
- Chellah et nécropole mérinide
- Médina et remparts almohades

📚 Activités proposées :
- Visites guidées gratuites
- Reconstitutions historiques
- Ateliers artisanaux traditionnels
- Expositions photographiques
- Conférences d'historiens

🎭 Spectacles de rue et animations
📖 Lectures de textes historiques
🎨 Démonstrations d'arts traditionnels''',
                'date_start': date(2025, 9, 15),
                'date_end': date(2025, 9, 17),
                'time': time(10, 0),
                'location': 'Rabat - Sites patrimoniaux de la ville',
                'image_url': 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.patrimoine-maroc.ma/journees'
            },
            {
                'title': 'Festival de Musique Andalouse 2025 - Héritage d\'Al-Andalus',
                'description': '''Célébration de la musique andalouse marocaine, héritage précieux
d'Al-Andalus. Concerts exceptionnels des plus grands maîtres et
orchestres du genre, dans un cadre historique unique.

🎼 Orchestres participants :
- Orchestre de Fès (École de Fès)
- Ensemble Al-Brihi de Tétouan
- Orchestre Moulay Ahmed Loukili de Rabat
- Invités d'Algérie et de Tunisie

🎵 Répertoire :
- Noubas complètes des 11 modes
- Pièces rares et manuscrits anciens
- Créations contemporaines inspirées
- Collaborations inter-maghrébines

🏛️ Cadre exceptionnel :
- Théâtre en plein air de Chellah
- Cour d'honneur du Mausolée Mohammed V
- Jardins de l'Institut du Monde Arabe''',
                'date_start': date(2025, 10, 12),
                'date_end': date(2025, 10, 15),
                'time': time(20, 30),
                'location': 'Rabat - Chellah et sites historiques',
                'image_url': 'https://images.unsplash.com/photo-1503095396549-807759245b35?w=800&h=600&fit=crop&crop=center',
                'registration_url': 'https://www.musique-andalouse.ma/festival'
            }
        ]

        compteur_ajoutes = 0
        compteur_existants = 0

        for event_data in events:
            event, created = Event.objects.get_or_create(
                title=event_data['title'],
                defaults=event_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Événement ajouté : {event.title}')
                )
                compteur_ajoutes += 1
            else:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Événement déjà existant : {event.title}')
                )
                compteur_existants += 1

        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(f'📊 RÉSUMÉ :')
        self.stdout.write(f'   ✅ Événements ajoutés : {compteur_ajoutes}')
        self.stdout.write(f'   ⚠️  Événements existants : {compteur_existants}')
        self.stdout.write(f'   📅 Total événements 2025 : {compteur_ajoutes + compteur_existants}')
        self.stdout.write('\n🎭 Tous les événements culturels 2025 sont maintenant disponibles !')
        self.stdout.write('🌐 Consultez-les sur : http://127.0.0.1:8000/home/')
