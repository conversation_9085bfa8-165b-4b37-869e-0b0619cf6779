{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Admin Dashboard - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Admin Dashboard Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .dashboard-container {
        padding: 15px 0;
    }

    .dashboard-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .dashboard-header h1 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .dashboard-header p {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 0.8rem;
    }

    .dashboard-header .btn-outline-light {
        border: 1px solid rgba(255, 255, 255, 0.5);
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .dashboard-header .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .dashboard-card {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        margin-bottom: 15px;
        overflow: hidden;
    }

    .dashboard-card-header {
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dashboard-card-header h2 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #1abc9c;
    }

    .dashboard-card-body {
        padding: 12px;
    }

    .dashboard-table {
        width: 100%;
        font-size: 0.8rem;
    }

    .dashboard-table th {
        background-color: #f8f9fa;
        color: #555;
        font-weight: 600;
        padding: 6px 8px;
        border-bottom: 1px solid #eee;
    }

    .dashboard-table td {
        padding: 6px 8px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
    }

    .dashboard-table tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 30px;
        font-size: 0.7rem;
        font-weight: 500;
    }

    .status-active {
        background-color: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }

    .status-inactive {
        background-color: rgba(192, 57, 43, 0.1);
        color: #c0392b;
    }

    .action-btn {
        padding: 3px 6px;
        border-radius: 3px;
        font-size: 0.75rem;
        margin-right: 2px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s;
    }

    .btn-view {
        background-color: #3498db;
        color: white;
    }

    .btn-edit {
        background-color: #27ae60;
        color: white;
    }

    .btn-delete {
        background-color: #c0392b;
        color: white;
    }

    .action-btn:hover {
        opacity: 0.9;
        transform: translateY(-1px);
        color: white;
    }

    .add-user-btn {
        background-color: #27ae60;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 5px 10px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .add-user-btn i {
        margin-right: 3px;
    }

    .add-user-btn:hover {
        background-color: #219653;
        transform: translateY(-1px);
        color: white;
    }



    .pagination {
        margin-top: 12px;
        display: flex;
        justify-content: center;
    }

    .pagination .page-item .page-link {
        color: #27ae60;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .pagination .page-item.active .page-link {
        background-color: #27ae60;
        border-color: #27ae60;
        color: white;
    }

    .search-form {
        margin-bottom: 12px;
    }

    .search-form .form-control {
        border-radius: 30px;
        padding: 5px 12px;
        border: 1px solid #ddd;
        font-size: 0.8rem;
        height: auto;
    }

    .search-form .form-control:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 0.1rem rgba(39, 174, 96, 0.15);
    }

    .search-form .btn {
        border-radius: 30px;
        background-color: #27ae60;
        color: white;
        border: none;
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .search-form .btn:hover {
        background-color: #219653;
    }
</style>
{% endblock %}

{% block content %}
<div class="container dashboard-container">
    <div class="dashboard-header d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h1>
            <p>Gérez les utilisateurs</p>
        </div>
        <div>
            <a href="{% url 'admin_events_list' %}" class="btn btn-sm btn-outline-light me-2">
                <i class="fas fa-calendar-alt"></i> Gestion des événements
            </a>
            <a href="{% url 'home' %}" class="btn btn-sm btn-outline-light">
                <i class="fas fa-home"></i> Retour au site
            </a>
        </div>
    </div>

    <div class="row g-3">
        <!-- Main Content -->
        <div class="col-12">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h2><i class="fas fa-users me-1"></i>Gestion des utilisateurs</h2>
                    <a href="{% url 'admin_add_user' %}" class="add-user-btn">
                        <i class="fas fa-plus"></i> Ajouter
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <!-- Search Form -->
                    <form class="search-form row g-2" method="get">
                        <div class="col-md-10">
                            <input type="text" class="form-control" name="search" placeholder="Rechercher un utilisateur..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn w-100"><i class="fas fa-search"></i></button>
                        </div>
                    </form>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Nom d'utilisateur</th>
                                    <th>Email</th>
                                    <th>Date d'inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.date_joined|date:"d/m/Y" }}</td>
                                    <td>
                                        <a href="{% url 'admin_delete_user' user.id %}" class="action-btn btn-delete" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur?')" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun utilisateur trouvé</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if users.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            {% if users.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ users.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for i in users.paginator.page_range %}
                            <li class="page-item {% if users.number == i %}active{% endif %}">
                                <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endfor %}

                            {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ users.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
