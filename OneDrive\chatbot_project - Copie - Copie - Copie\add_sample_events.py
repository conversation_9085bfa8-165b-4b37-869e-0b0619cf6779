#!/usr/bin/env python3
"""
Ajouter des événements d'exemple pour l'application culturelle marocaine
"""

import os
import django
import sys
from datetime import datetime, timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def add_sample_events():
    """Ajouter des événements culturels marocains d'exemple"""
    
    print("🎭 Ajout d'événements culturels marocains")
    print("=" * 50)
    
    # Supprimer les anciens événements d'exemple
    Event.objects.filter(title__contains="Festival").delete()
    Event.objects.filter(title__contains="Exposition").delete()
    Event.objects.filter(title__contains="Concert").delete()
    Event.objects.filter(title__contains="Atelier").delete()
    
    # Dates pour les événements
    today = datetime.now().date()
    
    events_data = [
        {
            'title': 'Festival Marocain de Montréal 2024',
            'description': 'Le plus grand festival culturel marocain au Canada. Découvrez la richesse de la culture marocaine à travers la musique, la danse, l\'artisanat et la gastronomie traditionnelle. Un événement familial avec des spectacles de groupes folkloriques, des ateliers de henné, et des dégustations de thé à la menthe.',
            'date_start': today + timedelta(days=15),
            'date_end': today + timedelta(days=17),
            'time_start': '10:00:00',
            'time_end': '22:00:00',
            'location': 'Parc Jean-Drapeau, Montréal',
            'price': 0.00,
            'max_participants': 5000,
            'category': 'Festival',
            'organizer': 'Association Culturelle Marocaine de Montréal'
        },
        {
            'title': 'Exposition d\'Art Contemporain Marocain',
            'description': 'Une exposition unique présentant les œuvres d\'artistes marocains contemporains. Peintures, sculptures et installations qui reflètent l\'évolution de l\'art marocain moderne tout en préservant les traditions ancestrales.',
            'date_start': today + timedelta(days=5),
            'date_end': today + timedelta(days=35),
            'time_start': '09:00:00',
            'time_end': '18:00:00',
            'location': 'Galerie d\'Art Contemporain, Centre-ville',
            'price': 15.00,
            'max_participants': 200,
            'category': 'Exposition',
            'organizer': 'Galerie Maghreb Arts'
        },
        {
            'title': 'Concert de Musique Gnawa',
            'description': 'Soirée exceptionnelle de musique Gnawa avec des maîtres musiciens venus directement d\'Essaouira. La musique Gnawa, patrimoine spirituel et musical du Maroc, vous transportera dans un voyage mystique unique.',
            'date_start': today + timedelta(days=8),
            'date_end': today + timedelta(days=8),
            'time_start': '20:00:00',
            'time_end': '23:00:00',
            'location': 'Théâtre Corona, Montréal',
            'price': 45.00,
            'max_participants': 800,
            'category': 'Concert',
            'organizer': 'Productions Culturelles Maghreb'
        },
        {
            'title': 'Atelier de Cuisine Marocaine Traditionnelle',
            'description': 'Apprenez à préparer les plats emblématiques de la cuisine marocaine : tajine, couscous, pastilla et pâtisseries orientales. Atelier dirigé par un chef professionnel avec dégustation incluse.',
            'date_start': today + timedelta(days=12),
            'date_end': today + timedelta(days=12),
            'time_start': '14:00:00',
            'time_end': '18:00:00',
            'location': 'École Culinaire Internationale',
            'price': 85.00,
            'max_participants': 25,
            'category': 'Atelier',
            'organizer': 'Chef Amina Benali'
        },
        {
            'title': 'Soirée Culturelle : Contes et Légendes du Maroc',
            'description': 'Une soirée magique dédiée aux contes traditionnels marocains. Conteurs professionnels, musique d\'ambiance et thé à la menthe pour une immersion totale dans l\'imaginaire marocain.',
            'date_start': today + timedelta(days=20),
            'date_end': today + timedelta(days=20),
            'time_start': '19:30:00',
            'time_end': '22:00:00',
            'location': 'Bibliothèque Centrale, Salle des Conférences',
            'price': 12.00,
            'max_participants': 150,
            'category': 'Spectacle',
            'organizer': 'Cercle des Conteurs Maghrébins'
        },
        {
            'title': 'Marché Artisanal Marocain',
            'description': 'Découvrez l\'artisanat authentique du Maroc : tapis berbères, poteries de Salé, bijoux en argent, maroquinerie de Fès, et bien plus. Rencontrez les artisans et découvrez leurs techniques ancestrales.',
            'date_start': today + timedelta(days=25),
            'date_end': today + timedelta(days=27),
            'time_start': '10:00:00',
            'time_end': '19:00:00',
            'location': 'Place des Arts, Esplanade',
            'price': 0.00,
            'max_participants': 3000,
            'category': 'Marché',
            'organizer': 'Coopérative des Artisans Marocains'
        }
    ]
    
    created_count = 0
    
    for event_data in events_data:
        try:
            event = Event.objects.create(**event_data)
            print(f"✅ Événement créé: {event.title}")
            print(f"   📅 Date: {event.date_start} - {event.date_end}")
            print(f"   📍 Lieu: {event.location}")
            print(f"   💰 Prix: {event.price}€")
            print(f"   👥 Participants max: {event.max_participants}")
            print("-" * 50)
            created_count += 1
        except Exception as e:
            print(f"❌ Erreur lors de la création de l'événement '{event_data['title']}': {e}")
    
    print(f"\n🎉 {created_count} événements créés avec succès!")
    print(f"📊 Total d'événements dans la base: {Event.objects.count()}")

if __name__ == "__main__":
    add_sample_events()
