import pymysql
from datetime import datetime

def add_1_new_event():
    """Ajoute 1 nouvel événement culturel marocain"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🎭 Ajout d'1 nouvel événement culturel marocain...")
        print("=" * 60)
        
        # Nouvel événement : Spectacle de Danse Traditionnelle Marocaine
        event = (
            'Spectacle de Danse Traditionnelle Marocaine',
            'Une soirée magique dédiée aux danses traditionnelles du Maroc. Découvrez la beauté des danses berbères, chaâbi, et andalouses interprétées par la troupe nationale de danse du Maroc. Costumes authentiques, musique live et ambiance festive garantie.',
            '2025-11-20',
            '2025-11-20',
            '20:00:00',
            'Opéra de Rabat, Ra<PERSON>',
            'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800',
            'https://opera-rabat.ma/spectacle-danse-marocaine',
            datetime.now(),
            datetime.now()
        )
        
        # Vérifier si l'événement existe déjà
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_event WHERE title = %s", (event[0],))
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            print(f"ℹ️ Événement existe déjà: {event[0]}")
        else:
            # Insérer le nouvel événement
            insert_query = """
            INSERT INTO chatbot_app_event 
            (title, description, date_start, date_end, time, location, image_url, registration_url, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, event)
            connection.commit()
            
            print(f"✅ Événement créé: {event[0]}")
            print(f"   📅 Date: {event[2]}")
            print(f"   🕐 Heure: {event[4]}")
            print(f"   📍 Lieu: {event[5]}")
            print(f"   🖼️ Image: {event[6]}")
            print(f"   🔗 Inscription: {event[7]}")
            print("-" * 60)
        
        # Vérifier le nombre total d'événements
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_event")
        total_events = cursor.fetchone()[0]
        
        print(f"\n🎉 Nouvel événement ajouté avec succès!")
        print(f"📊 Total d'événements dans la base: {total_events}")
        
        # Afficher tous les événements
        cursor.execute("SELECT id, title, date_start, location FROM chatbot_app_event ORDER BY date_start")
        all_events = cursor.fetchall()
        
        if all_events:
            print(f"\n📋 Liste de tous les événements:")
            for event_info in all_events:
                print(f"   • ID: {event_info[0]} | {event_info[1]} | {event_info[2]} | {event_info[3]}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    add_1_new_event()
