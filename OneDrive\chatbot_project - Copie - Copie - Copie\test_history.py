#!/usr/bin/env python3
"""
Script pour tester l'historique des conversations
"""

import urllib.request
import urllib.parse

def test_history():
    url = "http://127.0.0.1:8000/chat/history/"
    
    try:
        print(f"🚀 Test de l'historique...")
        print(f"URL: {url}")
        
        # Créer la requête
        req = urllib.request.Request(url, method='GET')
        
        # Envoyer la requête GET
        with urllib.request.urlopen(req) as response:
            status_code = response.getcode()
            response_text = response.read().decode('utf-8')
            
            print(f"📊 Status Code: {status_code}")
            
            if status_code == 200:
                print(f"✅ Page historique chargée avec succès!")
                print(f"📝 Taille de la réponse: {len(response_text)} caractères")
                
                # Vérifier si la page contient des éléments attendus
                if "Historique des conversations" in response_text:
                    print("✅ Titre de la page trouvé")
                else:
                    print("⚠️ Titre de la page non trouvé")
                    
                if "conversation-date" in response_text:
                    print("✅ Conversations trouvées dans la page")
                else:
                    print("⚠️ Aucune conversation trouvée dans la page")
                    
            else:
                print(f"❌ Erreur HTTP: {status_code}")
                print(f"📝 Réponse: {response_text[:500]}...")
                
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_history()
