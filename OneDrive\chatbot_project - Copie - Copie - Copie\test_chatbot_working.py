#!/usr/bin/env python
"""
Test pour vérifier que le chatbot fonctionne
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
import json

def test_chatbot_working():
    """Test pour vérifier que le chatbot fonctionne maintenant"""
    print("🤖 Test final - Chatbot Maroc Cultures")
    print("=" * 50)
    
    # Créer un client de test Django
    client = Client()
    
    # Messages de test simples
    test_messages = [
        "bonjour",
        "salut",
        "merci"
    ]
    
    success_count = 0
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test {i}: '{message}'")
        
        try:
            # Envoyer une requête POST
            response = client.post('/process_message/', {
                'message': message
            })
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    bot_response = data.get('response', 'Pas de réponse')
                    conversation_id = data.get('conversation_id', 'N/A')
                    
                    print(f"   ✅ Réponse: {bot_response[:100]}...")
                    print(f"   📋 Conversation ID: {conversation_id}")
                    success_count += 1
                except json.JSONDecodeError:
                    print(f"   ❌ Réponse non-JSON: {response.content.decode()[:100]}...")
            else:
                print(f"   ❌ Erreur HTTP: {response.content.decode()[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print(f"\n📊 Résultats: {success_count}/{len(test_messages)} tests réussis")
    
    if success_count == len(test_messages):
        print("🎉 SUCCÈS ! Le chatbot fonctionne parfaitement !")
        print("✅ Vous pouvez maintenant utiliser le chatbot via l'interface web.")
    elif success_count > 0:
        print("⚠️ Le chatbot fonctionne partiellement.")
    else:
        print("❌ Le chatbot ne fonctionne pas encore.")
    
    return success_count == len(test_messages)

if __name__ == "__main__":
    test_chatbot_working()
