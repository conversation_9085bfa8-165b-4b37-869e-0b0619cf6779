#!/usr/bin/env python
"""
Script pour tester le chatbot
"""
import os
import sys
import django
import json

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

def test_faq_loading():
    """Test le chargement du fichier FAQ"""
    try:
        from django.conf import settings
        faq_file = os.path.join(settings.BASE_DIR, 'chatbot_app', 'faq_data.json')
        
        print(f"📁 Chemin du fichier FAQ: {faq_file}")
        print(f"📋 Fichier existe: {os.path.exists(faq_file)}")
        
        if os.path.exists(faq_file):
            with open(faq_file, 'r', encoding='utf-8') as f:
                faq_data = json.load(f)
            print(f"✅ Fichier FAQ chargé avec succès: {len(faq_data)} entrées")
            
            # Afficher quelques exemples
            for i, item in enumerate(faq_data[:3]):
                print(f"  {i+1}. {item['instruction']}")
        else:
            print("❌ Fichier FAQ introuvable")
            
    except Exception as e:
        print(f"❌ Erreur lors du chargement du FAQ: {e}")
        import traceback
        traceback.print_exc()

def test_models():
    """Test les modèles Django"""
    try:
        from chatbot_app.models import FAQ, Conversation, Message, Chatbot
        
        print("\n🔍 Test des modèles:")
        
        # Test FAQ
        faq_count = FAQ.objects.count()
        print(f"  FAQ en base: {faq_count}")
        
        # Test Conversation
        conv_count = Conversation.objects.count()
        print(f"  Conversations: {conv_count}")
        
        # Test Message
        msg_count = Message.objects.count()
        print(f"  Messages: {msg_count}")
        
        # Test Chatbot
        chatbot_count = Chatbot.objects.count()
        print(f"  Chatbots: {chatbot_count}")
        
        print("✅ Tous les modèles sont accessibles")
        
    except Exception as e:
        print(f"❌ Erreur avec les modèles: {e}")
        import traceback
        traceback.print_exc()

def test_chatbot_logic():
    """Test la logique du chatbot"""
    try:
        print("\n🤖 Test de la logique du chatbot:")
        
        # Simuler un message utilisateur
        test_message = "bonjour"
        print(f"  Message test: '{test_message}'")
        
        # Test de correspondance simple
        from django.conf import settings
        faq_file = os.path.join(settings.BASE_DIR, 'chatbot_app', 'faq_data.json')
        
        with open(faq_file, 'r', encoding='utf-8') as f:
            faq_data = json.load(f)
        
        import re
        
        best_match = None
        highest_score = 0
        
        for item in faq_data:
            # Normaliser les textes pour la comparaison
            instruction_words = set(re.findall(r'\w+', item['instruction'].lower()))
            input_words = set(re.findall(r'\w+', item['input'].lower()))
            message_words = set(re.findall(r'\w+', test_message))
            
            # Calculer le score de correspondance
            instruction_common = instruction_words.intersection(message_words)
            input_common = input_words.intersection(message_words)
            
            score = max(len(instruction_common), len(input_common))
            
            if score > highest_score:
                highest_score = score
                best_match = item
        
        if best_match and highest_score >= 1:
            print(f"  ✅ Correspondance trouvée (score: {highest_score})")
            print(f"  Réponse: {best_match['output'][:100]}...")
        else:
            print(f"  ⚠️ Aucune correspondance trouvée (score: {highest_score})")
            
    except Exception as e:
        print(f"❌ Erreur dans la logique du chatbot: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Test du chatbot Maroc Cultures")
    print("=" * 50)
    
    test_faq_loading()
    test_models()
    test_chatbot_logic()
    
    print("\n✅ Tests terminés")
