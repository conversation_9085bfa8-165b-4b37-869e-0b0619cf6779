#!/usr/bin/env python3
"""
Test user creation to verify the RETURNING clause fix
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.contrib.auth.models import User
from chatbot_app.models import User as CustomUser

def test_user_creation():
    """Test creating users to verify the fix"""
    
    print("🧪 Testing User Creation with Custom Backend")
    print("=" * 50)
    
    # Test 1: Create Django auth user
    print("\n1️⃣ Testing Django Auth User creation...")
    try:
        # Check if test user already exists
        if User.objects.filter(username='testuser').exists():
            User.objects.filter(username='testuser').delete()
            print("   🗑️  Deleted existing test user")
        
        # Create new user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        print(f"   ✅ Django Auth User created successfully!")
        print(f"   📊 User ID: {user.id}")
        print(f"   👤 Username: {user.username}")
        print(f"   📧 Email: {user.email}")
        
        # Clean up
        user.delete()
        print("   🗑️  Test user cleaned up")
        
    except Exception as e:
        print(f"   ❌ Django Auth User creation failed: {e}")
        return False
    
    # Test 2: Create custom user
    print("\n2️⃣ Testing Custom User model creation...")
    try:
        # Check if test user already exists
        if CustomUser.objects.filter(nom='TestUser').exists():
            CustomUser.objects.filter(nom='TestUser').delete()
            print("   🗑️  Deleted existing custom test user")
        
        # Create new custom user
        custom_user = CustomUser.objects.create(
            nom='TestUser',
            email='<EMAIL>',
            mot_de_passe='testpass123'
        )
        
        print(f"   ✅ Custom User created successfully!")
        print(f"   📊 User ID: {custom_user.id}")
        print(f"   👤 Name: {custom_user.nom}")
        print(f"   📧 Email: {custom_user.email}")
        
        # Clean up
        custom_user.delete()
        print("   🗑️  Custom test user cleaned up")
        
    except Exception as e:
        print(f"   ❌ Custom User creation failed: {e}")
        return False
    
    print("\n🎉 ALL USER CREATION TESTS PASSED!")
    print("✅ The RETURNING clause issue has been fixed!")
    print("✅ User registration should now work properly!")
    
    return True

if __name__ == "__main__":
    test_user_creation()
