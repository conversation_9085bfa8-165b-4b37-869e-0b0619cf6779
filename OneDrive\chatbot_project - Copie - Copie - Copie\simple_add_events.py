#!/usr/bin/env python
"""
Script simple pour ajouter des événements
"""
import os
import sys
import django
from datetime import date, time

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def add_events():
    """Ajoute des événements d'exemple"""
    print("🎯 Ajout d'événements d'exemple...")
    
    try:
        # Événement 1: Festival Marocain de Montréal
        event1, created1 = Event.objects.get_or_create(
            title="Festival Marocain de Montréal",
            defaults={
                'description': "Un festival célébrant la riche culture marocaine avec de la musique traditionnelle, de la danse, de l'artisanat et de la cuisine authentique.",
                'date_start': date(2025, 7, 15),
                'date_end': date(2025, 7, 17),
                'time': time(10, 0),
                'location': "Parc <PERSON>, <PERSON>",
                'image_url': "https://example.com/festival-marocain.jpg",
                'registration_url': "https://example.com/inscription-festival"
            }
        )
        if created1:
            print(f"✅ Événement créé: {event1.title}")
        else:
            print(f"ℹ️ Événement existe déjà: {event1.title}")
        
        # Événement 2: Soirée Culturelle Marocaine
        event2, created2 = Event.objects.get_or_create(
            title="Soirée Culturelle Marocaine",
            defaults={
                'description': "Une soirée dédiée à la découverte de la culture marocaine à travers des spectacles de musique andalouse.",
                'date_start': date(2025, 6, 20),
                'time': time(19, 0),
                'location': "Centre Culturel de Montréal",
                'image_url': "https://example.com/soiree-culturelle.jpg",
                'registration_url': "https://example.com/inscription-soiree"
            }
        )
        if created2:
            print(f"✅ Événement créé: {event2.title}")
        else:
            print(f"ℹ️ Événement existe déjà: {event2.title}")
        
        # Événement 3: Atelier de Cuisine Marocaine
        event3, created3 = Event.objects.get_or_create(
            title="Atelier de Cuisine Marocaine",
            defaults={
                'description': "Apprenez à préparer des plats traditionnels marocains comme le tajine et le couscous.",
                'date_start': date(2025, 6, 10),
                'time': time(14, 0),
                'location': "École de Cuisine Montréal",
                'image_url': "https://example.com/atelier-cuisine.jpg",
                'registration_url': "https://example.com/inscription-cuisine"
            }
        )
        if created3:
            print(f"✅ Événement créé: {event3.title}")
        else:
            print(f"ℹ️ Événement existe déjà: {event3.title}")
        
        print(f"\n🎉 Total d'événements: {Event.objects.count()}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_events()
