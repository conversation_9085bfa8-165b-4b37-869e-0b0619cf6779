#!/usr/bin/env python3
"""
Vérifier la structure de la table des événements
"""

import pymysql

def check_events_table():
    """Vérifier la structure de la table des événements"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("📋 Structure de la table des événements")
        print("=" * 50)
        
        # Vérifier si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_event'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ Table chatbot_app_event existe")
            
            # Afficher la structure
            cursor.execute("DESCRIBE chatbot_app_event")
            columns = cursor.fetchall()
            
            print("\n📊 Colonnes de la table:")
            for column in columns:
                print(f"   - {column[0]} ({column[1]})")
            
            # Compter les événements existants
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_event")
            count = cursor.fetchone()[0]
            print(f"\n📈 Nombre d'événements existants: {count}")
            
            # Afficher les événements existants
            if count > 0:
                cursor.execute("SELECT id, title, date_start, location FROM chatbot_app_event ORDER BY date_start")
                events = cursor.fetchall()
                print("\n🎭 Événements existants:")
                for event in events:
                    print(f"   ID: {event[0]} | {event[1]} | {event[2]} | {event[3]}")
        else:
            print("❌ Table chatbot_app_event n'existe pas")
            
            # Lister toutes les tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print("\n📋 Tables disponibles:")
            for table in tables:
                print(f"   - {table[0]}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    check_events_table()
