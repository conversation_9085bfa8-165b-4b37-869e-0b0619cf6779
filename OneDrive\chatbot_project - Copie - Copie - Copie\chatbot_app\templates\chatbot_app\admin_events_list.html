{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Gestion des Événements - Admin Dashboard - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Admin Dashboard Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .dashboard-container {
        padding: 15px 0;
    }

    .dashboard-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .dashboard-header h1 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .dashboard-header p {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 0.8rem;
    }

    .dashboard-header .btn-outline-light {
        border: 1px solid rgba(255, 255, 255, 0.5);
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .dashboard-header .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .dashboard-card {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        margin-bottom: 15px;
        overflow: hidden;
    }

    .dashboard-card-header {
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dashboard-card-header h2 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #1abc9c;
    }

    .dashboard-card-body {
        padding: 12px;
    }

    .dashboard-table {
        width: 100%;
        font-size: 0.8rem;
    }

    .dashboard-table th {
        background-color: #f8f9fa;
        color: #555;
        font-weight: 600;
        padding: 6px 8px;
        border-bottom: 1px solid #eee;
    }

    .dashboard-table td {
        padding: 6px 8px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
    }

    .dashboard-table tr:last-child td {
        border-bottom: none;
    }

    .dashboard-table tr:hover {
        background-color: #f8f9fa;
    }

    .search-form {
        margin-bottom: 10px;
    }

    .search-form .form-control {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
        border-radius: 4px;
        border: 1px solid #ddd;
    }

    .search-form .btn {
        background-color: #1abc9c;
        color: white;
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
        border-radius: 4px;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border-radius: 4px;
        margin-right: 5px;
        color: white;
        text-decoration: none;
        font-size: 0.7rem;
    }

    .action-btn:hover {
        opacity: 0.9;
    }

    .btn-view {
        background-color: #3498db;
    }

    .btn-edit {
        background-color: #f39c12;
    }

    .btn-delete {
        background-color: #e74c3c;
    }

    .add-event-btn {
        background-color: #1abc9c;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        text-decoration: none;
    }

    .add-event-btn:hover {
        background-color: #16a085;
        color: white;
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 15px;
    }

    .pagination .page-item .page-link {
        color: #1abc9c;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .pagination .page-item.active .page-link {
        background-color: #1abc9c;
        border-color: #1abc9c;
        color: white;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
    }

    .admin-nav {
        margin-bottom: 15px;
    }

    .admin-nav .nav-link {
        color: #555;
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
        border-radius: 4px;
        margin-right: 5px;
    }

    .admin-nav .nav-link.active {
        background-color: #1abc9c;
        color: white;
    }

    .admin-nav .nav-link:hover:not(.active) {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container dashboard-container">
    <div class="dashboard-header d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-calendar-alt me-2"></i>Gestion des Événements</h1>
            <p>Gérez les événements culturels marocains</p>
        </div>
        <div>
            <a href="{% url 'admin_dashboard' %}" class="btn btn-sm btn-outline-light me-2">
                <i class="fas fa-users"></i> Gestion des utilisateurs
            </a>
            <a href="{% url 'home' %}" class="btn btn-sm btn-outline-light">
                <i class="fas fa-home"></i> Retour au site
            </a>
        </div>
    </div>

    <div class="row g-3">
        <!-- Main Content -->
        <div class="col-12">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h2><i class="fas fa-calendar-alt me-1"></i>Liste des événements</h2>
                    <a href="{% url 'admin_add_event' %}" class="add-event-btn">
                        <i class="fas fa-plus"></i> Ajouter un événement
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <!-- Search Form -->
                    <form class="search-form row g-2" method="get">
                        <div class="col-md-10">
                            <input type="text" class="form-control" name="search" placeholder="Rechercher un événement..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn w-100"><i class="fas fa-search"></i></button>
                        </div>
                    </form>

                    <!-- Events Table -->
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Date de début</th>
                                    <th>Lieu</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events %}
                                <tr>
                                    <td>{{ event.title }}</td>
                                    <td>{{ event.date_start|date:"d/m/Y" }}</td>
                                    <td>{{ event.location }}</td>
                                    <td>
                                        <a href="{% url 'event_detail' event.id %}" class="action-btn btn-view" title="Voir" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'admin_edit_event' event.id %}" class="action-btn btn-edit" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'admin_delete_event' event.id %}" class="action-btn btn-delete" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet événement?')" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun événement trouvé</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if events.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-3">
                        <ul class="pagination">
                            {% if events.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ events.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">&laquo;</span>
                            </li>
                            {% endif %}

                            {% for i in events.paginator.page_range %}
                            {% if events.number == i %}
                            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                            {% else %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a></li>
                            {% endif %}
                            {% endfor %}

                            {% if events.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ events.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">&raquo;</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
