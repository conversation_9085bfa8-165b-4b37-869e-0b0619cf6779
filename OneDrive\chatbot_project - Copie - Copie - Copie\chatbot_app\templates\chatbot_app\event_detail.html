{% extends 'chatbot_app/base.html' %}
{% load static %}
{% block title %}{{ event.title }} - MarocCultures{% endblock %}

{% block extra_css %}
<style>
    .event-header {
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.9) 0%, rgba(192, 57, 43, 0.8) 50%, rgba(39, 174, 96, 0.7) 100%);
        padding: 50px 0;
        margin-bottom: 40px;
        color: white;
        text-align: center;
        border-radius: 0 0 50% 50% / 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .event-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .event-meta {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 20px;
    }

    .event-meta-item {
        display: flex;
        align-items: center;
        font-size: 1rem;
    }

    .event-meta-item i {
        margin-right: 8px;
        font-size: 1.2rem;
    }

    .event-detail-card {
        background-color: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 30px;
    }

    .event-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
    }

    .event-content {
        padding: 30px;
    }

    .event-description {
        color: #555;
        line-height: 1.8;
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .event-info {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
    }

    .event-info-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #c0392b;
    }

    .event-info-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .event-info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .event-info-item i {
        color: #27ae60;
        margin-right: 10px;
        font-size: 1.2rem;
        margin-top: 3px;
    }

    .event-info-text {
        flex: 1;
    }

    .event-info-label {
        font-weight: 600;
        display: block;
        margin-bottom: 5px;
    }

    .event-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }

    .btn-event {
        display: inline-block;
        background-color: #27ae60;
        color: white;
        padding: 12px 25px;
        border-radius: 5px;
        text-decoration: none;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-event:hover {
        background-color: #219653;
        color: white;
    }

    .btn-back {
        background-color: #6c757d;
    }

    .btn-back:hover {
        background-color: #5a6268;
    }
</style>
{% endblock %}

{% block content %}
<!-- Event Header -->
<section class="event-header">
    <div class="container">
        <h1 class="event-title">{{ event.title }}</h1>
        <div class="event-meta">
            <div class="event-meta-item">
                <i class="far fa-calendar-alt"></i>
                {% if event.date_end %}
                {{ event.date_start|date:"d M Y" }} - {{ event.date_end|date:"d M Y" }}
                {% else %}
                {{ event.date_start|date:"d M Y" }}
                {% endif %}
            </div>
            <div class="event-meta-item">
                <i class="fas fa-map-marker-alt"></i>
                {{ event.location }}
            </div>
            {% if event.time %}
            <div class="event-meta-item">
                <i class="far fa-clock"></i>
                {{ event.time|time:"H:i" }}
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Event Content -->
<div class="container py-4">
    <div class="event-detail-card">
        {% if event.image_url %}
        <img src="{{ event.image_url }}" alt="{{ event.title }}" class="event-image">
        {% else %}
        <img src="{% static 'img/events/default_event.svg' %}" alt="{{ event.title }}" class="event-image">
        {% endif %}

        <div class="event-content">
            <div class="event-description">
                {{ event.description|linebreaks }}
            </div>

            <div class="event-info">
                <h3 class="event-info-title">Informations pratiques</h3>
                <ul class="event-info-list">
                    <li class="event-info-item">
                        <i class="far fa-calendar-alt"></i>
                        <div class="event-info-text">
                            <span class="event-info-label">Date</span>
                            {% if event.date_end %}
                            Du {{ event.date_start|date:"d F Y" }} au {{ event.date_end|date:"d F Y" }}
                            {% else %}
                            {{ event.date_start|date:"l d F Y" }}
                            {% endif %}
                        </div>
                    </li>
                    {% if event.time %}
                    <li class="event-info-item">
                        <i class="far fa-clock"></i>
                        <div class="event-info-text">
                            <span class="event-info-label">Heure</span>
                            {{ event.time|time:"H:i" }}
                        </div>
                    </li>
                    {% endif %}
                    <li class="event-info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="event-info-text">
                            <span class="event-info-label">Lieu</span>
                            {{ event.location }}
                        </div>
                    </li>
                </ul>
            </div>

            <div class="event-actions">
                <a href="{% url 'events_list' %}" class="btn-event btn-back">
                    <i class="fas fa-arrow-left"></i> Retour aux événements
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
