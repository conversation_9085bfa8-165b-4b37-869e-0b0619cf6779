#!/usr/bin/env python
"""
Script pour corriger et assigner les bonnes images aux événements
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def fix_event_images():
    """Corrige et assigne les bonnes images aux événements"""
    
    try:
        print("🖼️ Correction des images des événements...")
        print("=" * 60)
        
        # Récupérer tous les événements
        events = Event.objects.all()
        
        for event in events:
            old_image = event.image_url
            new_image = None
            
            # Assigner les images en fonction du titre exact
            if "Festival Marocain de Montréal" in event.title:
                new_image = "/static/img/events/festival_marocain.svg"
            elif "Soirée Ramadan" in event.title and "Iftar" in event.title:
                new_image = "/static/img/events/ramadan_iftar.svg"
            elif "Concert" in event.title and "Gnawa" in event.title:
                new_image = "/static/img/events/concert_gnawa.svg"
            elif "Festival Gnawa" in event.title:
                new_image = "/static/img/events/concert_gnawa.svg"
            elif "Ramadan" in event.title:
                new_image = "https://images.unsplash.com/photo-1542816417-0983c9c9ad53?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            elif "Festival" in event.title:
                new_image = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            elif "Concert" in event.title or "Musique" in event.title:
                new_image = "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            elif "Théâtre" in event.title or "Theatre" in event.title:
                new_image = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            elif "Exposition" in event.title:
                new_image = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            else:
                # Image par défaut
                new_image = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            
            # Mettre à jour si nécessaire
            if new_image and old_image != new_image:
                event.image_url = new_image
                event.save()
                
                print(f"✅ {event.title}")
                print(f"   📅 Date: {event.date_start}")
                print(f"   📍 Lieu: {event.location}")
                print(f"   🖼️ Nouvelle image: {new_image}")
                print("-" * 60)
            else:
                print(f"ℹ️ {event.title} - Image déjà correcte")
        
        print(f"\n🎉 Correction terminée!")
        print(f"📊 {events.count()} événement(s) vérifiés")
        
        # Afficher un résumé des images utilisées
        print("\n📋 Résumé des images:")
        local_images = events.filter(image_url__startswith='/static/').count()
        external_images = events.filter(image_url__startswith='http').count()
        
        print(f"   🏠 Images locales: {local_images}")
        print(f"   🌐 Images externes: {external_images}")
        
        print("\n💡 Actualisez votre navigateur pour voir les changements!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = fix_event_images()
    if success:
        print("\n✅ Script exécuté avec succès!")
    else:
        print("\n❌ Erreur lors de l'exécution du script!")
        sys.exit(1)
