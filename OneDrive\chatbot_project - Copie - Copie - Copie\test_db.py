import pymysql

try:
    # Connexion à MySQL
    connection = pymysql.connect(
        host='127.0.0.1',
        user='root',
        password='',
        database='maroc_cultures_db',
        port=3306
    )
    
    print("✅ Connexion réussie à MySQL!")
    
    # Création d'un curseur
    cursor = connection.cursor()
    
    # Exécution d'une requête pour lister les tables
    cursor.execute("SHOW TABLES")
    
    # Récupération des résultats
    tables = cursor.fetchall()
    
    print("\n📋 Tables dans la base de données maroc_cultures_db:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Vérification des données dans la table FAQ
    cursor.execute("SELECT COUNT(*) FROM chatbot_app_faq")
    faq_count = cursor.fetchone()[0]
    print(f"\n📊 Nombre d'entrées dans la table FAQ: {faq_count}")
    
    if faq_count > 0:
        print("\n📝 Exemples de FAQ:")
        cursor.execute("SELECT question, answer FROM chatbot_app_faq LIMIT 3")
        faqs = cursor.fetchall()
        for i, (question, answer) in enumerate(faqs, 1):
            print(f"\nFAQ #{i}:")
            print(f"Question: {question}")
            print(f"Réponse: {answer[:100]}..." if len(answer) > 100 else f"Réponse: {answer}")
    
    # Fermeture de la connexion
    cursor.close()
    connection.close()
    
    print("\n✅ La base de données est ACTIVE et fonctionne correctement!")
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    print("❌ La base de données n'est PAS ACTIVE ou rencontre des problèmes!")
