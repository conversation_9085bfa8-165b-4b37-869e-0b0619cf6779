<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Maroc Cultures{% endblock %}</title>
    {% load static %}
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    {% block extra_css %}{% endblock %}

    <style>
        /* Chat Toggle Button Styles */
        .chat-toggle-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #27ae60;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .chat-toggle-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
            animation: none;
            background-color: #219653;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
            }
        }

        /* Chat Badge */
        .chat-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #219653;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            border: 2px solid white;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 400px; /* Augmenté de 350px à 400px */
            height: 550px; /* Augmenté de 450px à 550px */
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
            z-index: 999;
            overflow: hidden;
            display: none;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .chat-widget.active {
            display: flex;
            animation: chatFadeIn 0.3s ease-out;
        }

        @keyframes chatFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 18px 20px;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .chat-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .chat-close:hover {
            transform: scale(1.1);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: #f8f9fa;
            scrollbar-width: thin;
            scrollbar-color: #ddd transparent;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background-color: #ddd;
            border-radius: 10px;
        }

        .chat-input-container {
            padding: 15px;
            border-top: 1px solid #eaeaea;
            display: flex;
            align-items: center;
            background-color: white;
        }

        .chat-input {
            flex: 1;
            padding: 14px 18px;
            border: 1px solid #ddd;
            border-radius: 24px;
            outline: none;
            font-size: 1rem;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .chat-input:focus {
            border-color: #27ae60;
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        }

        .chat-input::placeholder {
            color: #aaa;
        }

        .chat-send {
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            margin-left: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(39, 174, 96, 0.3);
        }

        .chat-send:hover {
            background-color: #219653;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.4);
        }

        .chat-send i {
            font-size: 1.1rem;
        }

        .message {
            margin-bottom: 20px;
            max-width: 85%; /* Augmenté de 80% à 85% */
            animation: messageFadeIn 0.3s ease-out;
        }

        @keyframes messageFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .bot-message {
            margin-right: auto;
        }

        .user-message {
            margin-left: auto;
            text-align: right;
        }

        .message-content {
            padding: 12px 18px;
            border-radius: 18px;
            display: inline-block;
            font-size: 1rem;
            line-height: 1.5;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .bot-message .message-content {
            background-color: #f0f2f5;
            color: #333;
            border-bottom-left-radius: 5px;
        }

        .user-message .message-content {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-bottom-right-radius: 5px;
        }

        /* Boutons d'accessibilité */
        .chat-accessibility {
            display: flex;
            justify-content: center;
            padding: 8px;
            border-top: 1px solid #eaeaea;
            background-color: #f8f9fa;
        }

        .chat-tool-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 14px;
            margin: 0 8px;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .chat-tool-btn:hover {
            background-color: #e9ecef;
            color: #27ae60;
        }

        .chat-tool-btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.3);
        }

        #historyBtn {
            color: #1abc9c;
        }

        #historyBtn:hover {
            background-color: rgba(26, 188, 156, 0.1);
        }

        /* Typing Indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 8px 16px;
            background-color: #f0f2f5;
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            display: inline-flex;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .typing-indicator span {
            height: 10px;
            width: 10px;
            background-color: #27ae60;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
            opacity: 0.7;
        }

        .typing-indicator span:nth-child(1) {
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation: typing 1s infinite 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation: typing 1s infinite 0.4s;
        }

        @keyframes typing {
            0% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-8px);
            }
            100% {
                transform: translateY(0);
            }
        }

        /* Responsive adjustments for chat widget */
        @media (max-width: 576px) {
            .chat-widget {
                width: 90%;
                right: 5%;
                left: 5%;
                height: 70vh;
                bottom: 80px;
            }

            .chat-toggle-btn {
                bottom: 20px;
                right: 20px;
            }

            .message-content {
                font-size: 0.95rem;
                padding: 10px 15px;
            }

            .chat-input {
                padding: 12px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'chatbot_app/navbar_compact.html' %}

    <!-- Main Content -->
    <main>
        <!-- Messages d'alerte -->
        {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
            <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    {% include 'chatbot_app/footer.html' %}

    <!-- Chat Toggle Button -->
    <div class="chat-toggle-btn" id="chatToggleBtn">
        <i class="fas fa-comments"></i>
        <span class="chat-badge" id="chatBadge">1</span>
    </div>

    <!-- Chat Widget -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div>
                <i class="fas fa-robot me-2"></i> Assistant Maroc Cultures
            </div>
            <button class="chat-close" id="chatClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot-message">
                <div class="message-content">
                    Bonjour ! Je suis l'assistant virtuel de Maroc Cultures. Comment puis-je vous aider aujourd'hui ?
                </div>
            </div>
        </div>
        <div class="chat-input-container">
            <input type="text" class="chat-input" id="chatInput" placeholder="Tapez votre message..." aria-label="Message">
            <button class="chat-send" id="chatSend" aria-label="Envoyer">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
        <!-- Boutons d'accessibilité -->
        <div class="chat-accessibility">
            <button class="chat-tool-btn" id="increaseFontBtn" aria-label="Augmenter la taille du texte">
                <i class="fas fa-text-height"></i>
            </button>
            <button class="chat-tool-btn" id="decreaseFontBtn" aria-label="Diminuer la taille du texte">
                <i class="fas fa-text-height fa-flip-vertical"></i>
            </button>
            <button class="chat-tool-btn" id="clearChatBtn" aria-label="Effacer la conversation">
                <i class="fas fa-trash-alt"></i>
            </button>
            <a href="{% url 'chat_history' %}" class="chat-tool-btn" id="historyBtn" aria-label="Voir l'historique des conversations">
                <i class="fas fa-history"></i>
            </a>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/navbar.js' %}"></script>

    <!-- Chat Widget JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatToggleBtn = document.getElementById('chatToggleBtn');
            const chatWidget = document.getElementById('chatWidget');
            const chatClose = document.getElementById('chatClose');
            const chatInput = document.getElementById('chatInput');
            const chatSend = document.getElementById('chatSend');
            const chatMessages = document.getElementById('chatMessages');
            const chatBadge = document.getElementById('chatBadge');
            const increaseFontBtn = document.getElementById('increaseFontBtn');
            const decreaseFontBtn = document.getElementById('decreaseFontBtn');
            const clearChatBtn = document.getElementById('clearChatBtn');

            // Variable pour suivre la taille de police actuelle
            let currentFontSize = 1; // 1rem par défaut

            // Toggle chat widget
            chatToggleBtn.addEventListener('click', function() {
                chatWidget.classList.toggle('active');
                if (chatWidget.classList.contains('active')) {
                    chatInput.focus();
                    // Hide badge when chat is opened
                    chatBadge.style.display = 'none';
                }
            });

            // Close chat widget
            chatClose.addEventListener('click', function() {
                chatWidget.classList.remove('active');
            });

            // Augmenter la taille de la police
            increaseFontBtn.addEventListener('click', function() {
                if (currentFontSize < 1.5) { // Limite maximale
                    currentFontSize += 0.1;
                    updateFontSize();
                }
            });

            // Diminuer la taille de la police
            decreaseFontBtn.addEventListener('click', function() {
                if (currentFontSize > 0.8) { // Limite minimale
                    currentFontSize -= 0.1;
                    updateFontSize();
                }
            });

            // Mettre à jour la taille de la police
            function updateFontSize() {
                const messages = document.querySelectorAll('.message-content');
                messages.forEach(message => {
                    message.style.fontSize = `${currentFontSize}rem`;
                });
            }

            // Effacer la conversation
            clearChatBtn.addEventListener('click', function() {
                if (confirm('Voulez-vous vraiment effacer toute la conversation?')) {
                    // Garder uniquement le premier message (message d'accueil)
                    const welcomeMessage = chatMessages.firstElementChild;
                    chatMessages.innerHTML = '';
                    if (welcomeMessage) {
                        chatMessages.appendChild(welcomeMessage);
                    }
                }
            });

            // Send message
            function sendMessage() {
                const message = chatInput.value.trim();
                if (message === '') return;

                // Add user message to chat
                addMessage(message, true);

                // Clear input
                chatInput.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send to server and get response
                fetch('{% url "process_message" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: new URLSearchParams({
                        'message': message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Hide typing indicator
                    hideTypingIndicator();

                    // Add bot response to chat after a small delay to simulate thinking
                    setTimeout(() => {
                        addMessage(data.response, false);
                    }, 500);
                })
                .catch(error => {
                    // Hide typing indicator
                    hideTypingIndicator();

                    console.error('Error:', error);
                    addMessage("Désolé, une erreur s'est produite. Veuillez réessayer plus tard.", false);
                });
            }

            // Add message to chat
            function addMessage(content, isUser) {
                const messageDiv = document.createElement('div');
                messageDiv.className = isUser ? 'message user-message' : 'message bot-message';

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                contentDiv.style.fontSize = `${currentFontSize}rem`; // Appliquer la taille de police actuelle

                messageDiv.appendChild(contentDiv);
                chatMessages.appendChild(messageDiv);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Show typing indicator
            function showTypingIndicator() {
                // Check if typing indicator already exists
                if (document.getElementById('typingIndicator')) return;

                const typingDiv = document.createElement('div');
                typingDiv.className = 'message bot-message';
                typingDiv.id = 'typingIndicator';

                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'typing-indicator';

                // Add three dots
                for (let i = 0; i < 3; i++) {
                    const dot = document.createElement('span');
                    typingIndicator.appendChild(dot);
                }

                typingDiv.appendChild(typingIndicator);
                chatMessages.appendChild(typingDiv);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Hide typing indicator
            function hideTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            // Send message on button click
            chatSend.addEventListener('click', sendMessage);

            // Send message on Enter key
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Appliquer la taille de police aux messages existants
            updateFontSize();
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>