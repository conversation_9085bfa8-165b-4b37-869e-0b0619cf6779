# 🔧 Installation du Module Requests - URGENT

## ❌ **Problème Identifié**

Le serveur Django fonctionne, mais l'API Mistral échoue avec l'erreur :
```
Erreur système de réponse: No module named 'requests'
```

## ✅ **Solution : Installer le module requests**

### **Méthode 1 : Terminal VSCode (Recommandée)**

1. **Ouvrez le terminal VSCode** (`Ctrl + ù`)
2. **Naviguez vers le projet** :
   ```bash
   cd "OneDrive\chatbot_project - Copie - Copie - Copie"
   ```
3. **Activez l'environnement virtuel** :
   ```bash
   venv\Scripts\activate
   ```
4. **Installez requests** :
   ```bash
   pip install requests
   ```

### **Méthode 2 : PowerShell**

```powershell
cd "C:\Users\<USER>\OneDrive\chatbot_project - Copie - Copie - Co<PERSON>"
.\venv\Scripts\Activate.ps1
pip install requests
```

### **Méthode 3 : Commande directe**

```bash
"OneDrive\chatbot_project - Copie - Co<PERSON> - Co<PERSON>\venv\Scripts\pip.exe" install requests
```

---

## 🧪 **Vérification**

Après installation, testez :

```bash
python -c "import requests; print('✅ Requests installé avec succès!')"
```

---

## 📦 **Installation Complète (Optionnel)**

Pour installer toutes les dépendances Mistral :

```bash
pip install requests mistralai python-dotenv
```

---

## 🚀 **Redémarrage du Serveur**

Après installation :

1. **Arrêtez le serveur Django** (`Ctrl + C`)
2. **Relancez le serveur** :
   ```bash
   python manage.py runserver
   ```

---

## 🧪 **Test de l'API Mistral**

Une fois `requests` installé, testez dans le chat :

```
✅ "Bonjour, parle-moi du tajine marocain"
```

Vous devriez voir dans les logs :
```
🤖 Tentative d'appel Mistral AI pour: Bonjour, parle-moi du tajine marocain...
📤 Réponse Mistral reçue: Le tajine est un plat emblématique...
✅ Mistral AI a répondu avec succès !
```

---

## 🎯 **Résultat Attendu**

Après installation, l'API Mistral devrait fonctionner et vous devriez recevoir des réponses intelligentes sur la culture marocaine au lieu des messages répétitifs actuels.

---

## 🔧 **Dépannage**

### **Si pip ne fonctionne pas :**
```bash
python -m pip install requests
```

### **Si l'environnement virtuel ne s'active pas :**
```bash
python -m venv venv --upgrade-deps
venv\Scripts\activate
pip install requests
```

### **Installation alternative :**
```bash
pip install -r requirements_mistral.txt
```

---

## 🏆 **Succès !**

Une fois `requests` installé, votre chatbot Maroc Cultures utilisera l'IA Mistral pour des conversations intelligentes sur la culture marocaine ! 🇲🇦✨
