#!/usr/bin/env python
"""
Test simple du chatbot
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

def test_chatbot():
    """Test simple du chatbot"""
    print("🤖 Test simple du chatbot")
    print("=" * 40)
    
    # Créer un client de test Django
    client = Client()
    
    # Messages de test
    test_messages = [
        "bonjour",
        "salut", 
        "événements",
        "contact",
        "merci"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test {i}: '{message}'")
        
        try:
            # Envoyer une requête POST
            response = client.post('/process_message/', {
                'message': message
            })
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    import json
                    data = json.loads(response.content)
                    bot_response = data.get('response', 'Pas de réponse')
                    conversation_id = data.get('conversation_id', 'N/A')
                    
                    print(f"   ✅ Réponse: {bot_response[:80]}...")
                    print(f"   📋 Conversation ID: {conversation_id}")
                except json.JSONDecodeError:
                    print(f"   ❌ Réponse non-JSON: {response.content.decode()[:80]}...")
            else:
                print(f"   ❌ Erreur: {response.content.decode()[:80]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n✅ Tests terminés")

if __name__ == "__main__":
    test_chatbot()
