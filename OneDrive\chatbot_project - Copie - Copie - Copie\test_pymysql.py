#!/usr/bin/env python3
"""
Test de connexion MySQL avec PyMySQL
"""

import pymysql

def test_mysql_connection():
    """Test de connexion avec PyMySQL"""
    print("🔍 Test de connexion MySQL avec PyMySQL...")
    print("=" * 50)
    
    try:
        # Configuration de connexion
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'connect_timeout': 10
        }
        
        print("Configuration:")
        print(f"  Host: {config['host']}")
        print(f"  Port: {config['port']}")
        print(f"  User: {config['user']}")
        print(f"  Password: {'(empty)' if not config['password'] else '***'}")
        print()
        
        # Test de connexion au serveur MySQL
        print("🔄 Connexion au serveur MySQL...")
        connection = pymysql.connect(**config)
        
        print("✅ Connexion au serveur MySQL réussie!")
        
        with connection.cursor() as cursor:
            # Obtenir la version MySQL
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📊 Version MySQL: {version}")
            
            # Lister les bases de données
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            db_names = [db[0] for db in databases]
            
            print(f"🗄️ Bases de données disponibles ({len(databases)}):")
            for db in db_names:
                print(f"  - {db}")
            
            # Vérifier si notre base de données existe
            target_db = 'chatbot_maroc_cultures'
            if target_db in db_names:
                print(f"✅ Base de données '{target_db}' existe!")
                
                # Se connecter à la base de données spécifique
                cursor.execute(f"USE {target_db}")
                cursor.execute("SELECT DATABASE()")
                current_db = cursor.fetchone()[0]
                print(f"🎯 Base de données actuelle: {current_db}")
                
                # Lister les tables
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"📋 Tables dans {target_db} ({len(tables)}):")
                    for table in tables:
                        print(f"  - {table[0]}")
                else:
                    print(f"ℹ️ Aucune table dans {target_db} (normal si les migrations n'ont pas été exécutées)")
                    
            else:
                print(f"❌ Base de données '{target_db}' n'existe pas!")
                print("💡 Création de la base de données...")
                
                cursor.execute(f"CREATE DATABASE {target_db} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"✅ Base de données '{target_db}' créée avec succès!")
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def configure_django_for_pymysql():
    """Instructions pour configurer Django avec PyMySQL"""
    print("\n🔧 Configuration Django pour PyMySQL:")
    print("=" * 50)
    
    print("Pour utiliser PyMySQL avec Django, ajoutez ceci au début de settings.py:")
    print()
    print("```python")
    print("import pymysql")
    print("pymysql.install_as_MySQLdb()")
    print("```")
    print()
    print("Ou créez un fichier __init__.py dans votre projet avec ce contenu.")

def main():
    """Fonction principale"""
    print("🚀 Test MySQL avec PyMySQL - Chatbot Maroc Cultures")
    print("=" * 60)
    print()
    
    success = test_mysql_connection()
    
    if success:
        print("\n🎉 Test de connexion MySQL réussi!")
        configure_django_for_pymysql()
        
        print("\n🚀 Prochaines étapes:")
        print("1. Configurez Django pour utiliser PyMySQL (voir instructions ci-dessus)")
        print("2. Exécutez: python manage.py migrate")
        print("3. Créez un superutilisateur: python manage.py createsuperuser")
        print("4. Démarrez le serveur: python manage.py runserver")
    else:
        print("\n❌ Test de connexion échoué!")
        print("Vérifiez que MySQL est démarré et accessible.")

if __name__ == "__main__":
    main()
