{% extends 'chatbot_app/base.html' %}
{% load static %}
{% block title %}Événements - MarocCultures{% endblock %}

{% block extra_css %}
<style>
    .events-header {
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.9) 0%, rgba(192, 57, 43, 0.8) 50%, rgba(39, 174, 96, 0.7) 100%);
        padding: 30px 0; /* Réduit de 50px à 30px */
        margin-bottom: 25px; /* Réduit de 40px à 25px */
        color: white;
        text-align: center;
        border-radius: 0 0 50% 50% / 15px; /* Réduit de 20px à 15px */
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); /* Réduit l'ombre */
    }

    .events-title {
        font-size: 2rem; /* Réduit de 2.5rem à 2rem */
        font-weight: 800;
        margin-bottom: 10px; /* Réduit de 15px à 10px */
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .events-subtitle {
        font-size: 1rem; /* Réduit de 1.1rem à 1rem */
        max-width: 650px; /* Réduit de 700px à 650px */
        margin: 0 auto;
        opacity: 0.9;
    }

    .event-card {
        background-color: white;
        border-radius: 10px; /* Réduit de 12px à 10px */
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Réduit l'ombre */
        margin-bottom: 20px; /* Réduit de 30px à 20px */
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1); /* Transition plus rapide */
        height: 100%;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .event-card:hover {
        transform: translateY(-5px); /* Réduit de -10px à -5px */
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); /* Réduit l'ombre */
    }

    .event-img {
        position: relative;
        height: 180px; /* Réduit de 200px à 180px */
        overflow: hidden;
    }

    .event-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.4s ease; /* Transition plus rapide */
    }

    .event-card:hover .event-img img {
        transform: scale(1.05); /* Réduit de 1.1 à 1.05 */
    }

    .event-date {
        position: absolute;
        top: 10px; /* Réduit de 15px à 10px */
        right: 10px; /* Réduit de 15px à 10px */
        background-color: rgba(39, 174, 96, 0.9);
        color: white;
        padding: 4px 8px; /* Réduit de 5px 10px à 4px 8px */
        border-radius: 4px; /* Réduit de 5px à 4px */
        font-size: 0.75rem; /* Réduit de 0.8rem à 0.75rem */
        font-weight: 600;
    }

    .event-content {
        padding: 15px; /* Réduit de 20px à 15px */
    }

    .event-title {
        font-size: 1.2rem; /* Réduit de 1.3rem à 1.2rem */
        font-weight: 700;
        margin-bottom: 8px; /* Réduit de 10px à 8px */
        color: #c0392b;
    }

    .event-location {
        font-size: 0.85rem; /* Réduit de 0.9rem à 0.85rem */
        color: #555;
        margin-bottom: 8px; /* Réduit de 10px à 8px */
        display: flex;
        align-items: center;
    }

    .event-location i {
        color: #27ae60;
        margin-right: 4px; /* Réduit de 5px à 4px */
    }

    .event-description {
        color: #666;
        margin-bottom: 12px; /* Réduit de 15px à 12px */
        font-size: 0.9rem; /* Réduit de 0.95rem à 0.9rem */
        line-height: 1.4; /* Réduit de 1.5 à 1.4 */
    }

    .event-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn-event {
        display: inline-block;
        background-color: #27ae60;
        color: white;
        padding: 6px 12px; /* Réduit de 8px 15px à 6px 12px */
        border-radius: 4px; /* Réduit de 5px à 4px */
        text-decoration: none;
        font-size: 0.85rem; /* Réduit de 0.9rem à 0.85rem */
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-event:hover {
        background-color: #219653;
        color: white;
    }

    .pagination {
        margin-top: 20px; /* Réduit de 30px à 20px */
        display: flex;
        justify-content: center;
    }

    .page-item .page-link {
        color: #27ae60;
        border-color: #e9ecef;
        padding: 0.4rem 0.75rem; /* Réduit la hauteur du bouton de pagination */
    }

    .page-item.active .page-link {
        background-color: #27ae60;
        border-color: #27ae60;
    }

    .no-events {
        text-align: center;
        padding: 50px 0;
        color: #666;
    }

    .no-events i {
        font-size: 3rem;
        color: #e0e0e0;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="events-header">
    <div class="container">
        <h1 class="events-title">Événements à Venir</h1>
        <p class="events-subtitle">Découvrez et participez aux événements culturels marocains organisés au Canada</p>
    </div>
</section>

<!-- Events Section -->
<div class="container py-3"> <!-- Réduit de py-4 à py-3 -->
    {% if events %}
    <div class="row g-3"> <!-- Ajout de g-3 pour réduire l'espacement entre les cartes -->
        {% for event in events %}
        <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="event-card">
                <div class="event-img">
                    {% if event.image_url %}
                    <img src="{{ event.image_url }}" alt="{{ event.title }}">
                    {% else %}
                    <img src="{% static 'img/events/default_event.svg' %}" alt="{{ event.title }}">
                    {% endif %}
                    <div class="event-date">
                        {{ event.date_start|date:"d M Y" }}
                    </div>
                </div>
                <div class="event-content">
                    <h3 class="event-title">{{ event.title }}</h3>
                    <p class="event-location">
                        <i class="fas fa-map-marker-alt"></i> {{ event.location }}
                    </p>
                    <p class="event-description">
                        {{ event.description|truncatechars:90 }} <!-- Réduit de 100 à 90 caractères -->
                    </p>
                    <div class="event-actions">
                        <a href="{% url 'event_detail' event.id %}" class="btn-event">
                            <i class="fas fa-info-circle"></i> Détails
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if events.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination">
            {% if events.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ events.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-hidden="true">&laquo;</span>
            </li>
            {% endif %}

            {% for i in events.paginator.page_range %}
            {% if events.number == i %}
            <li class="page-item active">
                <span class="page-link">{{ i }}</span>
            </li>
            {% else %}
            <li class="page-item">
                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
            </li>
            {% endif %}
            {% endfor %}

            {% if events.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ events.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-hidden="true">&raquo;</span>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="no-events">
        <i class="far fa-calendar-times"></i>
        <h3>Aucun événement à venir pour le moment</h3>
        <p>Revenez bientôt pour découvrir nos prochains événements culturels.</p>
    </div>
    {% endif %}
</div>
{% endblock %}
