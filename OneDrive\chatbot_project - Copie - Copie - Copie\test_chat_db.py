#!/usr/bin/env python3
"""
Script de test pour vérifier les données du chat dans la base de données SQLite
"""

import sqlite3
import os

def test_chat_database():
    try:
        # Connexion à la base de données SQLite
        db_path = os.path.join(os.path.dirname(__file__), 'db.sqlite3')
        print(f"🔍 Connexion à la base SQLite: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 TEST DE LA BASE DE DONNÉES CHAT")
        print("=" * 50)
        
        # Test 1: Vérifier les conversations
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count = cursor.fetchone()[0]
        print(f"📊 Nombre total de conversations: {conv_count}")
        
        # Test 2: Vérifier les messages
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count = cursor.fetchone()[0]
        print(f"💬 Nombre total de messages: {msg_count}")
        
        # Test 3: Afficher les dernières conversations
        cursor.execute("""
            SELECT id, title, date, session_id, auth_user_id
            FROM chatbot_app_conversation 
            ORDER BY date DESC 
            LIMIT 10
        """)
        
        conversations = cursor.fetchall()
        print(f"\n📋 Dernières conversations:")
        for conv in conversations:
            print(f"   ID: {conv[0]}, Titre: {conv[1]}, Date: {conv[2]}, Session: {conv[3]}, User: {conv[4]}")
        
        # Test 4: Afficher les derniers messages
        cursor.execute("""
            SELECT m.id, m.content, m.role, m.time, m.conversation_id
            FROM chatbot_app_message m
            ORDER BY m.time DESC 
            LIMIT 10
        """)
        
        messages = cursor.fetchall()
        print(f"\n💬 Derniers messages:")
        for msg in messages:
            content = msg[1][:50] + "..." if len(msg[1]) > 50 else msg[1]
            print(f"   ID: {msg[0]}, Contenu: {content}, Rôle: {msg[2]}, Conv: {msg[4]}")
        
        # Test 5: Vérifier les sessions
        cursor.execute("""
            SELECT session_id, COUNT(*) as count, MAX(date) as latest_date
            FROM chatbot_app_conversation 
            WHERE session_id IS NOT NULL
            GROUP BY session_id
            ORDER BY latest_date DESC
        """)
        
        sessions = cursor.fetchall()
        print(f"\n🔑 Sessions disponibles:")
        for session in sessions:
            print(f"   Session: {session[0]}, Conversations: {session[1]}, Dernière: {session[2]}")
        
        # Test 6: Test de la requête de l'historique (SQLite compatible)
        print(f"\n🔍 Test de la requête historique:")
        cursor.execute("""
            SELECT c.id, c.title, c.date, m.content, m.role, m.time
            FROM chatbot_app_conversation c
            LEFT JOIN chatbot_app_message m ON c.id = m.conversation_id
            WHERE c.date >= datetime('now', '-7 days')
            ORDER BY c.date DESC, m.time ASC
            LIMIT 20
        """)
        
        history_results = cursor.fetchall()
        print(f"   Résultats de la requête historique: {len(history_results)} lignes")
        for i, row in enumerate(history_results[:5]):  # Afficher les 5 premiers
            content = row[3][:30] + "..." if row[3] and len(row[3]) > 30 else row[3]
            print(f"   {i+1}. Conv: {row[0]}, Titre: {row[1]}, Contenu: {content}, Rôle: {row[4]}")
        
        cursor.close()
        conn.close()
        
        print("\n✅ Test terminé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chat_database()
