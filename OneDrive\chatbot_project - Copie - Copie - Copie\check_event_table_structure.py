#!/usr/bin/env python
"""
Script pour vérifier la structure de la table Event
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def check_event_table_structure():
    """Vérifie la structure de la table Event"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        # Vérifier si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_event'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ Table 'chatbot_app_event' existe")
            
            # Afficher la structure de la table
            cursor.execute("DESCRIBE chatbot_app_event")
            columns = cursor.fetchall()
            
            print("\n📋 Structure de la table 'chatbot_app_event':")
            print("Column Name | Type | Null | Key | Default | Extra")
            print("-" * 60)
            for column in columns:
                print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
            
            # Vérifier spécifiquement les colonnes de date
            column_names = [col[0] for col in columns]
            print(f"\n🔍 Colonnes trouvées: {column_names}")
            
            if 'date_start' in column_names:
                print("✅ Colonne 'date_start' trouvée")
            else:
                print("❌ Colonne 'date_start' manquante")
                
            if 'date_end' in column_names:
                print("✅ Colonne 'date_end' trouvée")
            else:
                print("❌ Colonne 'date_end' manquante")
                
        else:
            print("❌ Table 'chatbot_app_event' n'existe pas")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    check_event_table_structure()
