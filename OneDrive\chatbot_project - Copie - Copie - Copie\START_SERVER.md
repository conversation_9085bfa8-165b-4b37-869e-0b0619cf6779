# 🚀 Guide de Démarrage du Serveur - Maroc Cultures

## 🇲🇦 **Serveur Django avec Mistral AI**

### **📋 Instructions de Démarrage**

#### **Méthode 1 : Terminal VSCode (Recommandée)**
1. Ouvrez le terminal intégré de VSCode (`Ctrl + ù`)
2. Naviguez vers le répertoire du projet :
   ```bash
   cd "OneDrive\chatbot_project - Copie - Copie - Copie"
   ```
3. Activez l'environnement virtuel :
   ```bash
   venv\Scripts\activate
   ```
4. Lancez le serveur :
   ```bash
   python manage.py runserver
   ```

#### **Méthode 2 : Script Batch**
1. Double-cliquez sur `run_server.bat` dans l'explorateur de fichiers
2. Le serveur se lancera automatiquement

#### **Méthode 3 : PowerShell**
1. Ouvrez PowerShell en tant qu'administrateur
2. Naviguez vers le projet :
   ```powershell
   cd "C:\Users\<USER>\OneDrive\chatbot_project - Copie - Copie - Copie"
   ```
3. Exécutez le script :
   ```powershell
   .\run_server.ps1
   ```

---

## 🌐 **URLs Disponibles**

Une fois le serveur démarré, accédez à :

### **Pages Principales**
- 🏠 **Page d'accueil** : http://127.0.0.1:8000/home/
- 💬 **Chat Mistral AI** : http://127.0.0.1:8000/chat/
- 📋 **Historique** : http://127.0.0.1:8000/chat/history/
- ⭐ **Features** : http://127.0.0.1:8000/features/
- ℹ️ **À propos** : http://127.0.0.1:8000/about/
- 📧 **Contact** : http://127.0.0.1:8000/contact/

### **Authentification**
- 🔐 **Connexion** : http://127.0.0.1:8000/login/
- 📝 **Inscription** : http://127.0.0.1:8000/register/

### **Événements**
- 📅 **Liste des événements** : http://127.0.0.1:8000/events/
- 📄 **Détail événement** : http://127.0.0.1:8000/events/1/

### **Administration**
- 🛠️ **Dashboard Admin** : http://127.0.0.1:8000/admin-maroc-cultures/dashboard/
- 👥 **Gestion Utilisateurs** : http://127.0.0.1:8000/admin-maroc-cultures/users/
- 📅 **Gestion Événements** : http://127.0.0.1:8000/admin-maroc-cultures/events/

---

## 🧪 **Test de l'API Mistral**

### **Questions de Test**
Une fois sur la page chat, testez avec :

```
✅ "Bonjour, parle-moi du Maroc"
✅ "Qu'est-ce que le tajine ?"
✅ "Raconte-moi l'histoire de Marrakech"
✅ "Qu'est-ce que la musique gnawa ?"
✅ "Décris l'artisanat marocain"
✅ "Quelles sont les villes impériales ?"
```

### **Vérifications**
- ✅ Réponses en français
- ✅ Contenu culturel marocain
- ✅ Signature "🇲🇦 Maroc Cultures"
- ✅ Réponses contextuelles
- ✅ Fallback vers FAQ si erreur

---

## 🔧 **Dépannage**

### **Erreurs Communes**

#### **1. "Module not found"**
```bash
pip install -r requirements_mistral.txt
```

#### **2. "Database error"**
```bash
python manage.py migrate
```

#### **3. "Port already in use"**
```bash
python manage.py runserver 8001
```

#### **4. "Mistral API error"**
- Vérifiez la clé API dans `.env`
- Testez avec : `python test_mistral_api.py`

### **Logs Utiles**
- Erreurs Django : Terminal de lancement
- Erreurs Mistral : Console du navigateur (F12)
- Base de données : Logs MySQL

---

## 📊 **Monitoring**

### **Vérifications Post-Démarrage**
1. ✅ Serveur accessible sur http://127.0.0.1:8000/
2. ✅ Pages se chargent sans erreur
3. ✅ Chat répond avec Mistral AI
4. ✅ Historique fonctionne
5. ✅ Admin accessible

### **Performance**
- Temps de réponse chat : < 5 secondes
- Chargement pages : < 2 secondes
- Erreurs API : < 5%

---

## 🎯 **Prochaines Étapes**

1. **Tester toutes les fonctionnalités**
2. **Vérifier l'intégration Mistral**
3. **Tester sur différents navigateurs**
4. **Collecter les retours utilisateurs**
5. **Optimiser les performances**

---

## 🏆 **Succès !**

Si tout fonctionne :
- 🇲🇦 **Chatbot Mistral AI** opérationnel
- 💬 **Conversations intelligentes** sur la culture marocaine
- 📱 **Interface responsive** et moderne
- 🛠️ **Administration** complète

**Votre plateforme Maroc Cultures est prête !** ✨
