#!/usr/bin/env python
"""
Script pour corriger la structure de la table Event
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def fix_event_table():
    """Corrige la structure de la table Event"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Correction de la structure de la table 'chatbot_app_event'...")
        
        # 1. Renommer la colonne 'date' en 'date_start'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event CHANGE COLUMN `date` `date_start` DATE NOT NULL")
            print("✅ Colonne 'date' renommée en 'date_start'")
        except Exception as e:
            print(f"⚠️ Erreur lors du renommage de 'date' en 'date_start': {e}")
        
        # 2. Ajouter la colonne 'date_end'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event ADD COLUMN `date_end` DATE NULL")
            print("✅ Colonne 'date_end' ajoutée")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'ajout de 'date_end': {e}")
        
        # 3. Ajouter la colonne 'time'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event ADD COLUMN `time` TIME NULL")
            print("✅ Colonne 'time' ajoutée")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'ajout de 'time': {e}")
        
        # 4. Renommer la colonne 'image' en 'image_url' et changer le type
        try:
            cursor.execute("ALTER TABLE chatbot_app_event CHANGE COLUMN `image` `image_url` VARCHAR(200) NULL")
            print("✅ Colonne 'image' renommée en 'image_url'")
        except Exception as e:
            print(f"⚠️ Erreur lors du renommage de 'image' en 'image_url': {e}")
        
        # 5. Ajouter la colonne 'registration_url'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event ADD COLUMN `registration_url` VARCHAR(200) NULL")
            print("✅ Colonne 'registration_url' ajoutée")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'ajout de 'registration_url': {e}")
        
        # 6. Ajouter la colonne 'created_at'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event ADD COLUMN `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6)")
            print("✅ Colonne 'created_at' ajoutée")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'ajout de 'created_at': {e}")
        
        # 7. Ajouter la colonne 'updated_at'
        try:
            cursor.execute("ALTER TABLE chatbot_app_event ADD COLUMN `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)")
            print("✅ Colonne 'updated_at' ajoutée")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'ajout de 'updated_at': {e}")
        
        # Valider les changements
        conn.commit()
        print("\n✅ Toutes les modifications ont été appliquées avec succès!")
        
        # Vérifier la nouvelle structure
        cursor.execute("DESCRIBE chatbot_app_event")
        columns = cursor.fetchall()
        
        print("\n📋 Nouvelle structure de la table 'chatbot_app_event':")
        print("Column Name | Type | Null | Key | Default | Extra")
        print("-" * 60)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    fix_event_table()
