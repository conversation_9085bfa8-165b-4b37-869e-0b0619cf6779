# Generated by Django 5.2.1 on 2025-05-23 16:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot_app', '0007_event'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Historique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(blank=True, help_text='Identifiant de session pour les utilisateurs non connectés', max_length=100, null=True)),
                ('date_acces', models.DateTimeField(auto_now=True, verbose_name='Dernière consultation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('id_conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='historique_entries', to='chatbot_app.conversation')),
                ('id_utilisateur', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='historique', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date_acces'],
                'unique_together': {('id_conversation', 'id_utilisateur', 'session_id')},
            },
        ),
    ]
