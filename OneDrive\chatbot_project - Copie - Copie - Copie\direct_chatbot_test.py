#!/usr/bin/env python
"""
Test direct de la logique du chatbot
"""
import os
import sys
import django
import json
import re

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.conf import settings

def test_chatbot_logic():
    """Test direct de la logique du chatbot"""
    print("🤖 Test direct de la logique du chatbot")
    print("=" * 50)
    
    # Messages de test
    test_messages = [
        "bonjour",
        "salut",
        "événements", 
        "contact",
        "merci",
        "au revoir",
        "aide",
        "festival mawazine"
    ]
    
    # Charger les données FAQ
    faq_file = os.path.join(settings.BASE_DIR, 'chatbot_app', 'faq_data.json')
    
    try:
        with open(faq_file, 'r', encoding='utf-8') as f:
            faq_data = json.load(f)
        
        print(f"✅ FAQ chargé: {len(faq_data)} entrées")
        
        for i, user_message in enumerate(test_messages, 1):
            print(f"\n📝 Test {i}: '{user_message}'")
            
            # Logique de correspondance (copiée de views.py)
            best_match = None
            highest_score = 0
            
            for item in faq_data:
                # Normaliser les textes pour la comparaison
                instruction_words = set(re.findall(r'\w+', item['instruction'].lower()))
                input_words = set(re.findall(r'\w+', item['input'].lower()))
                message_words = set(re.findall(r'\w+', user_message.lower()))
                
                # Calculer le score de correspondance avec l'instruction
                instruction_common = instruction_words.intersection(message_words)
                instruction_score = len(instruction_common)
                
                # Calculer le score de correspondance avec l'input
                input_common = input_words.intersection(message_words)
                input_score = len(input_common)
                
                # Prendre le meilleur score
                score = max(instruction_score, input_score)
                
                if score > highest_score:
                    highest_score = score
                    best_match = item
            
            # Afficher le résultat
            if best_match and highest_score >= 1:
                print(f"   ✅ Correspondance trouvée (score: {highest_score})")
                print(f"   📋 Instruction: {best_match['instruction']}")
                print(f"   💬 Réponse: {best_match['output'][:100]}...")
            else:
                print(f"   ⚠️ Aucune correspondance (score: {highest_score})")
                print(f"   💬 Réponse par défaut serait utilisée")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ Tests terminés")

if __name__ == "__main__":
    test_chatbot_logic()
