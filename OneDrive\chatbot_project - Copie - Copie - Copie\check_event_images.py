#!/usr/bin/env python
"""
Script pour vérifier l'état des images des événements
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def check_event_images():
    """Vérifie l'état des images des événements"""
    
    try:
        print("🔍 Vérification des images des événements...")
        print("=" * 70)
        
        events = Event.objects.all().order_by('date_start')
        
        local_images = 0
        external_images = 0
        no_images = 0
        
        for event in events:
            print(f"📅 {event.title}")
            print(f"   Date: {event.date_start}")
            print(f"   Lieu: {event.location}")
            
            if event.image_url:
                if event.image_url.startswith('/static/'):
                    print(f"   🏠 Image locale: {event.image_url}")
                    local_images += 1
                    
                    # Vérifier si le fichier existe
                    file_path = f"OneDrive\\chatbot_project - Copie - Copie - Copie\\static{event.image_url[8:]}"
                    if os.path.exists(file_path):
                        print(f"   ✅ Fichier trouvé")
                    else:
                        print(f"   ❌ Fichier manquant: {file_path}")
                        
                elif event.image_url.startswith('http'):
                    print(f"   🌐 Image externe: {event.image_url[:60]}...")
                    external_images += 1
                else:
                    print(f"   ❓ Image inconnue: {event.image_url}")
            else:
                print(f"   ❌ Aucune image")
                no_images += 1
            
            print("-" * 70)
        
        print(f"\n📊 RÉSUMÉ:")
        print(f"   📅 Total événements: {events.count()}")
        print(f"   🏠 Images locales: {local_images}")
        print(f"   🌐 Images externes: {external_images}")
        print(f"   ❌ Sans image: {no_images}")
        
        # Vérifier les fichiers d'images locales
        print(f"\n📁 FICHIERS D'IMAGES LOCALES:")
        images_dir = "OneDrive\\chatbot_project - Copie - Copie - Copie\\static\\img\\events"
        if os.path.exists(images_dir):
            for file in os.listdir(images_dir):
                if file.endswith(('.svg', '.jpg', '.jpeg', '.png')):
                    file_path = os.path.join(images_dir, file)
                    size = os.path.getsize(file_path)
                    print(f"   📄 {file} ({size} bytes)")
        else:
            print(f"   ❌ Dossier non trouvé: {images_dir}")
        
        print(f"\n💡 RECOMMANDATIONS:")
        if no_images > 0:
            print(f"   - Assigner des images aux {no_images} événements sans image")
        if local_images > 0:
            print(f"   - Les {local_images} images locales sont prêtes")
        if external_images > 0:
            print(f"   - Les {external_images} images externes devraient fonctionner")
        
        print(f"\n🔄 Actualisez votre navigateur pour voir les images!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = check_event_images()
    if success:
        print("\n✅ Vérification terminée!")
    else:
        print("\n❌ Erreur lors de la vérification!")
        sys.exit(1)
