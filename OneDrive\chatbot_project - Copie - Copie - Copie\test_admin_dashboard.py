#!/usr/bin/env python3
"""
Test the admin dashboard functionality
"""

import os
import django
import sys

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.contrib.auth.models import User as AuthUser
from django.test import Client
from django.urls import reverse

def test_admin_dashboard():
    """Test the admin dashboard view"""
    
    print("🧪 Testing Admin Dashboard")
    print("=" * 50)
    
    # Check if users exist in database
    users = AuthUser.objects.all().order_by('-date_joined')
    print(f"\n📊 Found {users.count()} Django Auth Users:")
    
    for user in users:
        print(f"   - ID: {user.id} | Username: {user.username} | Email: {user.email} | Joined: {user.date_joined}")
    
    # Test the view
    client = Client()
    
    try:
        # Test admin dashboard URL
        response = client.get('/admin-maroc-cultures/dashboard/')
        print(f"\n🌐 Admin Dashboard Response:")
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Admin dashboard loads successfully!")
            
            # Check if users are in the response
            content = response.content.decode('utf-8')
            
            if users.exists():
                first_user = users.first()
                if first_user.username in content:
                    print(f"   ✅ Users are displayed! Found '{first_user.username}' in response")
                else:
                    print(f"   ❌ Users not found in response")
            else:
                if "Aucun utilisateur trouvé" in content:
                    print("   ✅ 'No users found' message displayed correctly")
                else:
                    print("   ❌ Expected 'No users found' message")
        else:
            print(f"   ❌ Admin dashboard failed to load: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing admin dashboard: {e}")

if __name__ == "__main__":
    test_admin_dashboard()
