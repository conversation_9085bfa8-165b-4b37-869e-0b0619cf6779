{% extends 'chatbot_app/base.html' %}

{% block title %}Inscription - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes floatStar {
        0% {
            transform: translateY(0) rotate(0deg);
        }
        50% {
            transform: translateY(-10px) rotate(180deg);
        }
        100% {
            transform: translateY(0) rotate(360deg);
        }
    }

    /* Register Page Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    /* Main layout container */
    .page-container {
        display: flex;
        min-height: calc(100vh - 60px); /* Adjust for navbar height */
        position: relative;
    }

    /* Content area (main area) */
    .content-area {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30px 15px; /* Réduit de 40px 20px à 30px 15px */
    }

    /* Register form container */
    .register-container {
        width: 100%;
        max-width: 450px; /* Réduit de 550px à 450px */
        margin: 0 auto;
        animation: fadeIn 0.8s ease-out;
        z-index: 10; /* Ensure form is above other elements */
    }

    .register-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        position: relative;
    }

    .register-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 20px 15px; /* Réduit de 25px 20px à 20px 15px */
        text-align: center;
        position: relative;
    }

    .register-header h2 {
        font-size: 1.4rem; /* Réduit de 1.6rem à 1.4rem */
        font-weight: 600;
        margin-bottom: 8px; /* Réduit de 10px à 8px */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .register-header h2 i {
        margin-right: 10px;
    }

    .register-header p {
        font-size: 0.9rem; /* Réduit de 1rem à 0.9rem */
        opacity: 0.95;
        margin-bottom: 0;
        line-height: 1.4; /* Réduit de 1.5 à 1.4 */
    }

    .register-body {
        padding: 20px; /* Réduit de 30px à 20px */
    }

    .form-group {
        margin-bottom: 16px; /* Réduit de 22px à 16px */
    }

    .form-label {
        display: block;
        margin-bottom: 6px; /* Réduit de 8px à 6px */
        font-weight: 500;
        color: #333;
        font-size: 0.9rem; /* Réduit de 0.95rem à 0.9rem */
    }

    .form-control {
        width: 100%;
        height: 40px; /* Réduit de 48px à 40px */
        padding: 8px 12px; /* Réduit de 10px 15px à 8px 12px */
        padding-left: 40px; /* Réduit de 45px à 40px */
        border: 1px solid #e0e0e0;
        border-radius: 6px; /* Réduit de 8px à 6px */
        background-color: #f5f7fa;
        transition: all 0.3s;
        font-size: 0.95rem; /* Réduit de 1rem à 0.95rem */
    }

    .form-control:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        outline: none;
        background-color: white;
    }

    .input-group {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 12px; /* Réduit de 15px à 12px */
        top: 50%;
        transform: translateY(-50%);
        color: #888;
        font-size: 1rem; /* Réduit de 1.1rem à 1rem */
    }

    .password-toggle {
        position: absolute;
        right: 12px; /* Réduit de 15px à 12px */
        top: 50%;
        transform: translateY(-50%);
        color: #888;
        cursor: pointer;
        font-size: 0.95rem; /* Réduit de 1rem à 0.95rem */
        z-index: 5;
    }

    .password-toggle:hover {
        color: #27ae60;
    }

    .register-btn {
        display: flex;
        width: 100%;
        background-color: #27ae60;
        color: white;
        border: none;
        border-radius: 6px; /* Réduit de 8px à 6px */
        padding: 12px; /* Réduit de 14px à 12px */
        font-size: 1rem; /* Réduit de 1.05rem à 1rem */
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        align-items: center;
        justify-content: center;
        margin-top: 8px; /* Réduit de 10px à 8px */
    }

    .register-btn:hover {
        background-color: #219653;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(39, 174, 96, 0.2);
    }

    .register-btn i {
        margin-right: 10px;
    }

    .register-footer {
        padding: 15px; /* Réduit de 18px à 15px */
        text-align: center;
        border-top: 1px solid #eee;
        background-color: #f8f9fa;
        font-size: 0.9rem; /* Réduit de 0.95rem à 0.9rem */
    }

    .login-link {
        color: #27ae60;
        text-decoration: none;
        font-weight: 500;
    }

    .login-link:hover {
        text-decoration: underline;
    }

    /* Floating stars */
    .star {
        position: absolute;
        opacity: 0.15;
        z-index: 1;
        animation: floatStar 15s infinite linear;
    }

    .star:nth-child(1) {
        top: 15%;
        left: 10%;
        font-size: 18px;
        color: #c0392b;
        animation-duration: 20s;
    }

    .star:nth-child(2) {
        top: 25%;
        right: 15%;
        font-size: 16px;
        color: #27ae60;
        animation-duration: 25s;
        animation-delay: 2s;
    }

    .star:nth-child(3) {
        bottom: 20%;
        left: 20%;
        font-size: 20px;
        color: #c0392b;
        animation-duration: 18s;
        animation-delay: 5s;
    }

    .star:nth-child(4) {
        bottom: 35%;
        right: 20%;
        font-size: 17px;
        color: #27ae60;
        animation-duration: 22s;
        animation-delay: 1s;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .page-container {
            flex-direction: column;
        }

        .content-area {
            padding: 30px 15px;
        }
    }

    @media (max-width: 768px) {
        .register-container {
            max-width: 100%;
        }

        .register-header h2 {
            font-size: 1.4rem;
        }

        .register-header p {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .register-body {
            padding: 20px 15px;
        }

        .form-control {
            height: 45px;
            font-size: 0.95rem;
        }

        .register-btn {
            padding: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="content-area">
        <div class="register-container">
            <!-- Étoiles flottantes -->
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>

            <div class="register-card">
                <div class="register-header">
                    <h2><i class="fas fa-user-plus"></i>Inscription</h2>
                
                </div>

                <div class="register-body">
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="username" class="form-label">Nom d'utilisateur</label>
                            <div class="input-group">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" class="form-control {% if form.username.errors %}is-invalid{% endif %}" id="username" name="username" placeholder="Entrez votre nom d'utilisateur" value="{{ form.username.value|default:'' }}" required>
                                {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Adresse email</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" id="email" name="email" placeholder="Entrez votre adresse email" value="{{ form.email.value|default:'' }}" required>
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>



                        <div class="form-group">
                            <label for="password1" class="form-label">Mot de passe</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" class="form-control {% if form.password1.errors %}is-invalid{% endif %}" id="password1" name="password1" placeholder="Entrez votre mot de passe" required>
                                <span class="password-toggle" onclick="togglePassword('password1', 'toggleIcon1')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </span>
                                {% if form.password1.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password1.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                        </div>

                        <div class="form-group">
                            <label for="password2" class="form-label">Confirmez le mot de passe</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" class="form-control {% if form.password2.errors %}is-invalid{% endif %}" id="password2" name="password2" placeholder="Confirmez votre mot de passe" required>
                                <span class="password-toggle" onclick="togglePassword('password2', 'toggleIcon2')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </span>
                                {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <button type="submit" class="register-btn">
                            <i class="fas fa-user-plus"></i>S'inscrire
                        </button>
                    </form>
                </div>

                <div class="register-footer">
                    <p class="mb-0">Vous avez déjà un compte? <a href="{% url 'login' %}" class="login-link">Connectez-vous</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Fonction pour afficher/masquer le mot de passe
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Ajuster la hauteur de la page pour éviter les chevauchements
document.addEventListener('DOMContentLoaded', function() {
    function adjustHeight() {
        const navbar = document.querySelector('.navbar');
        const footer = document.querySelector('footer');
        const pageContainer = document.querySelector('.page-container');

        if (navbar && footer && pageContainer) {
            const navbarHeight = navbar.offsetHeight;
            const footerHeight = footer.offsetHeight;
            const windowHeight = window.innerHeight;

            // Ajuster la hauteur minimale du conteneur principal
            pageContainer.style.minHeight = (windowHeight - navbarHeight - footerHeight) + 'px';
        }
    }

    // Appeler la fonction au chargement et au redimensionnement
    adjustHeight();
    window.addEventListener('resize', adjustHeight);
});
</script>
{% endblock %}
