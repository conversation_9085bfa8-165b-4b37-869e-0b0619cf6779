from django.db import models
from django.contrib.auth.models import User as AuthUser
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.urls import reverse

class FAQ(models.Model):
    question = models.TextField()
    answer = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)  # Ajout automatique à la création
    updated_at = models.DateTimeField(auto_now=True)      # Mise à jour automatique

    def __str__(self):
        return self.question

class User(models.Model):
    username = models.CharField(max_length=100, unique=True)
    email = models.EmailField(unique=True)
    mdp = models.CharField(max_length=128, verbose_name="mot de passe", default="default_password")  # Stocké de manière sécurisée
    privilege = models.Char<PERSON>ield(max_length=20, default="user")  # user, admin, etc.
    acd = models.DateTimeField(auto_now_add=True, verbose_name="date de création")

    def __str__(self):
        return self.username

    def register(self):
        """Enregistre un nouvel utilisateur"""
        self.acd = timezone.now()
        self.save()
        return self

    def login(self):
        """Connecte l'utilisateur"""
        # Logique de connexion
        return True

    def logout(self):
        """Déconnecte l'utilisateur"""
        # Logique de déconnexion
        return True

    def reset_password(self):
        """Réinitialise le mot de passe de l'utilisateur"""
        # Logique de réinitialisation
        return "nouveau_mot_de_passe_temporaire"

    class Meta:
        ordering = ['-acd']

class Admin(User):
    """Classe Admin héritant de User avec des privilèges supplémentaires"""

    def delete_user(self, user_id):
        """Supprime un utilisateur par son ID"""
        try:
            user = User.objects.get(id=user_id)
            user.delete()
            return True, f"L'utilisateur {user.username} a été supprimé avec succès."
        except User.DoesNotExist:
            return False, "Utilisateur non trouvé."
        except Exception as e:
            return False, f"Erreur lors de la suppression: {str(e)}"

    def modifier(self, user_id, **kwargs):
        """Modifie les informations d'un utilisateur

        Args:
            user_id: ID de l'utilisateur à modifier
            **kwargs: Attributs à modifier (username, email, mdp, privilege)

        Returns:
            tuple: (success, message)
        """
        try:
            user = User.objects.get(id=user_id)

            # Vérifier et mettre à jour les attributs fournis
            if 'username' in kwargs and kwargs['username']:
                # Vérifier si le nom d'utilisateur est déjà pris
                if User.objects.filter(username=kwargs['username']).exclude(id=user_id).exists():
                    return False, "Ce nom d'utilisateur est déjà utilisé."
                user.username = kwargs['username']

            if 'email' in kwargs and kwargs['email']:
                # Vérifier si l'email est déjà pris
                if User.objects.filter(email=kwargs['email']).exclude(id=user_id).exists():
                    return False, "Cet email est déjà utilisé."
                user.email = kwargs['email']



            if 'privilege' in kwargs and kwargs['privilege']:
                # Vérifier que le privilège est valide
                if kwargs['privilege'] not in ['user', 'admin']:
                    return False, "Privilège non valide. Utilisez 'user' ou 'admin'."
                user.privilege = kwargs['privilege']

            user.save()
            return True, f"L'utilisateur {user.username} a été modifié avec succès."

        except User.DoesNotExist:
            return False, "Utilisateur non trouvé."
        except Exception as e:
            return False, f"Erreur lors de la modification: {str(e)}"

    def ajouter(self, username, email, mdp, privilege='user'):
        """Ajoute un nouvel utilisateur

        Args:
            username: Nom d'utilisateur
            email: Adresse email
            mdp: Mot de passe
            privilege: Niveau de privilège (default: 'user')

        Returns:
            tuple: (success, message, user_id)
        """
        try:
            # Vérifier si le nom d'utilisateur est déjà pris
            if User.objects.filter(username=username).exists():
                return False, "Ce nom d'utilisateur est déjà utilisé.", None

            # Vérifier si l'email est déjà pris
            if User.objects.filter(email=email).exists():
                return False, "Cet email est déjà utilisé.", None

            # Vérifier que le privilège est valide
            if privilege not in ['user', 'admin']:
                return False, "Privilège non valide. Utilisez 'user' ou 'admin'.", None

            # Créer le nouvel utilisateur
            user = User(
                username=username,
                email=email,
                mdp=mdp,  # Dans un cas réel, il faudrait hasher le mot de passe
                privilege=privilege,

            )
            user.save()

            return True, f"L'utilisateur {username} a été créé avec succès.", user.id

        except Exception as e:
            return False, f"Erreur lors de la création: {str(e)}", None

    def save(self, *args, **kwargs):
        """Surcharge de la méthode save pour définir le privilège à 'admin'"""
        self.privilege = "admin"
        super().save(*args, **kwargs)

class Conversation(models.Model):
    """Représente une conversation entre un utilisateur et le chatbot"""
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    date = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversations', null=True, blank=True)
    auth_user = models.ForeignKey(AuthUser, on_delete=models.CASCADE, related_name='conversations', null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True,
                                 help_text="Identifiant de session pour les utilisateurs non connectés")

    def __str__(self):
        return f"{self.title} - {self.date.strftime('%d/%m/%Y')}"

    class Meta:
        ordering = ['-date']

class Message(models.Model):
    """Représente un message dans une conversation"""
    id = models.AutoField(primary_key=True)
    content = models.TextField()
    role = models.CharField(max_length=10, choices=[('user', 'Utilisateur'), ('bot', 'Chatbot')])
    time = models.DateTimeField(auto_now_add=True)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='messages_sent')
    auth_user = models.ForeignKey(AuthUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='messages_sent')

    def __str__(self):
        return f"{self.role}: {self.content[:50]}... - {self.time.strftime('%d/%m/%Y %H:%M')}"

    class Meta:
        ordering = ['time']

class Chatbot(models.Model):
    """Représente le chatbot et ses fonctionnalités"""
    name = models.CharField(max_length=100, default="Maroc Cultures Assistant")
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def generate_title(self, conversation_content):
        """Génère un titre pour une conversation basé sur son contenu"""
        # Logique simple pour générer un titre
        words = conversation_content.split()[:5]
        title = " ".join(words) + "..."
        return title

    def __str__(self):
        return self.name

class Historique(models.Model):
    """Représente l'historique des conversations pour chaque utilisateur"""
    id_conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='historique_entries')
    id_utilisateur = models.ForeignKey(AuthUser, on_delete=models.CASCADE, related_name='historique', null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True, null=True,
                                 help_text="Identifiant de session pour les utilisateurs non connectés")
    date_acces = models.DateTimeField(auto_now=True, verbose_name="Dernière consultation")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_display = f"User {self.id_utilisateur.id}" if self.id_utilisateur else f"Session {self.session_id[:8]}..."
        return f"Historique {user_display} - Conv {self.id_conversation.id}"

    class Meta:
        ordering = ['-date_acces']
        unique_together = ['id_conversation', 'id_utilisateur', 'session_id']

class Event(models.Model):
    """Représente un événement culturel"""
    title = models.CharField(max_length=200, verbose_name="Titre")
    description = models.TextField(verbose_name="Description")
    date_start = models.DateField(verbose_name="Date de début")
    date_end = models.DateField(verbose_name="Date de fin", null=True, blank=True)
    time = models.TimeField(verbose_name="Heure", null=True, blank=True)
    location = models.CharField(max_length=200, verbose_name="Lieu")
    image_url = models.URLField(verbose_name="URL de l'image", null=True, blank=True)
    registration_url = models.URLField(verbose_name="URL d'inscription", null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def get_absolute_url(self):
        return reverse('event_detail', args=[str(self.id)])

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['date_start']
