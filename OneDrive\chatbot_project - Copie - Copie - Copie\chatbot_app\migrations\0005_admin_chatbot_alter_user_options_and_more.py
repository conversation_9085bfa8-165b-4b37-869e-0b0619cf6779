# Generated by Django 5.2.1 on 2025-05-22 10:59

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot_app', '0004_chatmessage'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Admin',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='chatbot_app.user')),
            ],
            bases=('chatbot_app.user',),
        ),
        migrations.CreateModel(
            name='Chatbot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Maroc Cultures Assistant', max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['-acd']},
        ),
        migrations.RemoveField(
            model_name='user',
            name='date_joined',
        ),
        migrations.RemoveField(
            model_name='user',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='user',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_login',
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_name',
        ),
        migrations.AddField(
            model_name='user',
            name='acd',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='date de création'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='img',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='image de profil'),
        ),
        migrations.AddField(
            model_name='user',
            name='mdp',
            field=models.CharField(default='default_password', max_length=128, verbose_name='mot de passe'),
        ),
        migrations.AddField(
            model_name='user',
            name='privilege',
            field=models.CharField(default='user', max_length=20),
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('session_id', models.CharField(blank=True, help_text='Identifiant de session pour les utilisateurs non connectés', max_length=100, null=True)),
                ('auth_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to='chatbot_app.user')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('role', models.CharField(choices=[('user', 'Utilisateur'), ('bot', 'Chatbot')], max_length=10)),
                ('time', models.DateTimeField(auto_now_add=True)),
                ('auth_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages_sent', to=settings.AUTH_USER_MODEL)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='chatbot_app.conversation')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='messages_sent', to='chatbot_app.user')),
            ],
            options={
                'ordering': ['time'],
            },
        ),
        migrations.DeleteModel(
            name='ChatMessage',
        ),
    ]
