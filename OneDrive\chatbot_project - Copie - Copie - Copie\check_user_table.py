import pymysql

try:
    # Connexion à MySQL
    connection = pymysql.connect(
        host='127.0.0.1',
        user='root',
        password='',
        database='maroc_cultures_db',
        port=3306
    )
    
    print("✅ Connexion réussie à MySQL!")
    
    # Création d'un curseur
    cursor = connection.cursor()
    
    # Vérification de la structure de la table auth_user
    print("\n📋 Structure de la table auth_user (table de connexion des utilisateurs) :")
    cursor.execute("DESCRIBE auth_user")
    columns = cursor.fetchall()
    for column in columns:
        print(f"- {column[0]}: {column[1]}")
    
    # Vérification des utilisateurs existants
    print("\n👥 Utilisateurs existants dans la table auth_user :")
    cursor.execute("SELECT id, username, email, is_superuser, is_staff, is_active FROM auth_user")
    users = cursor.fetchall()
    if users:
        for user in users:
            user_id, username, email, is_superuser, is_staff, is_active = user
            print(f"- ID: {user_id}, Nom d'utilisateur: {username}, Email: {email}")
            print(f"  Statut: {'Superutilisateur' if is_superuser else 'Utilisateur normal'}, {'Staff' if is_staff else 'Non-staff'}, {'Actif' if is_active else 'Inactif'}")
    else:
        print("Aucun utilisateur trouvé dans la table auth_user")
    
    # Vérification de la table chatbot_app_user (si elle existe)
    print("\n📋 Vérification de la table chatbot_app_user (table utilisateur personnalisée) :")
    cursor.execute("DESCRIBE chatbot_app_user")
    columns = cursor.fetchall()
    for column in columns:
        print(f"- {column[0]}: {column[1]}")
    
    # Vérification des utilisateurs existants dans la table personnalisée
    print("\n👥 Utilisateurs existants dans la table chatbot_app_user :")
    cursor.execute("SELECT id, username, email, first_name, last_name, is_active FROM chatbot_app_user")
    users = cursor.fetchall()
    if users:
        for user in users:
            user_id, username, email, first_name, last_name, is_active = user
            print(f"- ID: {user_id}, Nom d'utilisateur: {username}, Email: {email}")
            print(f"  Nom: {first_name} {last_name}, {'Actif' if is_active else 'Inactif'}")
    else:
        print("Aucun utilisateur trouvé dans la table chatbot_app_user")
    
    # Fermeture de la connexion
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ Erreur: {e}")
