#!/usr/bin/env python3
"""
Script de test de connexion MySQL pour le projet Chatbot Maroc Cultures
Ce script teste la connexion à la base de données MySQL configurée.
"""

import os
import sys
import django
from dotenv import load_dotenv

# Ajouter le répertoire du projet au path Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Charger les variables d'environnement
load_dotenv()

# Configurer Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.db import connection
from django.core.management.color import make_style

style = make_style()

def test_database_connection():
    """Teste la connexion à la base de données Django"""
    print("🔍 Test de connexion à la base de données...")
    print("=" * 50)
    
    try:
        # Obtenir les informations de configuration
        db_config = connection.settings_dict
        
        print("📋 Configuration de la base de données:")
        print(f"  Engine: {db_config.get('ENGINE', 'Non défini')}")
        print(f"  Name: {db_config.get('NAME', 'Non défini')}")
        print(f"  User: {db_config.get('USER', 'Non défini')}")
        print(f"  Host: {db_config.get('HOST', 'Non défini')}")
        print(f"  Port: {db_config.get('PORT', 'Non défini')}")
        print()
        
        # Tester la connexion
        print("🔄 Test de connexion en cours...")
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        if result and result[0] == 1:
            print(style.SUCCESS("✅ Connexion à la base de données réussie!"))
            
            # Obtenir des informations sur la base de données
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"📊 Version MySQL: {version}")
                
                cursor.execute("SELECT DATABASE()")
                current_db = cursor.fetchone()[0]
                print(f"🗄️  Base de données actuelle: {current_db}")
                
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"📋 Nombre de tables: {len(tables)}")
                
                if tables:
                    print("📝 Tables existantes:")
                    for table in tables:
                        print(f"  - {table[0]}")
                else:
                    print("ℹ️  Aucune table trouvée (normal si les migrations n'ont pas encore été exécutées)")
            
            return True
            
    except Exception as e:
        print(style.ERROR(f"❌ Erreur de connexion: {e}"))
        print()
        print("🔧 Suggestions de dépannage:")
        print("  1. Vérifiez que MySQL est démarré")
        print("  2. Vérifiez les paramètres dans le fichier .env")
        print("  3. Vérifiez que la base de données existe")
        print("  4. Vérifiez les permissions de l'utilisateur MySQL")
        return False

def test_environment_variables():
    """Teste les variables d'environnement"""
    print("🔍 Vérification des variables d'environnement...")
    print("=" * 50)
    
    required_vars = ['DB_ENGINE', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Masquer le mot de passe pour la sécurité
            display_value = "***" if var == 'DB_PASSWORD' else value
            print(f"  {var}: {display_value}")
        else:
            missing_vars.append(var)
            print(f"  {var}: ❌ Non défini")
    
    if missing_vars:
        print(style.ERROR(f"\n❌ Variables manquantes: {', '.join(missing_vars)}"))
        return False
    else:
        print(style.SUCCESS("\n✅ Toutes les variables d'environnement sont définies"))
        return True

def check_mysql_packages():
    """Vérifie que les packages MySQL sont installés"""
    print("🔍 Vérification des packages MySQL...")
    print("=" * 50)
    
    packages_to_check = ['mysql.connector', 'MySQLdb', 'pymysql']
    available_packages = []
    
    for package in packages_to_check:
        try:
            __import__(package)
            available_packages.append(package)
            print(f"  ✅ {package}: Disponible")
        except ImportError:
            print(f"  ❌ {package}: Non disponible")
    
    if available_packages:
        print(style.SUCCESS(f"\n✅ Packages MySQL disponibles: {', '.join(available_packages)}"))
        return True
    else:
        print(style.ERROR("\n❌ Aucun package MySQL trouvé"))
        print("💡 Installez un package MySQL avec:")
        print("  pip install mysqlclient")
        print("  ou")
        print("  pip install PyMySQL")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test de connexion MySQL - Chatbot Maroc Cultures")
    print("=" * 60)
    print()
    
    # Vérifier les variables d'environnement
    env_ok = test_environment_variables()
    print()
    
    # Vérifier les packages MySQL
    packages_ok = check_mysql_packages()
    print()
    
    # Tester la connexion à la base de données
    if env_ok and packages_ok:
        db_ok = test_database_connection()
        print()
        
        if db_ok:
            print("🎉 Tous les tests sont passés avec succès!")
            print("Vous pouvez maintenant exécuter les migrations Django:")
            print("  python manage.py makemigrations")
            print("  python manage.py migrate")
        else:
            print("❌ Le test de connexion a échoué. Vérifiez la configuration.")
    else:
        print("❌ Configuration incomplète. Corrigez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
