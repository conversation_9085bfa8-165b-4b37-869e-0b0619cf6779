#!/usr/bin/env python3
"""
🧪 Test Simple de l'API Mistral
"""

import requests
import os

def test_mistral_direct():
    """Test direct de l'API Mistral"""
    print("🧪 Test direct de l'API Mistral...")
    
    api_key = "UkKBKDAhhOhZwyAH2RAi76p2vCXT3txv"
    url = "https://api.mistral.ai/v1/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    data = {
        "model": "mistral-small-latest",
        "messages": [
            {
                "role": "system", 
                "content": "Tu es un assistant culturel expert du Maroc. Réponds en français de manière chaleureuse."
            },
            {
                "role": "user", 
                "content": "Parle-moi du tajine marocain"
            }
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    try:
        print("📤 Envoi de la requête...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            bot_response = result['choices'][0]['message']['content']
            print(f"✅ Réponse reçue:")
            print(f"🤖 {bot_response}")
            return True
        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_mistral_with_django():
    """Test avec le module Django"""
    print("\n🧪 Test avec le module Django...")
    
    try:
        import sys
        import os
        import django
        
        # Configuration Django
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
        django.setup()
        
        from chatbot_app.mistral_api import get_mistral_response
        
        print("📤 Test de get_mistral_response...")
        response = get_mistral_response("Parle-moi du couscous marocain")
        
        print(f"✅ Réponse Django:")
        print(f"🤖 {response}")
        
        if "Erreur" in response or "erreur" in response:
            return False
        else:
            return True
            
    except Exception as e:
        print(f"❌ Erreur Django: {e}")
        return False

def main():
    """Fonction principale"""
    print("🇲🇦 Test de l'API Mistral - Maroc Cultures")
    print("=" * 50)
    
    # Test 1: API directe
    success1 = test_mistral_direct()
    
    # Test 2: Module Django
    success2 = test_mistral_with_django()
    
    print("\n" + "="*50)
    print("📊 RÉSULTATS:")
    print(f"🔗 API Directe: {'✅ OK' if success1 else '❌ ÉCHEC'}")
    print(f"🐍 Module Django: {'✅ OK' if success2 else '❌ ÉCHEC'}")
    
    if success1 and success2:
        print("\n🎉 Mistral AI fonctionne parfaitement !")
        print("💡 Le problème vient peut-être d'ailleurs dans le code.")
    elif success1 and not success2:
        print("\n⚠️  L'API Mistral fonctionne mais pas l'intégration Django.")
        print("💡 Vérifiez le fichier mistral_api.py")
    elif not success1:
        print("\n❌ L'API Mistral ne fonctionne pas.")
        print("💡 Vérifiez votre clé API et votre connexion internet.")
    
    print("\n🔧 SOLUTIONS POSSIBLES:")
    print("1. Vérifiez votre connexion internet")
    print("2. Vérifiez que la clé API est correcte")
    print("3. Vérifiez les logs du serveur Django")
    print("4. Testez avec une question simple")

if __name__ == "__main__":
    main()
