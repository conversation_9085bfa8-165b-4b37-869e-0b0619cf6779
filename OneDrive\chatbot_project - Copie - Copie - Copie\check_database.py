#!/usr/bin/env python
"""
Script pour vérifier le contenu de la base de données
"""
import pymysql

def check_database():
    """Vérifier le contenu de la base de données"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔍 Vérification de la base de données")
        print("=" * 50)
        
        # Compter les conversations
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count = cursor.fetchone()[0]
        print(f"📊 Conversations en base: {conv_count}")
        
        # Compter les messages
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count = cursor.fetchone()[0]
        print(f"📊 Messages en base: {msg_count}")
        
        # Afficher les conversations
        cursor.execute("""
            SELECT id, title, date, session_id, auth_user_id 
            FROM chatbot_app_conversation 
            ORDER BY date DESC 
            LIMIT 10
        """)
        conversations = cursor.fetchall()
        
        print(f"\n📋 Conversations récentes:")
        for conv in conversations:
            conv_id, title, date, session_id, auth_user_id = conv
            session_display = session_id[:8] + "..." if session_id else "N/A"
            user_display = f"User {auth_user_id}" if auth_user_id else "Anonyme"
            print(f"   ID: {conv_id} | {title} | {date} | Session: {session_display} | {user_display}")
        
        # Afficher les messages
        cursor.execute("""
            SELECT m.content, m.role, m.conversation_id, m.time, c.title
            FROM chatbot_app_message m
            LEFT JOIN chatbot_app_conversation c ON m.conversation_id = c.id
            ORDER BY m.time DESC 
            LIMIT 20
        """)
        messages = cursor.fetchall()
        
        print(f"\n💬 Messages récents:")
        for msg in messages:
            content, role, conv_id, time, conv_title = msg
            content_short = content[:40] + "..." if len(content) > 40 else content
            print(f"   {role.upper()}: {content_short} | Conv: {conv_id} ({conv_title}) | {time}")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ Vérification terminée !")
        
        if conv_count > 0 and msg_count > 0:
            print(f"🎉 L'historique devrait maintenant s'afficher sur la page web !")
        else:
            print(f"⚠️ Aucune donnée trouvée. Essayez d'envoyer quelques messages via le chatbot.")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    check_database()
