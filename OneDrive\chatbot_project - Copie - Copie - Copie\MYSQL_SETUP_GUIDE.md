# Guide de Configuration MySQL pour Chatbot Maroc Cultures

Ce guide vous aidera à configurer MySQL comme base de données pour votre projet Django.

## Prérequis

1. **MySQL Server installé** sur votre système
2. **Python 3.13** avec pip
3. **Accès administrateur** à MySQL

## Étape 1: Installation de MySQL (si pas déjà fait)

### Windows:
1. Téléchargez MySQL Community Server depuis https://dev.mysql.com/downloads/mysql/
2. Installez MySQL avec MySQL Workbench
3. Notez le mot de passe root que vous définissez

### Alternative avec XAMPP:
1. Téléchargez XAMPP depuis https://www.apachefriends.org/
2. Installez XAMPP et démarrez MySQL depuis le panneau de contrôle

## Étape 2: Configuration des Variables d'Environnement

Modifiez le fichier `.env` avec vos paramètres MySQL:

```env
# MySQL Database Configuration
DB_ENGINE=mysql
DB_NAME=chatbot_maroc_cultures
DB_USER=root
DB_PASSWORD=votre_mot_de_passe_mysql
DB_HOST=localhost
DB_PORT=3306
```

**Important:** Remplacez `votre_mot_de_passe_mysql` par votre vrai mot de passe MySQL.

## Étape 3: Installation des Dépendances Python

1. Activez votre environnement virtuel:
```bash
# Windows
venv\Scripts\activate

# Ou si vous utilisez venv_new
venv_new\Scripts\activate
```

2. Installez les nouvelles dépendances:
```bash
pip install -r requirements_new.txt
```

## Étape 4: Configuration Automatique avec le Script

Exécutez le script de configuration automatique:

```bash
python setup_mysql.py
```

Ce script va:
- Tester la connexion MySQL
- Créer la base de données automatiquement
- Exécuter les migrations Django
- Proposer de créer un superutilisateur

## Étape 5: Configuration Manuelle (Alternative)

Si le script automatique ne fonctionne pas, suivez ces étapes manuelles:

### 5.1 Créer la Base de Données

Connectez-vous à MySQL et créez la base de données:

```sql
mysql -u root -p
CREATE DATABASE chatbot_maroc_cultures CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
SHOW DATABASES;
EXIT;
```

### 5.2 Exécuter les Migrations Django

```bash
python manage.py makemigrations
python manage.py migrate
```

### 5.3 Créer un Superutilisateur

```bash
python manage.py createsuperuser
```

## Étape 6: Vérification

1. Démarrez le serveur Django:
```bash
python manage.py runserver
```

2. Visitez http://127.0.0.1:8000/ pour voir la landing page
3. Visitez http://127.0.0.1:8000/admin/ pour l'interface d'administration

## Dépannage

### Erreur: "No module named 'MySQLdb'"
```bash
pip install mysqlclient
```

### Erreur: "Access denied for user"
- Vérifiez le mot de passe dans le fichier `.env`
- Assurez-vous que MySQL est démarré

### Erreur: "Can't connect to MySQL server"
- Vérifiez que MySQL est en cours d'exécution
- Vérifiez le port (3306 par défaut)

### Erreur de compilation mysqlclient sur Windows
Installez Microsoft C++ Build Tools ou utilisez:
```bash
pip install PyMySQL
```

Puis ajoutez dans `settings.py`:
```python
import pymysql
pymysql.install_as_MySQLdb()
```

## Migration depuis SQLite

Si vous avez des données existantes dans SQLite:

1. Exportez les données:
```bash
python manage.py dumpdata > data_backup.json
```

2. Configurez MySQL et exécutez les migrations

3. Importez les données:
```bash
python manage.py loaddata data_backup.json
```

## Fichiers Modifiés

- ✅ `.env` - Configuration MySQL ajoutée
- ✅ `settings.py` - Support MySQL configuré
- ✅ `requirements_new.txt` - Dépendances MySQL incluses
- ✅ `setup_mysql.py` - Script de configuration automatique

## Support

Si vous rencontrez des problèmes:
1. Vérifiez que MySQL est installé et en cours d'exécution
2. Vérifiez les paramètres dans le fichier `.env`
3. Consultez les logs d'erreur Django pour plus de détails
