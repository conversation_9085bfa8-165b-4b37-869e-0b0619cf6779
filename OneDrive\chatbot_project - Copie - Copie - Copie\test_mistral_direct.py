#!/usr/bin/env python3
"""
🧪 Test Direct de l'API Mistral - Sans Django
"""

import requests
import json

def test_mistral_api():
    """Test direct de l'API Mistral"""
    print("🧪 Test direct de l'API Mistral...")

    api_key = "TyWSqM7VMMzjBygUrTeNS0SuZicudsD2"
    url = "https://api.mistral.ai/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    # Prompt système spécialisé pour Maroc Cultures
    system_prompt = """Tu es un assistant culturel expert du Maroc, représentant l'organisation "Maroc Cultures".

🇲🇦 TON RÔLE :
- Expert en culture, histoire, traditions, gastronomie, et patrimoine marocain
- Guide touristique virtuel pour le Maroc
- Ambassadeur de la richesse culturelle marocaine

🎯 STYLE DE RÉPONSE :
- Chaleureux et accueillant
- Informatif mais accessible
- Utilise des émojis appropriés
- Encourage la découverte du Maroc
- Réponds en français principalement

⚠️ LIMITES :
- Ne réponds QUE aux questions sur le Maroc et sa culture
- Si la question n'est pas liée au Maroc, redirige poliment vers la culture marocaine
- Reste factuel et respectueux des traditions"""

    data = {
        "model": "mistral-small-latest",
        "messages": [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": "Bonjour, parle-moi du tajine marocain"
            }
        ],
        "max_tokens": 300,
        "temperature": 0.7,
        "top_p": 0.9,
        "stream": False
    }

    try:
        print("📤 Envoi de la requête à Mistral AI...")
        print(f"🔗 URL: {url}")
        print(f"🔑 API Key: {api_key[:10]}...")

        response = requests.post(url, headers=headers, json=data, timeout=30)

        print(f"📊 Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            bot_response = result['choices'][0]['message']['content'].strip()

            print(f"✅ SUCCÈS ! Réponse reçue:")
            print("=" * 60)
            print(f"🤖 {bot_response}")
            print("=" * 60)

            # Ajouter signature Maroc Cultures
            if len(bot_response) > 50:
                bot_response += "\n\n🇲🇦 *Maroc Cultures - Votre guide culturel du Maroc*"

            print(f"\n📝 Réponse finale avec signature:")
            print("=" * 60)
            print(f"🤖 {bot_response}")
            print("=" * 60)

            return True, bot_response

        elif response.status_code == 401:
            print("❌ ERREUR 401: Clé API invalide")
            return False, "🔑 Erreur d'authentification API. Veuillez vérifier la clé Mistral."
        elif response.status_code == 429:
            print("❌ ERREUR 429: Trop de requêtes")
            return False, "⏳ Trop de requêtes. Veuillez patienter un moment avant de réessayer."
        else:
            print(f"❌ ERREUR {response.status_code}: {response.text}")
            return False, f"❌ Erreur API ({response.status_code}). Veuillez réessayer plus tard."

    except requests.exceptions.Timeout:
        print("❌ TIMEOUT: Délai d'attente dépassé")
        return False, "⏰ Délai d'attente dépassé. Veuillez réessayer."
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Problème de connexion")
        return False, "🌐 Problème de connexion. Vérifiez votre connexion internet."
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False, f"🔧 Une erreur technique s'est produite: {e}"

def test_multiple_questions():
    """Test avec plusieurs questions"""
    questions = [
        "Parle-moi du tajine marocain",
        "Quelle est l'histoire de Marrakech ?",
        "Qu'est-ce que la musique gnawa ?",
        "Comment programmer en Python ?"  # Question non-culturelle pour tester la redirection
    ]

    print("\n🧪 Test avec plusieurs questions...")

    for i, question in enumerate(questions, 1):
        print(f"\n📝 Question {i}: {question}")
        success, response = test_single_question(question)

        if success:
            print(f"✅ Réponse: {response[:100]}...")
        else:
            print(f"❌ Échec: {response}")

def test_single_question(question):
    """Test une seule question"""
    api_key = "TyWSqM7VMMzjBygUrTeNS0SuZicudsD2"
    url = "https://api.mistral.ai/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    system_prompt = """Tu es un assistant culturel expert du Maroc. Réponds en français de manière chaleureuse sur la culture marocaine."""

    data = {
        "model": "mistral-small-latest",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question}
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            bot_response = result['choices'][0]['message']['content'].strip()
            return True, bot_response
        else:
            return False, f"Erreur {response.status_code}"

    except Exception as e:
        return False, str(e)

def main():
    """Fonction principale"""
    print("🇲🇦 Test Direct de l'API Mistral - Maroc Cultures")
    print("=" * 60)

    # Test principal
    success, response = test_mistral_api()

    if success:
        print("\n🎉 L'API Mistral fonctionne parfaitement !")
        print("💡 Le chatbot devrait maintenant utiliser Mistral AI.")

        # Test avec plusieurs questions
        test_multiple_questions()

    else:
        print("\n❌ L'API Mistral ne fonctionne pas.")
        print("💡 Vérifications à faire :")
        print("1. Connexion internet")
        print("2. Clé API valide")
        print("3. Quota API non dépassé")

    print("\n🔧 PROCHAINES ÉTAPES :")
    print("1. Si ce test fonctionne, le problème vient du code Django")
    print("2. Vérifiez les logs du serveur Django")
    print("3. Testez une question dans le chat web")
    print("4. Regardez la console du navigateur (F12)")

if __name__ == "__main__":
    main()
