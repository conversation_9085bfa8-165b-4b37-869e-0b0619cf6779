{% extends 'chatbot_app/base.html' %}

{% block title %}Connexion - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes floatStar {
        0% {
            transform: translateY(0) rotate(0deg);
        }
        50% {
            transform: translateY(-10px) rotate(180deg);
        }
        100% {
            transform: translateY(0) rotate(360deg);
        }
    }

    /* Login Page Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    /* Main layout container */
    .page-container {
        display: flex;
        min-height: calc(100vh - 60px); /* Adjust for navbar height */
        position: relative;
    }

    /* Content area (main area) */
    .content-area {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 20px;
    }

    /* Login form container */
    .login-container {
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
        animation: fadeIn 0.8s ease-out;
        z-index: 10; /* Ensure form is above other elements */
    }

    .login-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        position: relative;
    }

    .login-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 25px 20px;
        text-align: center;
        position: relative;
    }

    .login-header h2 {
        font-size: 1.6rem;
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .login-header h2 i {
        margin-right: 10px;
    }

    .login-header p {
        font-size: 1rem;
        opacity: 0.95;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .login-body {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 22px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 0.95rem;
    }

    .form-control {
        width: 100%;
        height: 48px;
        padding: 10px 15px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #f5f7fa;
        transition: all 0.3s;
        font-size: 1rem;
    }

    .form-control:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        outline: none;
        background-color: white;
    }

    .input-group {
        position: relative;
    }

    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #888;
        cursor: pointer;
        font-size: 1rem;
        z-index: 5;
    }

    .password-toggle:hover {
        color: #27ae60;
    }

    .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        font-size: 0.95rem;
        flex-wrap: wrap;
        gap: 10px;
    }

    .remember-me {
        display: flex;
        align-items: center;
    }

    .remember-me input {
        margin-right: 8px;
        cursor: pointer;
        width: 16px;
        height: 16px;
    }

    .remember-me label {
        cursor: pointer;
        user-select: none;
        color: #555;
    }

    .forgot-password {
        color: #c0392b;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s;
    }

    .forgot-password:hover {
        color: #a93226;
        text-decoration: underline;
    }

    .login-btn {
        display: flex;
        width: 100%;
        background-color: #27ae60;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 14px;
        font-size: 1.05rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        align-items: center;
        justify-content: center;
    }

    .login-btn:hover {
        background-color: #219653;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(39, 174, 96, 0.2);
    }

    .login-btn i {
        margin-right: 10px;
    }

    .register-section {
        text-align: center;
        margin-top: 25px;
        font-size: 0.95rem;
        color: #555;
    }

    .register-link {
        color: #27ae60;
        text-decoration: none;
        font-weight: 500;
    }

    .register-link:hover {
        text-decoration: underline;
    }

    .login-footer {
        padding: 18px;
        text-align: center;
        border-top: 1px solid #eee;
        background-color: #f8f9fa;
        font-size: 0.95rem;
    }

    /* Floating stars */
    .star {
        position: absolute;
        opacity: 0.15;
        z-index: 1;
        animation: floatStar 15s infinite linear;
    }

    .star:nth-child(1) {
        top: 15%;
        left: 10%;
        font-size: 18px;
        color: #c0392b;
        animation-duration: 20s;
    }

    .star:nth-child(2) {
        top: 25%;
        right: 15%;
        font-size: 16px;
        color: #27ae60;
        animation-duration: 25s;
        animation-delay: 2s;
    }

    .star:nth-child(3) {
        bottom: 20%;
        left: 20%;
        font-size: 20px;
        color: #c0392b;
        animation-duration: 18s;
        animation-delay: 5s;
    }

    .star:nth-child(4) {
        bottom: 35%;
        right: 20%;
        font-size: 17px;
        color: #27ae60;
        animation-duration: 22s;
        animation-delay: 1s;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .page-container {
            flex-direction: column;
        }

        .content-area {
            padding: 30px 15px;
        }
    }

    @media (max-width: 768px) {
        .login-container {
            max-width: 100%;
        }

        .login-header h2 {
            font-size: 1.4rem;
        }

        .login-header p {
            font-size: 0.9rem;
        }

        .login-options {
            flex-direction: column;
            align-items: flex-start;
        }

        .forgot-password {
            margin-top: 8px;
        }
    }

    @media (max-width: 576px) {
        .login-body {
            padding: 20px 15px;
        }

        .form-control {
            height: 45px;
            font-size: 0.95rem;
        }

        .login-btn {
            padding: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="content-area">
        <div class="login-container">
            <!-- Étoiles flottantes -->
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>
            <div class="star"><i class="fas fa-star"></i></div>

            <div class="login-card">
                <div class="login-header">
                    <h2><i class="fas fa-users"></i>Connexion</h2>
                    <p>Entrez vos identifiants pour accéder à votre compte</p>
                </div>

                <div class="login-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="username" class="form-label">Nom d'utilisateur</label>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Entrez votre nom d'utilisateur" required>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Mot de passe</label>
                            <div class="password-field">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Entrez votre mot de passe" required>
                                <span class="password-toggle" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </span>
                            </div>
                        </div>

                        <div class="login-options">
                            <div class="remember-me">
                                <input type="checkbox" id="remember" name="remember">
                                <label for="remember">Se souvenir de moi</label>
                            </div>
                            <a href="#" class="forgot-password">Mot de passe oublié?</a>
                        </div>

                        <button type="submit" class="login-btn">
                            <i class="fas fa-sign-in-alt"></i>Se connecter
                        </button>
                    </form>

                    <div class="register-section">
                        <p>Pas encore de compte? <a href="{% url 'register' %}" class="register-link">Inscrivez-vous maintenant</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Fonction pour afficher/masquer le mot de passe
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Ajuster la hauteur de la page pour éviter les chevauchements
document.addEventListener('DOMContentLoaded', function() {
    function adjustHeight() {
        const navbar = document.querySelector('.navbar');
        const footer = document.querySelector('footer');
        const pageContainer = document.querySelector('.page-container');

        if (navbar && footer && pageContainer) {
            const navbarHeight = navbar.offsetHeight;
            const footerHeight = footer.offsetHeight;
            const windowHeight = window.innerHeight;

            // Ajuster la hauteur minimale du conteneur principal
            pageContainer.style.minHeight = (windowHeight - navbarHeight - footerHeight) + 'px';
        }
    }

    // Appeler la fonction au chargement et au redimensionnement
    adjustHeight();
    window.addEventListener('resize', adjustHeight);
});
</script>
{% endblock %}
