# 📋 CAHIER DES CHARGES - APPLICATION MAROC CULTURES

## 🎯 **1. PRÉSENTATION DU PROJET**

### **1.1 Contexte**
- **Nom du projet** : Maroc Cultures - Plateforme Culturelle Interactive
- **Organisation** : Association Maroc Cultures
- **Date de création** : 2001
- **Mission** : Promouvoir la culture marocaine à travers des événements artistiques

### **1.2 Objectifs**
- Créer une plateforme web interactive pour promouvoir la culture marocaine
- Fournir un chatbot intelligent spécialisé dans la culture du Maroc
- Gérer les événements culturels et festivals
- Faciliter l'interaction avec le public et les artistes

---

## 🏗️ **2. ARCHITECTURE TECHNIQUE**

### **2.1 Technologies Utilisées**
- **Backend** : Django 5.2.1 (Python)
- **Base de données** : MySQL
- **Frontend** : HTML5, CSS3, JavaScript
- **Intelligence Artificielle** : API Mistral AI
- **Serveur** : Django Development Server
- **Environnement** : Python Virtual Environment

### **2.2 Structure du Projet**
```
chatbot_project/
├── chatbot_app/          # Application principale
├── static/               # Fichiers statiques (CSS, JS, images)
├── templates/            # Templates HTML
├── venv/                 # Environnement virtuel
├── manage.py             # Gestionnaire Django
└── requirements.txt      # Dépendances
```

---

## 🎨 **3. DESIGN ET INTERFACE**

### **3.1 Charte Graphique**
- **Couleurs principales** : Rouge et vert (couleurs du drapeau marocain)
- **Couleur des boutons** : Vert (#1abc9c)
- **Couleur des titres** : #1abc9c
- **Couleurs des sections** : Rouge et vert en dégradé
- **Style** : Moderne, responsive, culturellement approprié

### **3.2 Éléments Visuels**
- **Étoiles flottantes** sur la page de connexion
- **Dégradés rouge-vert** pour les en-têtes
- **Interface responsive** (Flexbox/Grid)
- **Tailles compactes** pour une meilleure ergonomie

---

## 📱 **4. FONCTIONNALITÉS**

### **4.1 Pages Principales**

#### **4.1.1 Page d'Accueil (home.html)**
- **URL** : `/home/<USER>
- **Description** : Landing page professionnelle
- **Contenu** :
  - Présentation de Maroc Cultures
  - Sections : Nos Services, À Propos, Événements, Témoignages
  - Bouton de chat en bas à droite
  - Footer avec informations de contact

#### **4.1.2 Chatbot Intelligent**
- **URL** : `/chat/`
- **Description** : Interface de chat avec IA
- **Fonctionnalités** :
  - Système hybride : JSON → Mistral AI → Fallback
  - Réponses spécialisées culture marocaine
  - Historique des conversations
  - Interface responsive et moderne

#### **4.1.3 Gestion des Événements**
- **URL** : `/events/`
- **Description** : Liste et détails des événements
- **Contenu** :
  - Festival Mawazine
  - Festival du Théâtre des Cultures
  - Génération Mawazine
  - Événements à venir

#### **4.1.4 Administration**
- **URL** : `/admin-maroc-cultures/dashboard/`
- **Description** : Interface d'administration
- **Fonctionnalités** :
  - Gestion des utilisateurs
  - Gestion des événements
  - Statistiques et monitoring

### **4.2 Système d'Authentification**

#### **4.2.1 Inscription**
- **URL** : `/register/`
- **Champs** : Nom, email, mot de passe
- **Design** : Style login avec étoiles flottantes

#### **4.2.2 Connexion**
- **URL** : `/login/`
- **Design** : Dégradé rouge-vert, étoiles flottantes, boutons verts

---

## 🤖 **5. CHATBOT INTELLIGENT**

### **5.1 Architecture du Chatbot**

#### **5.1.1 Système Hybride à 3 Niveaux**
1. **Niveau 1 : Réponses JSON**
   - Fichier : `faq_data.json`
   - Réponses rapides et contrôlées
   - Score adaptatif : 1 pour un mot, 2 pour plusieurs mots

2. **Niveau 2 : API Mistral AI**
   - Clé API : `TyWSqM7VMMzjBygUrTeNS0SuZicudsD2`
   - Modèle : `mistral-small-latest`
   - Spécialisé culture marocaine

3. **Niveau 3 : Réponses Prédéfinies**
   - 16 mots-clés couverts
   - Fallback robuste

### **5.2 Spécialisation Culturelle**
- **Domaines d'expertise** :
  - Histoire du Maroc
  - Gastronomie (tajine, couscous, pâtisseries)
  - Artisanat (tapis, poterie, bijoux)
  - Musique (chaâbi, gnawa, andalou)
  - Villes impériales (Fès, Marrakech, Meknès, Rabat)
  - Festivals et événements culturels

### **5.3 Fonctionnalités Avancées**
- **Contexte de conversation** : Mémorise 10 derniers échanges
- **Gestion d'erreurs** : Triple fallback
- **Logs détaillés** : Monitoring en temps réel
- **Réponses naturelles** : En français, chaleureuses

---

## 💾 **6. BASE DE DONNÉES**

### **6.1 Configuration**
- **SGBD** : MySQL
- **Host** : localhost
- **Port** : 3306
- **Base** : `chatbot_db`
- **Utilisateur** : `root`
- **Mot de passe** : (vide)

### **6.2 Modèles Principaux**
- **User** : Utilisateurs de l'application
- **Conversation** : Historique des conversations
- **Event** : Événements culturels
- **Historique** : Logs des interactions
- **Admin** : Administrateurs du système

---

## 🔧 **7. CONFIGURATION TECHNIQUE**

### **7.1 Variables d'Environnement (.env)**
```env
# Base de données
DB_NAME=chatbot_db
DB_USER=root
DB_PASSWORD=
DB_HOST=localhost
DB_PORT=3306

# API Mistral
MISTRAL_API_KEY=TyWSqM7VMMzjBygUrTeNS0SuZicudsD2
MISTRAL_MODEL=mistral-small-latest

# Django
SECRET_KEY=[clé secrète Django]
DEBUG=True
```

### **7.2 Dépendances (requirements.txt)**
- Django==5.2.1
- PyMySQL==1.1.1
- requests==2.32.3
- python-dotenv==1.1.0
- mistralai==1.7.0

---

## 🚀 **8. DÉPLOIEMENT**

### **8.1 Installation**
```bash
# 1. Cloner le projet
cd "OneDrive\chatbot_project - Copie - Copie - Copie"

# 2. Activer l'environnement virtuel
venv\Scripts\activate

# 3. Installer les dépendances
pip install -r requirements.txt

# 4. Démarrer MySQL
net start mysql

# 5. Lancer le serveur
python manage.py runserver
```

### **8.2 URLs Principales**
- **Accueil** : http://127.0.0.1:8000/home/
- **Chat** : http://127.0.0.1:8000/chat/
- **Événements** : http://127.0.0.1:8000/events/
- **Admin** : http://127.0.0.1:8000/admin-maroc-cultures/dashboard/

---

## 📊 **9. PERFORMANCES ET MONITORING**

### **9.1 Métriques**
- **Temps de réponse** : < 5 secondes pour le chat
- **Taux de succès API** : > 95%
- **Disponibilité** : 99%

### **9.2 Logs et Debugging**
- Logs détaillés dans le terminal Django
- Monitoring des erreurs API
- Statistiques d'utilisation du chatbot

---

## 🔒 **10. SÉCURITÉ**

### **10.1 Authentification**
- Système Django Auth intégré
- Mots de passe hashés
- Sessions sécurisées

### **10.2 API**
- Clé API Mistral sécurisée dans .env
- Timeout de 30 secondes
- Gestion d'erreurs robuste

---

## 📈 **11. ÉVOLUTIONS FUTURES**

### **11.1 Court Terme**
- Cache des réponses fréquentes
- Analytics détaillées
- Support multilingue (arabe)

### **11.2 Long Terme**
- Application mobile
- Intégration réseaux sociaux
- IA vocale
- Réalité virtuelle pour les expositions

---

## 👥 **12. ÉQUIPE ET RÔLES**

### **12.1 Développement**
- **Développeur Principal** : Architecture Django + Intégration IA
- **Designer** : Interface utilisateur et expérience
- **Expert Culturel** : Contenu et validation culturelle

### **12.2 Maintenance**
- **Administrateur Système** : Serveur et base de données
- **Community Manager** : Contenu et interactions utilisateurs

---

## 📞 **13. CONTACT ET SUPPORT**

### **13.1 Informations de Contact**
- **Téléphone** : +****************
- **Email** : <EMAIL>
- **Adresse** : 456 Avenue Culturelle, Montréal, QC H2X 3Y7, Canada

### **13.2 Horaires**
- **Bureaux** : Lundi-Vendredi 9h-17h, Samedi 10h-15h
- **Support technique** : 24h/7j pour les urgences

---

---

## 📋 **14. ANNEXES TECHNIQUES**

### **14.1 Structure des Fichiers JSON**
```json
{
  "instruction": "Description de la question",
  "input": "mots-clés de recherche",
  "output": "Réponse à fournir"
}
```

### **14.2 Configuration Mistral AI**
```python
{
  "model": "mistral-small-latest",
  "max_tokens": 300,
  "temperature": 0.7,
  "timeout": 30
}
```

### **14.3 Commandes Utiles**
```bash
# Démarrer le serveur
python manage.py runserver

# Migrations base de données
python manage.py makemigrations
python manage.py migrate

# Créer un superutilisateur
python manage.py createsuperuser

# Collecter les fichiers statiques
python manage.py collectstatic
```

### **14.4 Tests et Validation**
- **Test API Mistral** : `python test_mistral_direct.py`
- **Test système complet** : `python test_mistral_simple.py`
- **Validation JSON** : Vérification syntaxe `faq_data.json`

---

**🇲🇦 Maroc Cultures - Votre passerelle vers la richesse culturelle du Maroc ! ✨**

**Document créé le :** 26 Mai 2025
**Version :** 1.0
**Statut :** Application fonctionnelle et déployée
