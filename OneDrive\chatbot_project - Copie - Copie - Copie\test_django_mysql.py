#!/usr/bin/env python3
"""
Test Django MySQL connection
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')

try:
    django.setup()
    
    from django.db import connection
    from django.core.management.color import make_style
    
    style = make_style()
    
    print("🔍 Testing Django MySQL connection...")
    print("=" * 50)
    
    # Get database configuration
    db_config = connection.settings_dict
    print("📋 Django Database Configuration:")
    print(f"  Engine: {db_config.get('ENGINE')}")
    print(f"  Name: {db_config.get('NAME')}")
    print(f"  User: {db_config.get('USER')}")
    print(f"  Host: {db_config.get('HOST')}")
    print(f"  Port: {db_config.get('PORT')}")
    print()
    
    # Test connection
    print("🔄 Testing Django database connection...")
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
    if result and result[0] == 1:
        print(style.SUCCESS("✅ Django MySQL connection successful!"))
        
        # Get database info
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📊 MySQL Version: {version}")
            
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"🗄️ Current Database: {current_db}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 Number of tables: {len(tables)}")
            
            if tables:
                print("📝 Tables:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("ℹ️ No tables found")
        
        print("\n🎉 Django is successfully connected to MySQL!")
        print("You can now:")
        print("  1. Run: python manage.py runserver")
        print("  2. Visit: http://127.0.0.1:8000/")
        
    else:
        print(style.ERROR("❌ Django MySQL connection failed"))
        
except Exception as e:
    print(f"❌ Error: {e}")
    print("\nTroubleshooting:")
    print("1. Check that MySQL is running in XAMPP")
    print("2. Verify .env file configuration")
    print("3. Check settings.py database configuration")
