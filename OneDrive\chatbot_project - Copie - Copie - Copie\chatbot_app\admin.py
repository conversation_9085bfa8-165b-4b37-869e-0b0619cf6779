from django.contrib import admin
from .models import FAQ, User, Admin, Conversation, Message, Chatbot, Event, Historique

class FAQAdmin(admin.ModelAdmin):
    list_display = ('question', 'created_at', 'updated_at')  # Affiche ces champs dans l'interface admin
    list_filter = ('created_at', 'updated_at')              # Ajoute des filtres pour ces champs

class UserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'privilege', 'acd')
    list_filter = ('privilege', 'acd')
    search_fields = ('username', 'email')

class AdminUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'privilege', 'acd')
    search_fields = ('username', 'email')

class ConversationAdmin(admin.ModelAdmin):
    list_display = ('title', 'date', 'user', 'auth_user', 'session_id')
    list_filter = ('date',)
    search_fields = ('title', 'session_id')

class MessageAdmin(admin.ModelAdmin):
    list_display = ('role', 'content', 'time', 'conversation')
    list_filter = ('role', 'time')
    search_fields = ('content',)

class ChatbotAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')

class HistoriqueAdmin(admin.ModelAdmin):
    list_display = ('id_conversation', 'id_utilisateur', 'session_id', 'date_acces', 'created_at')
    list_filter = ('date_acces', 'created_at')
    search_fields = ('session_id',)

admin.site.register(FAQ, FAQAdmin)
admin.site.register(User, UserAdmin)
admin.site.register(Admin, AdminUserAdmin)
admin.site.register(Conversation, ConversationAdmin)
admin.site.register(Message, MessageAdmin)
admin.site.register(Chatbot, ChatbotAdmin)
admin.site.register(Historique, HistoriqueAdmin)
