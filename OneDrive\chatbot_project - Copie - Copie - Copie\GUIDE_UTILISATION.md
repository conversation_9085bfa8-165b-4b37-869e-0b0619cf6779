# 📖 GUIDE D'UTILISATION - MAROC CULTURES

## 🚀 **DÉMARRAGE RAPIDE**

### **1. Lancement de l'Application**

#### **Prérequis**
- Python 3.8+ installé
- MySQL en cours d'exécution
- Environnement virtuel activé

#### **Commandes de Démarrage**
```bash
# 1. Aller dans le répertoire
cd "OneDrive\chatbot_project - Copie - Copie - Copie"

# 2. Activer l'environnement virtuel
venv\Scripts\activate

# 3. Démarrer le serveur
python manage.py runserver
```

#### **Accès à l'Application**
- **URL principale** : http://127.0.0.1:8000/
- **Page d'accueil** : http://127.0.0.1:8000/home/
- **Chat** : http://127.0.0.1:8000/chat/

---

## 👤 **UTILISATION POUR LES VISITEURS**

### **2. Navigation sur le Site**

#### **Page d'Accueil**
- **Découverte** : Présentation de Maroc Cultures
- **Services** : Festivals, spectacles, ateliers
- **Événements** : Prochaines manifestations
- **Chat** : Bouton en bas à droite

#### **Utilisation du Chat**
1. **Cliquer** sur le bouton chat (💬)
2. **Taper** votre question
3. **Envoyer** avec Entrée ou le bouton
4. **Recevoir** une réponse instantanée

### **3. Types de Questions Supportées**

#### **Questions Simples (Réponses JSON)**
```
✅ "Bonjour" → Salutation personnalisée
✅ "date" → Dates des événements
✅ "lieu" → Lieux des manifestations
✅ "prix" → Informations tarifaires
✅ "horaire" → Horaires des spectacles
✅ "contact" → Coordonnées de contact
```

#### **Questions Culturelles (Mistral AI)**
```
✅ "Parle-moi du tajine marocain"
✅ "Quelle est l'histoire de Marrakech ?"
✅ "Qu'est-ce que la musique gnawa ?"
✅ "Décris l'artisanat de Fès"
✅ "Comment préparer un couscous royal ?"
```

#### **Questions sur l'Association**
```
✅ "Qu'est-ce que Maroc Cultures ?"
✅ "Quand a été créée l'association ?"
✅ "Où se trouve le siège ?"
✅ "Quels sont vos événements phares ?"
✅ "Comment participer au Festival Mawazine ?"
```

---

## 🔐 **CRÉATION DE COMPTE**

### **4. Inscription**

#### **Étapes d'Inscription**
1. **Aller** sur http://127.0.0.1:8000/register/
2. **Remplir** le formulaire :
   - Nom complet
   - Adresse email
   - Mot de passe sécurisé
3. **Valider** l'inscription
4. **Connexion** automatique

#### **Avantages du Compte**
- **Historique** : Conversations sauvegardées
- **Personnalisation** : Réponses adaptées
- **Notifications** : Événements à venir
- **Participation** : Inscription aux festivals

### **5. Connexion**

#### **Page de Connexion**
- **URL** : http://127.0.0.1:8000/login/
- **Design** : Étoiles flottantes, dégradé rouge-vert
- **Champs** : Email et mot de passe

#### **Fonctionnalités**
- **Remember Me** : Session persistante
- **Mot de passe oublié** : Récupération par email
- **Redirection** : Vers la page demandée

---

## 📅 **GESTION DES ÉVÉNEMENTS**

### **6. Consultation des Événements**

#### **Liste des Événements**
- **URL** : http://127.0.0.1:8000/events/
- **Contenu** : Tous les événements à venir
- **Filtres** : Par date, type, lieu

#### **Détails d'un Événement**
- **Informations** : Date, heure, lieu, prix
- **Programme** : Artistes, spectacles
- **Inscription** : Réservation en ligne
- **Partage** : Réseaux sociaux

### **7. Événements Phares**

#### **Festival Mawazine**
- **Description** : Plus grand festival musical d'Afrique
- **Période** : Mai-Juin généralement
- **Artistes** : Internationaux et marocains
- **Accès** : Gratuit et payant selon les scènes

#### **Festival du Théâtre des Cultures**
- **Focus** : Arts dramatiques
- **Diversité** : Théâtre traditionnel et moderne
- **Participation** : Troupes nationales et internationales

#### **Génération Mawazine**
- **Public** : Jeunes talents
- **Objectif** : Découverte et promotion
- **Accompagnement** : Formation professionnelle

---

## 🤖 **OPTIMISATION DU CHAT**

### **8. Conseils pour de Meilleures Réponses**

#### **Questions Efficaces**
- **Soyez spécifique** : "Tajine aux olives" plutôt que "plat"
- **Utilisez des mots-clés** : "Festival Mawazine 2025"
- **Posez une question à la fois** : Évitez les questions multiples

#### **Exemples de Questions Optimales**
```
✅ "Quand aura lieu le prochain Festival Mawazine ?"
✅ "Comment s'inscrire au concours Génération Mawazine ?"
✅ "Quels sont les plats traditionnels du Ramadan au Maroc ?"
✅ "Peux-tu me parler de l'architecture des riads ?"
```

### **9. Fonctionnalités Avancées**

#### **Contexte de Conversation**
- **Mémoire** : Le chat se souvient des 10 derniers échanges
- **Continuité** : Références aux messages précédents
- **Personnalisation** : Réponses adaptées au contexte

#### **Historique**
- **Sauvegarde** : Toutes les conversations (utilisateurs connectés)
- **Recherche** : Dans l'historique personnel
- **Export** : Téléchargement des conversations

---

## 🛠️ **ADMINISTRATION**

### **10. Accès Administration**

#### **URL d'Administration**
- **Dashboard** : http://127.0.0.1:8000/admin-maroc-cultures/dashboard/
- **Accès** : Changement manuel d'URL (pas d'authentification initiale)

#### **Fonctionnalités Admin**
- **Gestion utilisateurs** : Liste, modification, suppression
- **Statistiques** : Utilisation du chat, événements
- **Contenu** : Mise à jour des événements

### **11. Gestion des Utilisateurs**

#### **Actions Disponibles**
- **Voir** : Profils et historiques
- **Modifier** : Informations utilisateur
- **Supprimer** : Comptes inactifs
- **Exporter** : Données utilisateur

#### **Statistiques**
- **Engagement** : Fréquence d'utilisation
- **Préférences** : Sujets les plus demandés
- **Satisfaction** : Feedback sur les réponses

---

## 🔧 **DÉPANNAGE**

### **12. Problèmes Courants**

#### **Le Chat ne Répond Pas**
- **Vérifier** : Module requests installé
- **Solution** : `pip install requests`
- **Alternative** : Utilise les réponses prédéfinies

#### **Erreur de Base de Données**
- **Cause** : MySQL non démarré
- **Solution** : `net start mysql`
- **Vérification** : Services Windows

#### **Page ne se Charge Pas**
- **Vérifier** : Serveur Django actif
- **Redémarrer** : `python manage.py runserver`
- **Port** : Changer si 8000 occupé

### **13. Messages d'Erreur**

#### **"No module named 'requests'"**
```bash
# Solution
pip install requests
# Puis redémarrer le serveur
```

#### **"Can't connect to MySQL server"**
```bash
# Démarrer MySQL
net start mysql
# Ou via Services Windows
```

#### **"Port already in use"**
```bash
# Utiliser un autre port
python manage.py runserver 8001
```

---

## 📞 **SUPPORT**

### **14. Obtenir de l'Aide**

#### **Canaux de Support**
- **Chat** : Support intégré dans l'application
- **Email** : <EMAIL>
- **Téléphone** : +****************

#### **Documentation**
- **Cahier des charges** : `CAHIER_DES_CHARGES.md`
- **Spécifications** : `SPECIFICATIONS_FONCTIONNELLES.md`
- **Tests** : `TEST_MOTS_SIMPLES.md`

#### **Horaires de Support**
- **Bureaux** : Lundi-Vendredi 9h-17h
- **Email** : Réponse sous 24h
- **Urgences** : Support technique 24h/7j

---

## 🎯 **BONNES PRATIQUES**

### **15. Utilisation Optimale**

#### **Pour les Visiteurs**
- **Explorez** : Testez différents types de questions
- **Soyez patient** : L'IA peut prendre quelques secondes
- **Donnez du feedback** : Aidez-nous à améliorer

#### **Pour les Administrateurs**
- **Surveillez** : Logs et métriques régulièrement
- **Mettez à jour** : Contenu événements fréquemment
- **Sauvegardez** : Base de données régulièrement

---

**🇲🇦 Maroc Cultures - Votre guide culturel intelligent ! ✨**

**Besoin d'aide ? N'hésitez pas à utiliser le chat intégré !** 💬
