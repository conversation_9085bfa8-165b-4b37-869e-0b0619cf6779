#!/usr/bin/env python
"""
Script simple pour tester l'ajout des événements 2025
"""

import os
import sys
import django

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event
from datetime import date, time

def test_ajout_evenements():
    print("🎭 Test d'ajout des événements 2025 - Maroc Cultures")
    print("=" * 60)
    
    # Test avec un seul événement
    event_data = {
        'title': 'Festival Mawazine 2025 - Rythmes du Monde',
        'description': '''Le plus grand festival musical d'Afrique revient pour sa 19ème édition ! 
Mawazine 2025 promet une programmation exceptionnelle avec des artistes 
internationaux de renom et les plus grandes stars de la musique marocaine.

🎵 Programmation :
- <PERSON><PERSON>g : Artistes internationaux
- <PERSON><PERSON> OLM Souissi : Stars arabes et marocaines  
- <PERSON>ène Nahda : Musique du monde
- <PERSON><PERSON> Chellah : Patrimoine musical marocain

🎫 Entrée gratuite sur la plupart des scènes
💫 Concerts payants pour les têtes d'affiche internationales''',
        'date_start': date(2025, 5, 23),
        'date_end': date(2025, 5, 31),
        'time': time(20, 0),
        'location': 'Rabat - Multiples scènes (Bouregreg, OLM Souissi, Nahda, Chellah)',
        'image_url': 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?w=800&h=600&fit=crop&crop=center',
        'registration_url': 'https://www.festivalmawazine.ma/inscription'
    }
    
    try:
        # Vérifier si l'événement existe déjà
        if Event.objects.filter(title=event_data['title']).exists():
            print(f"⚠️  Événement déjà existant : {event_data['title']}")
            event = Event.objects.get(title=event_data['title'])
            print(f"📅 Date : {event.date_start}")
            print(f"📍 Lieu : {event.location}")
            print(f"🖼️  Image : {event.image_url}")
        else:
            # Créer le nouvel événement
            event = Event.objects.create(**event_data)
            print(f"✅ Événement ajouté : {event.title}")
            print(f"📅 Date : {event.date_start}")
            print(f"📍 Lieu : {event.location}")
            print(f"🖼️  Image : {event.image_url}")
        
        # Compter tous les événements
        total_events = Event.objects.count()
        print(f"\n📊 Total événements en base : {total_events}")
        
        # Lister tous les événements
        print("\n📋 Liste des événements :")
        for event in Event.objects.all()[:5]:  # Afficher les 5 premiers
            print(f"   - {event.title} ({event.date_start})")
        
        if total_events > 5:
            print(f"   ... et {total_events - 5} autres")
            
        print("\n✅ Test réussi ! Les événements peuvent être ajoutés.")
        print("🌐 Consultez-les sur : http://127.0.0.1:8000/home/")
        
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
        print("💡 Vérifiez que Django est configuré correctement.")

if __name__ == '__main__':
    test_ajout_evenements()
