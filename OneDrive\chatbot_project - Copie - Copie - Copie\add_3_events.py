#!/usr/bin/env python3
"""
Ajouter les 3 événements culturels marocains spécifiés
"""

import pymysql
from datetime import datetime

def add_three_events():
    """Ajouter les 3 événements spécifiés"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🎭 Ajout des 3 événements culturels marocains")
        print("=" * 60)
        
        # Les 3 événements à ajouter
        events = [
            {
                'title': 'Festival Marocain de Montréal 2024',
                'description': 'Le plus grand festival culturel marocain au Canada. Découvrez la richesse de la culture marocaine à travers la musique, la danse, l\'artisanat et la gastronomie traditionnelle. Un événement familial avec des spectacles de groupes folkloriques, des ateliers de henné, et des dégustations de thé à la menthe.',
                'date_start': '2024-06-07',
                'date_end': '2024-06-09',
                'time_start': '10:00:00',
                'time_end': '22:00:00',
                'location': 'Parc Jean-Drapeau, Montréal',
                'price': 0.00,
                'max_participants': 5000,
                'category': 'Festival',
                'organizer': 'Association Culturelle Marocaine de Montréal'
            },
            {
                'title': 'Exposition d\'Art Contemporain Marocain',
                'description': 'Une exposition unique présentant les œuvres d\'artistes marocains contemporains. Peintures, sculptures et installations qui reflètent l\'évolution de l\'art marocain moderne tout en préservant les traditions ancestrales.',
                'date_start': '2024-05-28',
                'date_end': '2024-06-27',
                'time_start': '09:00:00',
                'time_end': '18:00:00',
                'location': 'Galerie d\'Art Contemporain, Centre-ville',
                'price': 15.00,
                'max_participants': 200,
                'category': 'Exposition',
                'organizer': 'Galerie Maghreb Arts'
            },
            {
                'title': 'Concert de Musique Gnawa',
                'description': 'Soirée exceptionnelle de musique Gnawa avec des maîtres musiciens venus directement d\'Essaouira. La musique Gnawa, patrimoine spirituel et musical du Maroc, vous transportera dans un voyage mystique unique.',
                'date_start': '2024-05-31',
                'date_end': '2024-05-31',
                'time_start': '20:00:00',
                'time_end': '23:00:00',
                'location': 'Théâtre Corona, Montréal',
                'price': 45.00,
                'max_participants': 800,
                'category': 'Concert',
                'organizer': 'Productions Culturelles Maghreb'
            }
        ]
        
        # Vérifier d'abord si la table existe
        cursor.execute("SHOW TABLES LIKE 'chatbot_app_event'")
        if not cursor.fetchone():
            print("❌ Table chatbot_app_event n'existe pas")
            return
        
        # Supprimer les événements existants avec ces titres
        for event in events:
            cursor.execute("DELETE FROM chatbot_app_event WHERE title = %s", (event['title'],))
        
        # Insérer les nouveaux événements
        insert_query = """
        INSERT INTO chatbot_app_event 
        (title, description, date_start, date_end, time_start, time_end, location, price, max_participants, category, organizer, created_at, updated_at)
        VALUES (%(title)s, %(description)s, %(date_start)s, %(date_end)s, %(time_start)s, %(time_end)s, %(location)s, %(price)s, %(max_participants)s, %(category)s, %(organizer)s, NOW(), NOW())
        """
        
        created_count = 0
        for event in events:
            try:
                cursor.execute(insert_query, event)
                print(f"✅ Événement créé: {event['title']}")
                print(f"   📅 Date: {event['date_start']} - {event['date_end']}")
                print(f"   🕐 Heure: {event['time_start']} - {event['time_end']}")
                print(f"   📍 Lieu: {event['location']}")
                print(f"   💰 Prix: {event['price']}€")
                print(f"   👥 Participants max: {event['max_participants']}")
                print(f"   🏷️ Catégorie: {event['category']}")
                print(f"   👤 Organisateur: {event['organizer']}")
                print("-" * 60)
                created_count += 1
            except Exception as e:
                print(f"❌ Erreur lors de la création de '{event['title']}': {e}")
        
        connection.commit()
        
        # Vérifier le nombre total d'événements
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_event")
        total_events = cursor.fetchone()[0]
        
        print(f"\n🎉 {created_count} événements créés avec succès!")
        print(f"📊 Total d'événements dans la base: {total_events}")
        
        # Afficher tous les événements
        cursor.execute("SELECT id, title, date_start, location, price FROM chatbot_app_event ORDER BY date_start")
        all_events = cursor.fetchall()
        
        if all_events:
            print(f"\n📋 Liste de tous les événements:")
            for event in all_events:
                print(f"   ID: {event[0]} | {event[1]} | {event[2]} | {event[3]} | {event[4]}€")
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        connection.rollback()
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    add_three_events()
