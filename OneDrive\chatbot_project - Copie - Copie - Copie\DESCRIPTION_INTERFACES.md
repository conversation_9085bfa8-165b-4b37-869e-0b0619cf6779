# 🖥️ DESCRIPTION DÉTAILLÉE DES INTERFACES - MAROC CULTURES

## 📋 **VUE D'ENSEMBLE DES INTERFACES**

L'application Maroc Cultures comprend **8 interfaces principales** offrant une expérience utilisateur complète et intuitive.

---

## 🏠 **1. INTERFACE PAGE D'ACCUEIL (home.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/home/
```

### **🎯 Objectif**
Landing page professionnelle présentant l'association Maroc Cultures et ses services.

### **🎨 Design et Layout**
- **Header** : Navigation principale avec logo Maroc Cultures
- **Hero Section** : Bannière d'accueil avec titre principal et sous-titre
- **Couleurs** : Dégradé rouge-vert (couleurs du drapeau marocain)
- **Responsive** : Adaptation automatique mobile/tablette/desktop

### **📱 Sections Principales**

#### **🎭 Section "Nos Services"**
- **Contenu** : Présentation des services offerts
  - Festivals culturels (Mawazine, Théâtre des Cultures)
  - Spectacles et concerts
  - Ateliers artistiques
  - Promotion culture marocaine
- **Couleur titre** : Rouge (#DC143C)
- **Layout** : Grille responsive 2-3 colonnes

#### **ℹ️ Section "À Propos de Nous"**
- **Contenu** : Histoire et mission de l'association
  - Fondation en 2001
  - Vision royale du Roi Mohammed VI
  - Siège à Rabat
  - Objectifs culturels
- **Couleur titre** : Rouge (#DC143C)
- **Style** : Texte centré avec image d'illustration

#### **📅 Section "Événements à Venir"**
- **Contenu** : Prochains festivals et spectacles
  - Festival Mawazine (mai-juin)
  - Festival du Théâtre des Cultures
  - Génération Mawazine
  - Événements ponctuels
- **Couleur titre** : Rouge (#DC143C)
- **Fonctionnalité** : Liens vers page événements détaillée

#### **💬 Section "Témoignages"**
- **Contenu** : Retours d'expérience des participants
- **Format** : Cartes avec photos et citations
- **Couleur titre** : Rouge (#DC143C)
- **Animation** : Carrousel ou grille statique

### **🔧 Éléments Interactifs**
- **Bouton Chat** : Position fixe coin inférieur droit
  - Couleur : Vert (#1abc9c)
  - Icône : 💬
  - Animation : Pulse ou bounce
  - Action : Ouvre l'interface chat
- **Menu Navigation** : Liens vers toutes les pages
- **Boutons CTA** : "Découvrir nos événements", "Nous contacter"

### **📱 Footer**
- **Informations Contact** :
  - Téléphone : +****************
  - Email : <EMAIL>
  - Adresse : 456 Avenue Culturelle, Montréal, QC H2X 3Y7
- **Couleurs titres** : Vert (#1abc9c)
- **Sections** : À propos, Navigation, Contact, Réseaux sociaux

---

## 💬 **2. INTERFACE CHATBOT (chat.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/chat/
```

### **🎯 Objectif**
Interface de conversation intelligente spécialisée dans la culture marocaine.

### **🎨 Design et Layout**
- **Style** : Interface de messagerie moderne
- **Couleurs** : Fond clair, bulles de chat contrastées
- **Responsive** : Optimisé pour tous les écrans
- **Taille** : Compacte pour une meilleure ergonomie

### **📱 Composants Principaux**

#### **💬 Zone de Conversation**
- **Messages Utilisateur** :
  - Position : Droite
  - Couleur : Bleu ou vert clair
  - Bordures : Arrondies
  - Timestamp : Affiché discrètement
- **Messages Chatbot** :
  - Position : Gauche
  - Couleur : Gris clair
  - Avatar : Logo Maroc Cultures ou 🤖
  - Signature : "Maroc Cultures" dans les réponses

#### **✍️ Zone de Saisie**
- **Champ de texte** :
  - Placeholder : "Posez votre question sur la culture marocaine..."
  - Taille : Optimisée pour la lisibilité
  - Auto-resize : S'adapte au contenu
- **Bouton Envoyer** :
  - Couleur : Vert (#1abc9c)
  - Icône : ➤ ou "Envoyer"
  - Raccourci : Touche Entrée

#### **📊 Indicateurs d'État**
- **Frappe en cours** : "Le chatbot réfléchit..."
- **Chargement** : Animation de points ou spinner
- **Erreur** : Message informatif et bienveillant
- **Succès** : Confirmation discrète

### **🔧 Fonctionnalités Avancées**
- **Historique** : Sauvegarde des conversations (utilisateurs connectés)
- **Contexte** : Mémorisation des 10 derniers échanges
- **Suggestions** : Questions fréquentes affichées
- **Export** : Téléchargement des conversations

### **📱 Responsive Behavior**
- **Mobile** : Interface plein écran, clavier optimisé
- **Tablette** : Sidebar ou modal
- **Desktop** : Fenêtre centrée ou intégrée

---

## 🔐 **3. INTERFACE CONNEXION (login.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/login/
```

### **🎯 Objectif**
Authentification sécurisée des utilisateurs avec design attractif.

### **🎨 Design Spécifique**
- **Arrière-plan** : Étoiles flottantes animées
- **Header** : Dégradé rouge-vert
- **Formulaire** : Centré, fond semi-transparent
- **Boutons** : Vert (#1abc9c) selon les préférences

### **📱 Composants du Formulaire**

#### **📧 Champ Email**
- **Label** : "Adresse email"
- **Type** : Email avec validation
- **Placeholder** : "<EMAIL>"
- **Validation** : Format email requis

#### **🔒 Champ Mot de Passe**
- **Label** : "Mot de passe"
- **Type** : Password avec option affichage
- **Placeholder** : "Votre mot de passe"
- **Validation** : Minimum 6 caractères

#### **✅ Options Supplémentaires**
- **Checkbox** : "Se souvenir de moi"
- **Lien** : "Mot de passe oublié ?"
- **Lien** : "Pas encore de compte ? S'inscrire"

### **🔧 Fonctionnalités**
- **Validation temps réel** : Feedback immédiat
- **Gestion erreurs** : Messages clairs et utiles
- **Redirection** : Vers page demandée ou accueil
- **Sécurité** : Protection CSRF, limitation tentatives

### **🎨 Éléments Visuels**
- **Étoiles flottantes** : Animation CSS continue
- **Dégradé header** : Rouge vers vert
- **Transitions** : Fluides et modernes
- **Responsive** : Adaptation mobile parfaite

---

## 📝 **4. INTERFACE INSCRIPTION (register.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/register/
```

### **🎯 Objectif**
Création de compte utilisateur avec design cohérent à la page de connexion.

### **🎨 Design Similaire Login**
- **Style** : Reprend le design de login.html
- **Étoiles flottantes** : Même animation d'arrière-plan
- **Dégradé header** : Rouge-vert identique
- **Boutons verts** : Cohérence visuelle

### **📱 Formulaire d'Inscription**

#### **👤 Champ Nom Complet**
- **Label** : "Nom complet"
- **Type** : Text
- **Placeholder** : "Prénom Nom"
- **Validation** : Minimum 2 caractères

#### **📧 Champ Email**
- **Label** : "Adresse email"
- **Type** : Email avec vérification unicité
- **Placeholder** : "<EMAIL>"
- **Validation** : Format + unicité en base

#### **🔒 Champ Mot de Passe**
- **Label** : "Mot de passe"
- **Type** : Password
- **Placeholder** : "Minimum 6 caractères"
- **Validation** : Critères sécurité Django

#### **🔒 Confirmation Mot de Passe**
- **Label** : "Confirmer le mot de passe"
- **Type** : Password
- **Validation** : Correspondance avec premier champ

### **🔧 Processus d'Inscription**
- **Validation** : Vérification en temps réel
- **Création compte** : Django Auth User
- **Confirmation** : Message de succès
- **Redirection** : Vers page d'accueil connecté

---

## 📅 **5. INTERFACE ÉVÉNEMENTS (events.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/events/
```

### **🎯 Objectif**
Présentation et gestion des événements culturels de l'association.

### **🎨 Design et Layout**
- **Header** : Titre principal "Nos Événements Culturels"
- **Couleur titre** : Rouge (#DC143C)
- **Layout** : Grille responsive d'événements
- **Style** : Cartes modernes avec images

### **📱 Liste des Événements**

#### **🎪 Festival Mawazine**
- **Description** : Plus grand festival musical d'Afrique
- **Période** : Mai-Juin (généralement)
- **Lieu** : Rabat et autres villes du Maroc
- **Artistes** : Internationaux et marocains
- **Particularité** : Événement phare de l'association
- **Image** : Photo du festival avec foule
- **Bouton** : "En savoir plus" (vert)

#### **🎭 Festival du Théâtre des Cultures**
- **Description** : Arts dramatiques et spectacles
- **Focus** : Théâtre traditionnel et moderne
- **Participation** : Troupes nationales et internationales
- **Objectif** : Promotion arts dramatiques
- **Image** : Scène de théâtre ou acteurs
- **Bouton** : "Découvrir" (vert)

#### **🌟 Génération Mawazine**
- **Description** : Concours pour jeunes talents
- **Public** : Artistes émergents
- **Objectif** : Découverte et promotion
- **Accompagnement** : Formation professionnelle
- **Image** : Jeunes artistes sur scène
- **Bouton** : "Participer" (vert)

### **📱 Détails par Événement**
- **Dates et horaires** : Calendrier complet
- **Lieux** : Adresses et plans d'accès
- **Programme** : Artistes et spectacles
- **Tarifs** : Grille tarifaire (gratuit/payant)
- **Inscription** : Formulaire ou lien externe

### **🔧 Fonctionnalités**
- **Filtres** : Par date, type, lieu
- **Recherche** : Barre de recherche événements
- **Partage** : Réseaux sociaux
- **Favoris** : Sauvegarde événements (utilisateurs connectés)

---

## 👨‍💼 **6. INTERFACE ADMINISTRATION (admin_dashboard.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/admin-maroc-cultures/dashboard/
```

### **🎯 Objectif**
Interface de gestion et monitoring pour les administrateurs.

### **🎨 Design Professionnel**
- **Sidebar** : Navigation administrative
- **Header** : Titre "Dashboard Administrateur"
- **Couleurs** : Palette professionnelle (bleu/gris)
- **Layout** : Grille de widgets et métriques

### **📊 Métriques Principales**

#### **👥 Statistiques Utilisateurs**
- **Nombre total** : Compteur avec icône
- **Nouveaux utilisateurs** : Dernières 24h/7j/30j
- **Utilisateurs actifs** : Connexions récentes
- **Graphique** : Évolution inscriptions

#### **💬 Statistiques Chatbot**
- **Messages traités** : Total et période
- **Taux de succès** : JSON/Mistral/Fallback
- **Temps de réponse** : Moyenne et distribution
- **Questions fréquentes** : Top 10

#### **📅 Statistiques Événements**
- **Événements actifs** : En cours et à venir
- **Inscriptions** : Par événement
- **Participation** : Taux de présence
- **Revenus** : Si applicable

### **🔧 Actions Rapides**
- **Gestion utilisateurs** : Lien vers liste complète
- **Modération chat** : Conversations signalées
- **Ajout événement** : Formulaire rapide
- **Sauvegarde** : Export données

### **📱 Widgets Dashboard**
- **Alertes** : Notifications importantes
- **Activité récente** : Log des actions
- **Performance** : Métriques serveur
- **Maintenance** : Statut système

---

## 👥 **7. INTERFACE GESTION UTILISATEURS (admin_users.html)**

### **📍 URL d'accès**
```
http://127.0.0.1:8000/admin-maroc-cultures/users/
```

### **🎯 Objectif**
Gestion complète des comptes utilisateurs par les administrateurs.

### **🎨 Design Tableau**
- **Layout** : Tableau responsive avec pagination
- **Filtres** : Barre de recherche et filtres avancés
- **Actions** : Boutons d'action par ligne
- **Couleurs** : Cohérentes avec le dashboard

### **📊 Colonnes du Tableau**
- **ID** : Identifiant unique
- **Nom** : Nom complet utilisateur
- **Email** : Adresse email
- **Date inscription** : Timestamp création
- **Dernière connexion** : Activité récente
- **Statut** : Actif/Inactif/Suspendu
- **Actions** : Voir/Modifier/Supprimer

### **🔧 Fonctionnalités de Gestion**

#### **🔍 Recherche et Filtres**
- **Recherche globale** : Nom, email
- **Filtre par statut** : Actif/Inactif
- **Filtre par date** : Période d'inscription
- **Tri** : Par colonne (nom, date, etc.)

#### **⚡ Actions en Masse**
- **Sélection multiple** : Checkboxes
- **Actions groupées** : Activer/Désactiver/Exporter
- **Confirmation** : Modales de sécurité

#### **👤 Détails Utilisateur**
- **Profil complet** : Informations personnelles
- **Historique chat** : Conversations utilisateur
- **Activité** : Log des connexions
- **Statistiques** : Utilisation de l'application

### **🔒 Sécurité et Permissions**
- **Logs d'actions** : Traçabilité modifications
- **Confirmations** : Actions critiques
- **Permissions** : Niveaux d'accès admin
- **Audit** : Historique des modifications

---

## 📱 **8. INTERFACE RESPONSIVE - ADAPTATIONS MOBILES**

### **🎯 Objectif**
Adaptation optimale de toutes les interfaces sur appareils mobiles.

### **📱 Breakpoints Responsive**
- **Mobile** : < 768px
- **Tablette** : 768px - 1024px
- **Desktop** : > 1024px

### **🔧 Adaptations Spécifiques**

#### **📱 Navigation Mobile**
- **Menu hamburger** : Icône ☰
- **Sidebar** : Coulissante depuis la gauche
- **Boutons** : Taille tactile optimisée (44px min)

#### **💬 Chat Mobile**
- **Interface plein écran** : Utilisation maximale
- **Clavier** : Adaptation automatique
- **Scroll** : Optimisé pour le tactile
- **Boutons** : Plus grands pour le doigt

#### **📝 Formulaires Mobile**
- **Champs** : Hauteur augmentée
- **Labels** : Positionnement optimisé
- **Validation** : Messages adaptés
- **Clavier** : Type approprié (email, numérique)

#### **📊 Tableaux Mobile**
- **Scroll horizontal** : Si nécessaire
- **Cartes** : Transformation en cartes empilées
- **Actions** : Menu déroulant ou swipe

### **🎨 Optimisations UX Mobile**
- **Touch targets** : Minimum 44px
- **Espacement** : Augmenté entre éléments
- **Polices** : Tailles lisibles (16px min)
- **Contraste** : Amélioré pour lisibilité
- **Performance** : Images optimisées, CSS minifié

---

---

## 🔧 **SPÉCIFICATIONS TECHNIQUES DES INTERFACES**

### **📁 Structure des Fichiers**
```
templates/chatbot_app/
├── base.html              # Template de base
├── home.html              # Page d'accueil
├── chat.html              # Interface chatbot
├── login.html             # Connexion
├── register.html          # Inscription
├── events.html            # Événements
├── admin_dashboard.html   # Dashboard admin
├── admin_users.html       # Gestion utilisateurs
└── components/            # Composants réutilisables
    ├── navbar.html
    ├── footer.html
    └── chat_widget.html
```

### **🎨 Fichiers CSS Associés**
```
static/css/
├── style.css              # Styles principaux
├── chat.css               # Styles chatbot
├── admin.css              # Styles administration
├── responsive.css         # Media queries
└── animations.css         # Animations et transitions
```

### **⚡ Fichiers JavaScript**
```
static/js/
├── chat.js                # Logique chatbot
├── navbar.js              # Navigation
├── admin.js               # Fonctions admin
├── forms.js               # Validation formulaires
└── responsive.js          # Comportements mobiles
```

---

## 📊 **MATRICE DES FONCTIONNALITÉS PAR INTERFACE**

| Interface | Authentification | Responsive | AJAX | Animations | Admin |
|-----------|------------------|------------|------|------------|-------|
| 🏠 Accueil | ❌ Optionnelle | ✅ Oui | ❌ Non | ✅ Oui | ❌ Non |
| 💬 Chat | ❌ Optionnelle | ✅ Oui | ✅ Oui | ✅ Oui | ❌ Non |
| 🔐 Login | ❌ Non | ✅ Oui | ✅ Oui | ✅ Oui | ❌ Non |
| 📝 Register | ❌ Non | ✅ Oui | ✅ Oui | ✅ Oui | ❌ Non |
| 📅 Events | ❌ Optionnelle | ✅ Oui | ❌ Non | ✅ Oui | ❌ Non |
| 👨‍💼 Dashboard | ✅ Requise | ✅ Oui | ✅ Oui | ✅ Oui | ✅ Oui |
| 👥 Users | ✅ Requise | ✅ Oui | ✅ Oui | ❌ Non | ✅ Oui |

---

## 🎯 **PARCOURS UTILISATEUR TYPE**

### **👤 Visiteur Non Connecté**
```
1. 🏠 Accueil → Découverte de l'association
2. 💬 Chat → Test du chatbot (questions culture)
3. 📅 Events → Consultation événements
4. 📝 Register → Création de compte
5. 🔐 Login → Connexion
```

### **🔐 Utilisateur Connecté**
```
1. 🏠 Accueil → Personnalisé avec nom utilisateur
2. 💬 Chat → Historique sauvegardé
3. 📅 Events → Inscription aux événements
4. 👤 Profil → Gestion compte personnel
```

### **👨‍💼 Administrateur**
```
1. 🔐 Login → Authentification admin
2. 👨‍💼 Dashboard → Vue d'ensemble métriques
3. 👥 Users → Gestion des utilisateurs
4. 📅 Events → Gestion des événements
5. 📊 Analytics → Statistiques détaillées
```

---

## 🔗 **NAVIGATION ET LIENS ENTRE INTERFACES**

### **🧭 Menu Principal (Toutes les pages)**
- **🏠 Accueil** → `/home/<USER>
- **💬 Chat** → `/chat/`
- **📅 Événements** → `/events/`
- **🔐 Connexion** → `/login/` (si non connecté)
- **👤 Profil** → `/profile/` (si connecté)
- **🚪 Déconnexion** → `/logout/` (si connecté)

### **🔗 Liens Contextuels**
- **Bouton Chat** (toutes pages) → Interface chat
- **"S'inscrire"** (login) → Page inscription
- **"Se connecter"** (register) → Page connexion
- **"Admin"** (si permissions) → Dashboard admin

---

## 📱 **COMPORTEMENTS RESPONSIVE DÉTAILLÉS**

### **📱 Mobile (< 768px)**
- **Navigation** : Menu hamburger
- **Chat** : Plein écran
- **Formulaires** : Champs empilés
- **Tableaux** : Scroll horizontal ou cartes
- **Boutons** : Taille tactile (44px+)

### **📱 Tablette (768px - 1024px)**
- **Navigation** : Menu complet ou réduit
- **Chat** : Modal ou sidebar
- **Grilles** : 2 colonnes maximum
- **Espacement** : Optimisé pour tablette

### **🖥️ Desktop (> 1024px)**
- **Navigation** : Menu complet horizontal
- **Chat** : Fenêtre intégrée ou popup
- **Grilles** : 3-4 colonnes
- **Sidebar** : Administration visible

---

## 🎨 **CHARTE GRAPHIQUE APPLIQUÉE**

### **🇲🇦 Couleurs Principales**
- **Rouge** : #DC143C (titres, accents)
- **Vert** : #228B22 (boutons, liens)
- **Vert accent** : #1abc9c (éléments interactifs)
- **Gris** : #333333 (texte)
- **Blanc** : #FFFFFF (arrière-plans)

### **📝 Typographie**
- **Titres H1** : 2.5rem, bold
- **Titres H2** : 2rem, bold
- **Titres H3** : 1.5rem, semi-bold
- **Texte** : 1rem, regular
- **Petits textes** : 0.875rem

### **🎭 Animations**
- **Transitions** : 0.3s ease-in-out
- **Hover effects** : Changement couleur/ombre
- **Loading** : Spinners ou barres de progression
- **Étoiles** : Animation CSS continue (login/register)

---

## 🔒 **SÉCURITÉ ET PERMISSIONS**

### **🛡️ Protection CSRF**
- **Toutes les formes** : Token CSRF Django
- **AJAX** : Headers X-CSRFToken
- **Validation** : Côté serveur obligatoire

### **👥 Niveaux d'Accès**
- **Public** : Accueil, Chat, Events (lecture)
- **Utilisateur** : Chat avec historique, Profil
- **Admin** : Dashboard, Gestion users/events

### **🔐 Authentification**
- **Sessions** : Django sessions framework
- **Mots de passe** : Hashage PBKDF2
- **Tentatives** : Limitation brute force
- **Expiration** : Sessions automatiques

---

## 📊 **MÉTRIQUES ET ANALYTICS**

### **📈 Suivi par Interface**
- **Page views** : Compteurs par page
- **Temps passé** : Durée moyenne
- **Taux de rebond** : Sorties immédiates
- **Conversions** : Inscriptions, utilisation chat

### **💬 Métriques Chat**
- **Messages envoyés** : Volume par jour/semaine
- **Types de réponses** : JSON/Mistral/Fallback
- **Satisfaction** : Feedback utilisateurs
- **Questions fréquentes** : Top 10

### **👥 Métriques Utilisateurs**
- **Inscriptions** : Nouvelles par période
- **Activité** : Connexions, utilisation
- **Rétention** : Retour utilisateurs
- **Engagement** : Interactions par session

---

**🇲🇦 Maroc Cultures - Interfaces complètes pour une expérience utilisateur exceptionnelle ! ✨**

**Documentation technique détaillée pour chaque interface de l'application.** 📚
