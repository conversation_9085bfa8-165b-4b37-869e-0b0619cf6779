#!/usr/bin/env python3
"""
Test simple de connexion MySQL sans dépendances Django
"""

import os

def test_mysql_with_connector():
    """Test avec mysql-connector-python"""
    try:
        import mysql.connector
        from mysql.connector import Error
        
        print("🔍 Test avec mysql-connector-python...")
        
        # Configuration depuis les variables d'environnement ou valeurs par défaut
        config = {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': '',  # Vous devez mettre votre mot de passe ici
            'database': 'chatbot_maroc_cultures'
        }
        
        print(f"Configuration:")
        print(f"  Host: {config['host']}")
        print(f"  Port: {config['port']}")
        print(f"  User: {config['user']}")
        print(f"  Database: {config['database']}")
        print()
        
        # Test de connexion
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ Connexion MySQL réussie avec mysql-connector-python!")
            
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📊 Version MySQL: {version}")
            
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"🗄️  Base de données: {current_db}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 Nombre de tables: {len(tables)}")
            
            if tables:
                print("Tables existantes:")
                for table in tables:
                    print(f"  - {table[0]}")
            
            cursor.close()
            connection.close()
            return True
            
    except ImportError:
        print("❌ mysql-connector-python n'est pas installé")
        return False
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_mysql_with_mysqlclient():
    """Test avec mysqlclient (MySQLdb)"""
    try:
        import MySQLdb
        
        print("🔍 Test avec mysqlclient (MySQLdb)...")
        
        # Configuration
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'passwd': '',  # Vous devez mettre votre mot de passe ici
            'db': 'chatbot_maroc_cultures',
            'charset': 'utf8mb4'
        }
        
        # Test de connexion
        connection = MySQLdb.connect(**config)
        
        print("✅ Connexion MySQL réussie avec mysqlclient!")
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"📊 Version MySQL: {version}")
        
        cursor.close()
        connection.close()
        return True
        
    except ImportError:
        print("❌ mysqlclient n'est pas installé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_mysql_with_pymysql():
    """Test avec PyMySQL"""
    try:
        import pymysql
        
        print("🔍 Test avec PyMySQL...")
        
        # Configuration
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',  # Vous devez mettre votre mot de passe ici
            'database': 'chatbot_maroc_cultures',
            'charset': 'utf8mb4'
        }
        
        # Test de connexion
        connection = pymysql.connect(**config)
        
        print("✅ Connexion MySQL réussie avec PyMySQL!")
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"📊 Version MySQL: {version}")
        
        cursor.close()
        connection.close()
        return True
        
    except ImportError:
        print("❌ PyMySQL n'est pas installé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test de connexion MySQL - Chatbot Maroc Cultures")
    print("=" * 60)
    print()
    
    print("⚠️  IMPORTANT: Modifiez ce script pour inclure votre mot de passe MySQL!")
    print("   Éditez les lignes 'password': '' dans chaque fonction de test.")
    print()
    
    # Tester différents drivers MySQL
    success = False
    
    success |= test_mysql_with_connector()
    print()
    
    success |= test_mysql_with_mysqlclient()
    print()
    
    success |= test_mysql_with_pymysql()
    print()
    
    if success:
        print("🎉 Au moins une connexion MySQL a réussi!")
        print("Vous pouvez maintenant configurer Django pour utiliser MySQL.")
    else:
        print("❌ Aucune connexion MySQL n'a réussi.")
        print("Vérifiez:")
        print("  1. MySQL est installé et démarré")
        print("  2. La base de données 'chatbot_maroc_cultures' existe")
        print("  3. Le mot de passe MySQL est correct")
        print("  4. Les packages Python MySQL sont installés")

if __name__ == "__main__":
    main()
