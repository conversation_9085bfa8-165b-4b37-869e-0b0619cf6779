#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test simple pour le chatbot Maroc Cultures
Teste la logique de correspondance JSON sans Django
"""

import json
import os

def test_chatbot_logic(user_message, faq_file_path):
    """
    Teste la logique de correspondance du chatbot
    """
    print(f"\n🔍 TEST: '{user_message}'")
    print("=" * 50)
    
    try:
        # Charger les données JSON
        with open(faq_file_path, 'r', encoding='utf-8') as f:
            faq_data = json.load(f)
        
        # Variables pour la correspondance
        best_match = None
        highest_score = 0
        user_message_clean = user_message.lower().strip()
        
        print(f"📋 Message nettoyé: '{user_message_clean}'")
        print(f"📊 Nombre d'entrées JSON: {len(faq_data)}")
        
        # Méthode 1: Correspondance directe avec les mots-clés d'input
        print("\n🎯 Phase 1: Correspondance directe")
        for i, item in enumerate(faq_data):
            input_keywords = item['input'].lower().split()
            
            for keyword in input_keywords:
                keyword = keyword.strip()
                if keyword and keyword in user_message_clean:
                    score = len(keyword) * 10
                    
                    print(f"  ✅ Mot-clé '{keyword}' trouvé (score: {score})")
                    
                    if score > highest_score:
                        highest_score = score
                        best_match = item
                        print(f"  🏆 Nouveau meilleur score: {score}")
        
        # Méthode 2: Correspondance partielle si nécessaire
        if not best_match:
            print("\n🔗 Phase 2: Correspondance partielle")
            for i, item in enumerate(faq_data):
                input_words = set(word.strip().lower() for word in item['input'].split() if word.strip())
                message_words = set(word.strip().lower() for word in user_message_clean.split() if word.strip())
                
                common_words = input_words.intersection(message_words)
                
                if common_words:
                    score = sum(len(word) for word in common_words)
                    print(f"  🔗 Mots communs: {common_words} (score: {score})")
                    
                    if score > highest_score:
                        highest_score = score
                        best_match = item
                        print(f"  🏆 Nouveau meilleur score: {score}")
        
        # Résultat final
        print(f"\n📊 RÉSULTAT FINAL:")
        print(f"Score final: {highest_score}")
        
        if best_match and highest_score > 0:
            print(f"✅ CORRESPONDANCE TROUVÉE!")
            print(f"📋 Instruction: {best_match['instruction']}")
            print(f"🔑 Mots-clés: {best_match['input']}")
            print(f"💬 Réponse: {best_match['output'][:100]}...")
            return best_match['output']
        else:
            print(f"❌ AUCUNE CORRESPONDANCE")
            return None
            
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return None

def main():
    """
    Fonction principale de test
    """
    print("🤖 TEST DU CHATBOT MAROC CULTURES")
    print("=" * 60)
    
    # Chemin vers le fichier JSON
    faq_file = os.path.join('chatbot_app', 'faq_data.json')
    
    if not os.path.exists(faq_file):
        print(f"❌ Fichier non trouvé: {faq_file}")
        return
    
    # Tests de base
    test_messages = [
        "bonjour",
        "salut",
        "événements",
        "contact",
        "mawazine",
        "prix",
        "horaires",
        "date",
        "lieu",
        "merci",
        "au revoir",
        "aide",
        "festival",
        "spectacle"
    ]
    
    print(f"🧪 Lancement de {len(test_messages)} tests...")
    
    results = {}
    
    for message in test_messages:
        response = test_chatbot_logic(message, faq_file)
        results[message] = response is not None
        
        # Pause entre les tests
        print("\n" + "-" * 60)
    
    # Résumé des résultats
    print("\n📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = sum(results.values())
    total_count = len(results)
    success_rate = (success_count / total_count) * 100
    
    print(f"✅ Tests réussis: {success_count}/{total_count}")
    print(f"📈 Taux de réussite: {success_rate:.1f}%")
    
    print("\n📋 Détail par test:")
    for message, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} '{message}'")
    
    if success_rate < 80:
        print(f"\n⚠️ ATTENTION: Taux de réussite faible ({success_rate:.1f}%)")
        print("🔧 Vérifiez la logique de correspondance ou enrichissez le fichier JSON")
    else:
        print(f"\n🎉 EXCELLENT: Taux de réussite élevé ({success_rate:.1f}%)")
        print("✅ Le chatbot fonctionne correctement!")

if __name__ == "__main__":
    main()
