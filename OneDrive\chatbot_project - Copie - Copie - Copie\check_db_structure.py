#!/usr/bin/env python3
"""
Script pour vérifier la structure de la base de données
"""

import os
import django
import sys

# Configuration Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Message, Conversation
from django.db import connection

def check_database_structure():
    print("🔍 VÉRIFICATION DE LA STRUCTURE DE LA BASE DE DONNÉES")
    print("=" * 60)
    
    # Vérifier la structure de la table Message
    with connection.cursor() as cursor:
        print("\n📋 STRUCTURE DE LA TABLE chatbot_app_message:")
        cursor.execute("DESCRIBE chatbot_app_message")
        columns = cursor.fetchall()
        
        for column in columns:
            field_name = column[0]
            field_type = column[1]
            null_allowed = column[2]
            key_type = column[3]
            default_value = column[4]
            extra = column[5]
            
            print(f"   - {field_name}: {field_type} (NULL: {null_allowed}, KEY: {key_type})")
        
        print("\n📋 STRUCTURE DE LA TABLE chatbot_app_conversation:")
        cursor.execute("DESCRIBE chatbot_app_conversation")
        columns = cursor.fetchall()
        
        for column in columns:
            field_name = column[0]
            field_type = column[1]
            null_allowed = column[2]
            key_type = column[3]
            default_value = column[4]
            extra = column[5]
            
            print(f"   - {field_name}: {field_type} (NULL: {null_allowed}, KEY: {key_type})")
    
    # Vérifier les données existantes
    print(f"\n📊 DONNÉES EXISTANTES:")
    conversation_count = Conversation.objects.count()
    message_count = Message.objects.count()
    
    print(f"   - Conversations: {conversation_count}")
    print(f"   - Messages: {message_count}")
    
    if conversation_count > 0:
        print(f"\n📝 EXEMPLE DE CONVERSATIONS:")
        for conv in Conversation.objects.all()[:3]:
            print(f"   - ID: {conv.id}, Titre: {conv.title}, Date: {conv.date}")
            messages = Message.objects.filter(conversation=conv)
            print(f"     Messages: {len(messages)}")
            for msg in messages[:2]:
                print(f"       * {msg.role}: {msg.content[:50]}...")

if __name__ == "__main__":
    check_database_structure()
