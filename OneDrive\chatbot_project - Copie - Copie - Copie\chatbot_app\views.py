from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib import messages
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User as AuthUser
from django.conf import settings
import json
import re
import uuid
import os
from datetime import datetime
from .models import FAQ, User, Message, Conversation, Chatbot, Admin, Event, Historique
from .forms import UserForm, RegisterForm, EventForm

def landing(request):
    return render(request, 'chatbot_app/landing.html')  # Page d'accueil principale (landing page)

def home(request):
    return render(request, 'chatbot_app/home.html')  # Page d'accueil secondaire

def chat(request):
    context = {
        'current_time': datetime.now()
    }
    return render(request, 'chatbot_app/chat.html', context)

def features(request):
    return render(request, 'chatbot_app/features.html')

def events_list(request):
    """Vue pour afficher la liste des événements"""
    events = Event.objects.all().order_by('date_start')

    # Pagination
    paginator = Paginator(events, 6)  # 6 événements par page
    page_number = request.GET.get('page', 1)
    events_page = paginator.get_page(page_number)

    context = {
        'events': events_page,
    }

    return render(request, 'chatbot_app/events_list.html', context)

def event_detail(request, event_id):
    """Vue pour afficher les détails d'un événement"""
    event = get_object_or_404(Event, id=event_id)

    context = {
        'event': event,
    }

    return render(request, 'chatbot_app/event_detail.html', context)

def about(request):
    return render(request, 'chatbot_app/about.html')

def contact(request):
    return render(request, 'chatbot_app/contact.html')

def login_view(request):
    return render(request, 'chatbot_app/login.html')

def register(request):
    print("=== Vue d'inscription appelée ===")
    print(f"Méthode: {request.method}")

    if request.method == 'POST':
        print("Données POST:", request.POST)
        form = RegisterForm(request.POST)

        if form.is_valid():
            print("Formulaire valide!")
            try:
                user = form.save()
                print(f"Utilisateur créé: {user.username} (ID: {user.id})")

                # Connecter automatiquement l'utilisateur après l'inscription
                raw_password = form.cleaned_data.get('password1')
                user_auth = authenticate(username=user.username, password=raw_password)

                if user_auth:
                    print(f"Authentification réussie pour {user.username}")
                    login(request, user_auth)
                    messages.success(request, "Inscription réussie ! Vous êtes maintenant connecté.")
                    return redirect('home')
                else:
                    print(f"Échec de l'authentification pour {user.username}")
                    messages.warning(request, "Inscription réussie, mais la connexion automatique a échoué. Veuillez vous connecter manuellement.")
                    return redirect('login')
            except Exception as e:
                print(f"Erreur lors de la création de l'utilisateur: {e}")
                messages.error(request, f"Une erreur s'est produite lors de l'inscription: {e}")
        else:
            print("Formulaire invalide!")
            print("Erreurs:", form.errors)
            # Si le formulaire n'est pas valide, afficher les erreurs
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")
    else:
        form = RegisterForm()
        print("Formulaire vide créé")

    return render(request, 'chatbot_app/register.html', {'form': form})

# Admin Dashboard Views
def admin_dashboard(request):
    search_query = request.GET.get('search', '')

    if search_query:
        users = AuthUser.objects.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query)
        ).order_by('-date_joined')
    else:
        users = AuthUser.objects.all().order_by('-date_joined')

    paginator = Paginator(users, 10)  # 10 users per page
    page_number = request.GET.get('page', 1)
    users_page = paginator.get_page(page_number)

    context = {
        'users': users_page,
        'search_query': search_query
    }

    return render(request, 'chatbot_app/admin_dashboard.html', context)

def admin_add_user(request):
    """Vue pour ajouter un utilisateur Django auth"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')

        try:
            # Créer un nouvel utilisateur Django auth
            user = AuthUser.objects.create_user(
                username=username,
                email=email,
                password=password
            )
            messages.success(request, f"L'utilisateur '{username}' a été créé avec succès.")
            return redirect('admin_dashboard')
        except Exception as e:
            messages.error(request, f"Erreur lors de la création de l'utilisateur: {e}")
            # Réafficher le formulaire avec les données soumises
            form = UserForm(request.POST)
    else:
        form = UserForm()

    context = {
        'user_form': form,
        'action': 'add'
    }

    return render(request, 'chatbot_app/admin_user_form.html', context)

# Fonction de modification d'utilisateur supprimée

def admin_delete_user(request, user_id):
    """Vue pour supprimer un utilisateur Django auth"""
    try:
        user = get_object_or_404(AuthUser, id=user_id)
        username = user.username
        user.delete()
        messages.success(request, f"L'utilisateur '{username}' a été supprimé avec succès.")
    except Exception as e:
        messages.error(request, f"Erreur lors de la suppression de l'utilisateur: {e}")

    return redirect('admin_dashboard')

# Vues pour la gestion des événements dans l'admin
def admin_events_list(request):
    """Vue pour afficher la liste des événements dans l'admin"""
    search_query = request.GET.get('search', '')

    if search_query:
        events = Event.objects.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(location__icontains=search_query)
        )
    else:
        events = Event.objects.all().order_by('-date_start')

    paginator = Paginator(events, 10)  # 10 événements par page
    page_number = request.GET.get('page', 1)
    events_page = paginator.get_page(page_number)

    context = {
        'events': events_page,
        'search_query': search_query
    }

    return render(request, 'chatbot_app/admin_events_list.html', context)

def admin_add_event(request):
    """Vue pour ajouter un événement"""
    if request.method == 'POST':
        form = EventForm(request.POST)
        if form.is_valid():
            event = form.save()
            messages.success(request, f"L'événement '{event.title}' a été ajouté avec succès.")
            return redirect('admin_events_list')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")
    else:
        form = EventForm()

    context = {
        'event_form': form,
        'action': 'add'
    }

    return render(request, 'chatbot_app/admin_event_form.html', context)

def admin_edit_event(request, event_id):
    """Vue pour modifier un événement"""
    event = get_object_or_404(Event, id=event_id)

    if request.method == 'POST':
        form = EventForm(request.POST, instance=event)
        if form.is_valid():
            event = form.save()
            messages.success(request, f"L'événement '{event.title}' a été modifié avec succès.")
            return redirect('admin_events_list')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")
    else:
        form = EventForm(instance=event)

    context = {
        'event_form': form,
        'action': 'edit',
        'event': event
    }

    return render(request, 'chatbot_app/admin_event_form.html', context)

def admin_delete_event(request, event_id):
    """Vue pour supprimer un événement"""
    event = get_object_or_404(Event, id=event_id)
    event_title = event.title

    event.delete()
    messages.success(request, f"L'événement '{event_title}' a été supprimé avec succès.")

    return redirect('admin_events_list')

def chat_history(request):
    """Vue pour afficher l'historique des conversations du chat - VERSION SIMPLIFIÉE"""
    try:
        print(f"📚 CHARGEMENT DE L'HISTORIQUE")
        print(f"   - Utilisateur connecté: {request.user.is_authenticated}")

        # Utiliser SQL direct pour récupérer toutes les conversations récentes
        import pymysql
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()

        if request.user.is_authenticated:
            # Pour les utilisateurs connectés
            user_id = request.user.id
            print(f"   - Recherche pour utilisateur ID: {user_id}")

            cursor.execute("""
                SELECT c.id, c.title, c.date, m.content, m.role, m.time
                FROM chatbot_app_conversation c
                LEFT JOIN chatbot_app_message m ON c.id = m.conversation_id
                WHERE c.auth_user_id = %s
                ORDER BY c.date DESC, m.time ASC
            """, (user_id,))

        else:
            # Pour les utilisateurs non connectés - trouver la session la plus récente
            session_id = request.session.get('chat_session_id')

            if not session_id:
                # Trouver automatiquement la session la plus récente
                cursor.execute("""
                    SELECT session_id, COUNT(*) as count, MAX(created_at) as latest
                    FROM chatbot_app_conversation
                    WHERE session_id IS NOT NULL
                    GROUP BY session_id
                    ORDER BY latest DESC
                    LIMIT 1
                """)
                result = cursor.fetchone()
                if result:
                    session_id = result[0]
                    session_count = result[1]
                    request.session['chat_session_id'] = session_id
                    print(f"   🔄 Session auto-détectée: {session_id} ({session_count} conversations)")
                else:
                    print(f"   ❌ Aucune session trouvée")
                    session_id = None

            if session_id:
                print(f"   - Recherche pour session: {session_id}")
                cursor.execute("""
                    SELECT c.id, c.title, c.date, m.content, m.role, m.time
                    FROM chatbot_app_conversation c
                    LEFT JOIN chatbot_app_message m ON c.id = m.conversation_id
                    WHERE c.session_id = %s
                    ORDER BY c.date DESC, m.time ASC
                """, (session_id,))
            else:
                print(f"   ❌ Pas de session disponible")
                cursor.execute("SELECT NULL, NULL, NULL, NULL, NULL, NULL WHERE 1=0")  # Requête vide

        results = cursor.fetchall()
        print(f"   📊 Résultats trouvés: {len(results)} messages")

        # Organiser les données par date et conversation
        conversations_data = {}

        for row in results:
            conv_id, title, date, content, role, time = row

            if content and conv_id:  # Si il y a un message et une conversation
                date_key = date.strftime('%Y-%m-%d')

                if date_key not in conversations_data:
                    conversations_data[date_key] = []

                # Trouver ou créer la conversation dans la liste
                conversation_found = False
                for conv_data in conversations_data[date_key]:
                    if conv_data['conversation']['id'] == conv_id:
                        conv_data['messages'].append({
                            'content': content,
                            'role': role,
                            'time': time
                        })
                        conversation_found = True
                        break

                if not conversation_found:
                    conversations_data[date_key].append({
                        'conversation': {
                            'id': conv_id,
                            'title': title,
                            'date': date
                        },
                        'messages': [{
                            'content': content,
                            'role': role,
                            'time': time
                        }]
                    })

        # Trier les conversations par date (la plus récente en premier)
        sorted_conversations = dict(sorted(conversations_data.items(), key=lambda x: x[0], reverse=True))

        print(f"   📋 Conversations organisées: {len(sorted_conversations)} dates")
        for date_key, convs in sorted_conversations.items():
            print(f"      - {date_key}: {len(convs)} conversations")

        context = {
            'conversations_data': sorted_conversations,
        }

    except Exception as e:
        print(f"❌ Erreur récupération historique: {e}")
        import traceback
        traceback.print_exc()
        context = {
            'conversations_data': {},
            'message': f"Erreur lors du chargement de l'historique: {e}"
        }

    return render(request, 'chatbot_app/chat_history.html', context)

def clear_chat_history(request):
    """Vue pour effacer l'historique des conversations - VERSION SIMPLIFIÉE"""
    if request.method == 'POST':
        try:
            import pymysql
            conn = pymysql.connect(
                host='localhost',
                user='root',
                password='',
                database='chatbot_maroc_cultures'
            )
            cursor = conn.cursor()

            deleted_conversations = 0
            deleted_messages = 0
            deleted_historique = 0

            print(f"🗑️ DÉBUT DE SUPPRESSION D'HISTORIQUE")
            print(f"   - Utilisateur connecté: {request.user.is_authenticated}")

            if request.user.is_authenticated:
                # Pour les utilisateurs connectés
                user_id = request.user.id
                print(f"🔍 Suppression pour utilisateur ID: {user_id}")

                # Supprimer les historiques
                cursor.execute("DELETE FROM chatbot_app_historique WHERE id_utilisateur_id = %s", (user_id,))
                deleted_historique = cursor.rowcount

                # Supprimer les conversations et messages
                cursor.execute("SELECT id FROM chatbot_app_conversation WHERE auth_user_id = %s", (user_id,))
                conversation_ids = [row[0] for row in cursor.fetchall()]

                if conversation_ids:
                    placeholders = ','.join(['%s'] * len(conversation_ids))
                    cursor.execute(f"DELETE FROM chatbot_app_message WHERE conversation_id IN ({placeholders})", conversation_ids)
                    deleted_messages = cursor.rowcount

                    cursor.execute("DELETE FROM chatbot_app_conversation WHERE auth_user_id = %s", (user_id,))
                    deleted_conversations = cursor.rowcount

            else:
                # Pour les utilisateurs non connectés - SUPPRIMER TOUT L'HISTORIQUE RÉCENT
                print(f"🔍 Suppression pour utilisateur non connecté")

                # Trouver la session la plus récente
                cursor.execute("""
                    SELECT session_id, COUNT(*) as count
                    FROM chatbot_app_conversation
                    WHERE session_id IS NOT NULL
                    GROUP BY session_id
                    ORDER BY MAX(created_at) DESC
                    LIMIT 1
                """)
                result = cursor.fetchone()

                if result:
                    session_id = result[0]
                    session_count = result[1]
                    print(f"   🎯 Session trouvée: {session_id} ({session_count} conversations)")

                    # Sauvegarder cette session dans Django pour cohérence
                    request.session['chat_session_id'] = session_id

                    # Supprimer les historiques
                    cursor.execute("DELETE FROM chatbot_app_historique WHERE session_id = %s", (session_id,))
                    deleted_historique = cursor.rowcount

                    # Supprimer les conversations et messages
                    cursor.execute("SELECT id FROM chatbot_app_conversation WHERE session_id = %s", (session_id,))
                    conversation_ids = [row[0] for row in cursor.fetchall()]

                    if conversation_ids:
                        placeholders = ','.join(['%s'] * len(conversation_ids))
                        cursor.execute(f"DELETE FROM chatbot_app_message WHERE conversation_id IN ({placeholders})", conversation_ids)
                        deleted_messages = cursor.rowcount

                        cursor.execute("DELETE FROM chatbot_app_conversation WHERE session_id = %s", (session_id,))
                        deleted_conversations = cursor.rowcount
                else:
                    print("   ❌ Aucune session trouvée")

            # Valider les changements
            conn.commit()
            cursor.close()
            conn.close()

            # Afficher les résultats
            total_deleted = deleted_conversations + deleted_messages + deleted_historique
            print(f"📊 RÉSULTATS:")
            print(f"   - Conversations supprimées: {deleted_conversations}")
            print(f"   - Messages supprimés: {deleted_messages}")
            print(f"   - Historiques supprimés: {deleted_historique}")
            print(f"   - Total: {total_deleted}")

            if total_deleted > 0:
                messages.success(request, f"✅ Historique effacé avec succès ! ({deleted_conversations} conversations, {deleted_messages} messages, {deleted_historique} entrées d'historique supprimées)")
                print(f"✅ SUPPRESSION RÉUSSIE!")
            else:
                messages.info(request, "Aucun historique à supprimer.")
                print("ℹ️ AUCUN ÉLÉMENT À SUPPRIMER")

        except Exception as e:
            print(f"❌ ERREUR: {e}")
            messages.error(request, f"Erreur lors de la suppression de l'historique: {str(e)}")

        return redirect('chat_history')

    return redirect('chat_history')

import json
import os
from django.conf import settings
from django.http import JsonResponse

def process_message(request):
    if request.method == 'POST':
        user_message = request.POST.get('message', '').strip()

        # Récupérer ou créer un identifiant de session pour les utilisateurs non connectés
        if not request.session.get('chat_session_id'):
            request.session['chat_session_id'] = str(uuid.uuid4())

        session_id = request.session.get('chat_session_id')

        # Récupérer ou créer une conversation
        conversation = None
        conversation_id = request.POST.get('conversation_id')

        if conversation_id:
            try:
                conversation = Conversation.objects.get(id=conversation_id)
            except Conversation.DoesNotExist:
                pass

        if not conversation:
            # Créer une nouvelle conversation
            conversation = Conversation.objects.create(
                title=f"Conversation du {datetime.now().strftime('%d/%m/%Y')}",
                auth_user=request.user if request.user.is_authenticated else None,
                session_id=session_id if not request.user.is_authenticated else None
            )

        # Sauvegarder le message de l'utilisateur
        try:
            # Utiliser SQL direct pour éviter les problèmes de modèles
            import pymysql
            conn = pymysql.connect(
                host='localhost',
                user='root',
                password='',
                database='chatbot_maroc_cultures'
            )
            cursor = conn.cursor()

            # Insérer le message utilisateur
            cursor.execute("""
                INSERT INTO chatbot_app_message (content, role, conversation_id, auth_user_id, time, is_user, timestamp)
                VALUES (%s, %s, %s, %s, NOW(), 1, NOW())
            """, (user_message, 'user', conversation.id, request.user.id if request.user.is_authenticated else None))

            conn.commit()
            cursor.close()
            conn.close()
        except Exception as e:
            print(f"Erreur sauvegarde message utilisateur: {e}")

        # Générer la réponse du bot - D'abord JSON, puis Mistral AI en fallback
        bot_response = None

        # ÉTAPE 1: Rechercher dans le fichier JSON d'abord
        print(f"📋 Recherche dans le fichier JSON pour: {user_message[:50]}...")
        try:
            faq_file = os.path.join(settings.BASE_DIR, 'chatbot_app', 'faq_data.json')

            with open(faq_file, 'r', encoding='utf-8') as f:
                faq_data = json.load(f)

            # SOLUTION AMÉLIORÉE : Algorithme de correspondance plus efficace
            best_match = None
            highest_score = 0
            user_message_clean = user_message.lower().strip()

            print(f"🔍 Analyse du message: '{user_message_clean}'")

            # Méthode 1: Correspondance directe avec les mots-clés d'input
            for item in faq_data:
                input_keywords = item['input'].lower().split()

                # Vérifier chaque mot-clé individuellement
                for keyword in input_keywords:
                    keyword = keyword.strip()
                    if keyword and keyword in user_message_clean:
                        # Score basé sur la longueur du mot-clé (mots plus longs = plus spécifiques)
                        score = len(keyword) * 10

                        if score > highest_score:
                            highest_score = score
                            best_match = item
                            print(f"🎯 Correspondance trouvée: '{keyword}' → '{item['instruction']}' (score: {score})")

            # Méthode 2: Si pas de correspondance directe, essayer correspondance partielle
            if not best_match:
                print("🔄 Aucune correspondance directe, essai correspondance partielle...")

                for item in faq_data:
                    # Diviser les mots-clés et le message en mots individuels
                    input_words = set(word.strip().lower() for word in item['input'].split() if word.strip())
                    message_words = set(word.strip().lower() for word in user_message_clean.split() if word.strip())

                    # Calculer l'intersection
                    common_words = input_words.intersection(message_words)

                    if common_words:
                        # Score basé sur le nombre et la longueur des mots communs
                        score = sum(len(word) for word in common_words)

                        if score > highest_score:
                            highest_score = score
                            best_match = item
                            print(f"🔗 Correspondance partielle: {common_words} → '{item['instruction']}' (score: {score})")

            # Vérifier si on a trouvé une correspondance valide
            if best_match and highest_score > 0:
                bot_response = best_match['output']
                print(f"✅ RÉPONSE TROUVÉE dans le JSON!")
                print(f"📋 Instruction: {best_match['instruction']}")
                print(f"📝 Score final: {highest_score}")
                print(f"💬 Réponse: {bot_response[:100]}...")
            else:
                print(f"❌ Aucune correspondance trouvée dans le JSON (score: {highest_score})")

        except Exception as json_error:
            print(f"❌ Erreur lecture JSON: {json_error}")

        # ÉTAPE 2: Si pas de réponse JSON, utiliser Mistral AI
        if not bot_response:
            print(f"🤖 Aucune réponse JSON trouvée, utilisation de Mistral AI...")
            try:
                # Vérifier si requests est disponible
                import requests
                from .mistral_api import get_mistral_response_with_context

                # Utiliser Mistral AI avec le contexte utilisateur
                user_id = request.user.id if request.user.is_authenticated else None
                print(f"👤 User ID: {user_id}")

                mistral_response = get_mistral_response_with_context(user_message, user_id)
                print(f"📤 Réponse Mistral reçue: {mistral_response[:100]}...")

                # Vérifier si Mistral a répondu correctement
                if not ("Erreur" in mistral_response or "erreur" in mistral_response or "🔑" in mistral_response or "❌" in mistral_response):
                    bot_response = mistral_response
                    print(f"✅ Mistral AI a répondu avec succès !")
                else:
                    print(f"⚠️ Mistral API a échoué: {mistral_response}")

            except ImportError:
                print(f"⚠️ Module 'requests' non installé, utilisation de réponses prédéfinies...")
                # Réponses prédéfinies pour les questions courantes
                predefined_responses = {
                    # Mots simples fréquents
                    "date": "📅 Pour connaître les dates de nos événements, consultez notre page événements ou demandez-moi des informations sur un festival spécifique comme Mawazine ! 🇲🇦",
                    "lieu": "📍 Nos événements se déroulent principalement au Maroc (Rabat, Casablanca, Marrakech, Fès). Notre siège est à Rabat. Quel lieu vous intéresse ? 🇲🇦",
                    "prix": "💰 Les prix varient selon l'événement. Certains spectacles sont gratuits, d'autres payants. Consultez notre site pour les tarifs détaillés ! 🇲🇦",
                    "horaire": "🕐 Les horaires dépendent de l'événement. Généralement, nos spectacles commencent en soirée. Précisez quel événement vous intéresse ! 🇲🇦",
                    "contact": "📞 Contactez-nous au +1 (514) 555-1234 ou par email à <EMAIL>. Notre adresse : 456 Avenue Culturelle, Montréal, QC H2X 3Y7, Canada. 🇲🇦",
                    "adresse": "🏢 Notre siège est situé au 456 Avenue Culturelle, Montréal, QC H2X 3Y7, Canada. Au Maroc, nous organisons des événements dans plusieurs villes ! 🇲🇦",

                    # Culture marocaine
                    "tajine": "🍲 Le tajine est un plat emblématique du Maroc, cuit lentement dans un plat en terre cuite conique. Il existe de nombreuses variantes : tajine aux olives, aux pruneaux, au citron confit... Chaque région a ses spécialités ! 🇲🇦",
                    "couscous": "🥘 Le couscous est le plat national du Maroc ! Traditionnellement servi le vendredi, il se compose de semoule de blé dur accompagnée de légumes et de viande. Le couscous royal combine agneau, poulet et merguez. 🇲🇦",
                    "marrakech": "🏛️ Marrakech, la 'Perle du Sud', est une ville impériale fondée en 1062. Célèbre pour sa place Jemaa el-Fna, ses souks colorés, et ses magnifiques jardins comme ceux de Majorelle. 🇲🇦",
                    "fès": "🕌 Fès est la capitale spirituelle du Maroc, abritant la plus ancienne université du monde (Al Quaraouiyine). Sa médina est un labyrinthe fascinant d'artisans et de traditions millénaires. 🇲🇦",
                    "artisanat": "🎨 L'artisanat marocain est d'une richesse exceptionnelle : tapis berbères, poteries de Salé, cuir de Fès, bijoux en argent, zellige... Chaque région a ses spécialités transmises de génération en génération. 🇲🇦",
                    "musique": "🎵 La musique marocaine est très diverse : le chaâbi populaire, le gnawa spirituel, l'andalou raffiné, l'ahidous berbère... Chaque style raconte l'histoire et l'âme du Maroc. 🇲🇦",

                    # Événements
                    "mawazine": "🎪 Mawazine est notre festival phare ! Le plus grand festival musical d'Afrique, attirant des millions de spectateurs et des artistes internationaux comme Beyoncé, Justin Bieber... 🇲🇦",
                    "festival": "🎭 Nous organisons plusieurs festivals : Mawazine (musique), Festival du Théâtre des Cultures, et Génération Mawazine pour les jeunes talents ! 🇲🇦",
                    "spectacle": "🎬 Nos spectacles incluent des concerts, du théâtre, de la danse traditionnelle et moderne. Consultez notre programmation ! 🇲🇦"
                }

                # Chercher une réponse prédéfinie
                message_lower = user_message.lower()
                for keyword, response in predefined_responses.items():
                    if keyword in message_lower:
                        bot_response = response
                        print(f"✅ Réponse prédéfinie trouvée pour '{keyword}'")
                        break

            except Exception as mistral_error:
                print(f"❌ Erreur Mistral API: {mistral_error}")

        # ÉTAPE 3: Réponse par défaut si tout échoue
        if not bot_response:
            print(f"🔄 Utilisation de la réponse par défaut")
            bot_response = "🇲🇦 Bonjour ! Je suis votre assistant culturel Maroc Cultures. Je peux vous parler de l'histoire, la gastronomie, l'artisanat, les villes et traditions du Maroc. Que souhaitez-vous découvrir ?"

        # Sauvegarder la réponse du bot
        try:
            import pymysql
            conn = pymysql.connect(
                host='localhost',
                user='root',
                password='',
                database='chatbot_maroc_cultures'
            )
            cursor = conn.cursor()

            # Insérer la réponse du bot
            cursor.execute("""
                INSERT INTO chatbot_app_message (content, role, conversation_id, auth_user_id, time, is_user, timestamp)
                VALUES (%s, %s, %s, %s, NOW(), 0, NOW())
            """, (bot_response, 'bot', conversation.id, request.user.id if request.user.is_authenticated else None))

            conn.commit()
            cursor.close()
            conn.close()
        except Exception as e:
            print(f"Erreur sauvegarde message bot: {e}")

        # Créer ou mettre à jour l'entrée dans l'historique
        try:
            historique, created = Historique.objects.get_or_create(
                id_conversation=conversation,
                id_utilisateur=request.user if request.user.is_authenticated else None,
                session_id=session_id if not request.user.is_authenticated else None,
                defaults={
                    'id_conversation': conversation,
                    'id_utilisateur': request.user if request.user.is_authenticated else None,
                    'session_id': session_id if not request.user.is_authenticated else None,
                }
            )
            if not created:
                # Mettre à jour la date d'accès
                historique.save()  # Cela met à jour automatiquement date_acces grâce à auto_now=True
        except Exception as e:
            print(f"Erreur création historique: {e}")

        return JsonResponse({
            'response': bot_response,
            'conversation_id': conversation.id
        })

    return JsonResponse({'response': "Erreur: méthode non autorisée"}, status=405)
