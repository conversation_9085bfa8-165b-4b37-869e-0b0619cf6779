#!/usr/bin/env python3
"""
Final verification that MySQL integration is working correctly
"""

import pymysql
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.db import connection

def verify_mysql_integration():
    """Comprehensive verification of MySQL integration"""
    
    print("🚀 Final MySQL Integration Verification")
    print("=" * 60)
    
    # Test 1: Direct MySQL connection
    print("\n1️⃣ Testing direct MySQL connection...")
    try:
        mysql_conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures',
            charset='utf8mb4'
        )
        cursor = mysql_conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"   ✅ MySQL Version: {version}")
        
        cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'chatbot_maroc_cultures'")
        table_count = cursor.fetchone()[0]
        print(f"   ✅ Tables in database: {table_count}")
        
        cursor.close()
        mysql_conn.close()
    except Exception as e:
        print(f"   ❌ MySQL connection failed: {e}")
        return False
    
    # Test 2: Django database connection
    print("\n2️⃣ Testing Django database connection...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        if result and result[0] == 1:
            print("   ✅ Django MySQL connection successful")
            
            # Get database info
            db_config = connection.settings_dict
            print(f"   📊 Engine: {db_config.get('ENGINE')}")
            print(f"   📊 Database: {db_config.get('NAME')}")
            print(f"   📊 Host: {db_config.get('HOST')}")
            print(f"   📊 Port: {db_config.get('PORT')}")
        else:
            print("   ❌ Django connection test failed")
            return False
    except Exception as e:
        print(f"   ❌ Django connection failed: {e}")
        return False
    
    # Test 3: Django models
    print("\n3️⃣ Testing Django models...")
    try:
        from chatbot_app.models import FAQ, User, Event
        
        # Test FAQ model
        faq_count = FAQ.objects.count()
        print(f"   ✅ FAQ model working - {faq_count} records")
        
        # Test User model
        user_count = User.objects.count()
        print(f"   ✅ User model working - {user_count} records")
        
        # Test Event model
        event_count = Event.objects.count()
        print(f"   ✅ Event model working - {event_count} records")
        
        # Test Django auth user
        from django.contrib.auth.models import User as AuthUser
        auth_user_count = AuthUser.objects.count()
        print(f"   ✅ Auth User model working - {auth_user_count} records")
        
    except Exception as e:
        print(f"   ❌ Django models test failed: {e}")
        return False
    
    # Test 4: Migrations status
    print("\n4️⃣ Checking migrations status...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_migrations")
            migration_count = cursor.fetchone()[0]
            print(f"   ✅ Applied migrations: {migration_count}")
            
            cursor.execute("SELECT app, COUNT(*) FROM django_migrations GROUP BY app")
            apps = cursor.fetchall()
            for app, count in apps:
                print(f"   📋 {app}: {count} migrations")
                
    except Exception as e:
        print(f"   ❌ Migrations check failed: {e}")
        return False
    
    # Test 5: Admin user
    print("\n5️⃣ Checking admin user...")
    try:
        from django.contrib.auth.models import User as AuthUser
        admin_users = AuthUser.objects.filter(is_superuser=True)
        
        if admin_users.exists():
            admin = admin_users.first()
            print(f"   ✅ Admin user exists: {admin.username}")
            print(f"   📧 Email: {admin.email}")
            print(f"   🔑 Is superuser: {admin.is_superuser}")
            print(f"   👤 Is staff: {admin.is_staff}")
        else:
            print("   ⚠️  No admin user found")
            
    except Exception as e:
        print(f"   ❌ Admin user check failed: {e}")
        return False
    
    print("\n🎉 ALL TESTS PASSED!")
    print("=" * 60)
    print("✅ MySQL is successfully integrated with Django!")
    print("✅ All database tables are created")
    print("✅ All migrations are applied")
    print("✅ Django models are working")
    print("✅ Admin user is created")
    print()
    print("🌐 Your application is ready!")
    print("   Landing page: http://127.0.0.1:8000/")
    print("   Admin panel: http://127.0.0.1:8000/admin/")
    print("   Admin credentials: admin / admin123")
    print()
    print("🚀 Next steps:")
    print("   1. Visit the landing page to see your chatbot")
    print("   2. Login to admin panel to manage content")
    print("   3. Add FAQ entries for the chatbot")
    print("   4. Create events and manage users")
    
    return True

if __name__ == "__main__":
    verify_mysql_integration()
