#!/usr/bin/env python3
"""
🧪 Test du Statut de l'API Mistral
"""

import sys
import os

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test des imports nécessaires"""
    print("🔍 Test des imports...")
    
    try:
        import requests
        print("✅ requests importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import requests: {e}")
        return False
    
    try:
        import django
        print("✅ django importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import django: {e}")
        return False
    
    return True

def test_mistral_api_key():
    """Test de la clé API Mistral"""
    print("\n🔑 Test de la clé API Mistral...")
    
    # Vérifier la clé dans le code
    api_key = "TyWSqM7VMMzjBygUrTeNS0SuZicudsD2"
    
    if api_key and len(api_key) > 10:
        print(f"✅ Clé API trouvée: {api_key[:10]}...")
        return True
    else:
        print("❌ Clé API non trouvée ou invalide")
        return False

def test_mistral_direct():
    """Test direct de l'API Mistral"""
    print("\n🌐 Test direct de l'API Mistral...")
    
    try:
        import requests
        
        api_key = "TyWSqM7VMMzjBygUrTeNS0SuZicudsD2"
        url = "https://api.mistral.ai/v1/chat/completions"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": "mistral-small-latest",
            "messages": [
                {"role": "system", "content": "Tu es un assistant culturel du Maroc."},
                {"role": "user", "content": "Dis bonjour en une phrase"}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        print("📤 Envoi de la requête...")
        response = requests.post(url, headers=headers, json=data, timeout=15)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            bot_response = result['choices'][0]['message']['content']
            print(f"✅ Réponse reçue: {bot_response}")
            return True
        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_django_integration():
    """Test de l'intégration Django"""
    print("\n🔧 Test de l'intégration Django...")
    
    try:
        # Configuration Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
        import django
        django.setup()
        
        from chatbot_app.mistral_api import get_mistral_response
        
        print("✅ Module mistral_api importé avec succès")
        
        # Test d'une réponse simple
        response = get_mistral_response("Bonjour")
        
        if response and len(response) > 10:
            print(f"✅ Réponse Django reçue: {response[:50]}...")
            return True
        else:
            print(f"❌ Réponse Django invalide: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur intégration Django: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 TEST DU STATUT DE L'API MISTRAL")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Clé API", test_mistral_api_key),
        ("API Directe", test_mistral_direct),
        ("Intégration Django", test_django_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, "✅" if result else "❌"))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, "❌"))
    
    # Résumé
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*50)
    
    for test_name, status in results:
        print(f"{status} {test_name}")
    
    # Conclusion
    success_count = sum(1 for _, status in results if status == "✅")
    total_tests = len(results)
    
    print(f"\n🎯 RÉSULTAT: {success_count}/{total_tests} tests réussis")
    
    if success_count == total_tests:
        print("🎉 L'API Mistral est ACTIVE et fonctionnelle !")
    elif success_count >= 2:
        print("⚠️  L'API Mistral fonctionne partiellement")
    else:
        print("❌ L'API Mistral n'est PAS active")

if __name__ == "__main__":
    main()
