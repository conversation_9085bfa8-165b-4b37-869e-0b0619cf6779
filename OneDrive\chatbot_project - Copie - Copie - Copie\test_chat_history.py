#!/usr/bin/env python
"""
Test pour vérifier que l'historique des conversations fonctionne
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
import json
import pymysql

def test_chat_history():
    """Test pour vérifier que l'historique des conversations fonctionne"""
    print("🗂️ Test de l'historique des conversations")
    print("=" * 50)
    
    # Créer un client de test Django
    client = Client()
    
    # Test 1: Envoyer quelques messages pour créer des conversations
    print("\n📝 Test 1: Création de conversations...")
    
    test_messages = [
        "bonjour",
        "comment allez-vous ?",
        "merci",
        "au revoir"
    ]
    
    conversation_ids = []
    
    for i, message in enumerate(test_messages, 1):
        print(f"   Envoi du message {i}: '{message}'")
        
        try:
            response = client.post('/process_message/', {
                'message': message
            })
            
            if response.status_code == 200:
                data = json.loads(response.content)
                conversation_id = data.get('conversation_id')
                conversation_ids.append(conversation_id)
                print(f"   ✅ Message envoyé - Conversation ID: {conversation_id}")
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    # Test 2: Vérifier la base de données directement
    print(f"\n🔍 Test 2: Vérification de la base de données...")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        # Compter les conversations
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count = cursor.fetchone()[0]
        print(f"   📊 Conversations en base: {conv_count}")
        
        # Compter les messages
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count = cursor.fetchone()[0]
        print(f"   📊 Messages en base: {msg_count}")
        
        # Afficher les dernières conversations
        cursor.execute("""
            SELECT id, title, date, session_id 
            FROM chatbot_app_conversation 
            ORDER BY date DESC 
            LIMIT 5
        """)
        conversations = cursor.fetchall()
        
        print(f"   📋 Dernières conversations:")
        for conv in conversations:
            conv_id, title, date, session_id = conv
            print(f"      ID: {conv_id}, Titre: {title}, Date: {date}, Session: {session_id[:8] if session_id else 'N/A'}...")
        
        # Afficher les derniers messages
        cursor.execute("""
            SELECT content, role, conversation_id, time 
            FROM chatbot_app_message 
            ORDER BY time DESC 
            LIMIT 10
        """)
        messages = cursor.fetchall()
        
        print(f"   💬 Derniers messages:")
        for msg in messages:
            content, role, conv_id, time = msg
            print(f"      {role}: {content[:30]}... (Conv: {conv_id}, {time})")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
    
    # Test 3: Tester la page d'historique
    print(f"\n🌐 Test 3: Test de la page d'historique...")
    
    try:
        response = client.get('/chat/history/')
        
        if response.status_code == 200:
            print(f"   ✅ Page d'historique accessible (Code: {response.status_code})")
            
            # Vérifier le contenu de la réponse
            content = response.content.decode('utf-8')
            
            if 'Historique des conversations' in content:
                print(f"   ✅ Titre de la page trouvé")
            else:
                print(f"   ⚠️ Titre de la page non trouvé")
                
            if 'conversation-date' in content or 'no-history' in content:
                print(f"   ✅ Structure de la page correcte")
            else:
                print(f"   ⚠️ Structure de la page incorrecte")
                
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print(f"\n🎯 Résumé:")
    print(f"   - Messages de test envoyés: {len(test_messages)}")
    print(f"   - Conversations créées: {len(set(conversation_ids))}")
    print(f"   - Page d'historique: Accessible")
    
    print(f"\n✅ Test terminé ! Vous pouvez maintenant vérifier l'historique à:")
    print(f"   http://127.0.0.1:8000/chat/history/")

if __name__ == "__main__":
    test_chat_history()
