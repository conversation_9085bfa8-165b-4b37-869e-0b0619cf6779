#!/usr/bin/env python
"""
Script pour corriger la colonne created_at
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

import pymysql

def fix_created_at_column():
    """Corrige la colonne created_at pour qu'elle ait une valeur par défaut"""
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        print("🔧 Correction de la colonne 'created_at' dans 'chatbot_app_conversation'...")
        
        # Modifier la colonne created_at pour avoir une valeur par défaut
        try:
            cursor.execute("""
                ALTER TABLE chatbot_app_conversation 
                MODIFY COLUMN created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6)
            """)
            print("✅ Colonne 'created_at' modifiée avec valeur par défaut")
        except Exception as e:
            print(f"⚠️ Erreur lors de la modification de 'created_at': {e}")
        
        # Vérifier et corriger aussi la table message si nécessaire
        print("🔧 Vérification de la table 'chatbot_app_message'...")
        
        cursor.execute("DESCRIBE chatbot_app_message")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        if 'conversation_id' in column_names:
            # Modifier la colonne conversation_id pour permettre NULL temporairement
            try:
                cursor.execute("""
                    ALTER TABLE chatbot_app_message 
                    MODIFY COLUMN conversation_id INT(11) NULL
                """)
                print("✅ Colonne 'conversation_id' modifiée pour permettre NULL")
            except Exception as e:
                print(f"⚠️ Erreur: {e}")
        
        # Valider les changements
        conn.commit()
        print("\n✅ Toutes les corrections ont été appliquées!")
        
        # Afficher la structure mise à jour
        cursor.execute("DESCRIBE chatbot_app_conversation")
        columns = cursor.fetchall()
        
        print("\n📋 Structure mise à jour de 'chatbot_app_conversation':")
        print("Column Name | Type | Null | Key | Default | Extra")
        print("-" * 60)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_created_at_column()
