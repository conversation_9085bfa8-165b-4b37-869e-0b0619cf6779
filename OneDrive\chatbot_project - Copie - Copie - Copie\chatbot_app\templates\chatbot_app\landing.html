{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Maroc Cultures - Votre Portail Culturel Marocain au Canada{% endblock %}

{% block extra_css %}
<style>
    /* General Styles */
    body {
        font-family: 'Poppins', sans-serif;
        color: #333;
        overflow-x: hidden;
    }

    /* Hero Section */
    .hero {
        position: relative;
        height: 80vh;
        min-height: 450px;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg,
            rgba(192, 57, 43, 0.9) 0%,
            rgba(192, 57, 43, 0.8) 50%,
            rgba(39, 174, 96, 0.7) 100%),
            url('https://images.unsplash.com/photo-1489493585363-d69421e0edd3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        background-size: cover;
        background-position: center;
        color: white;
        overflow: hidden;
    }

    .hero-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,20 L20,0 L40,20 L20,40 Z" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="1"/></svg>');
        background-size: 40px 40px;
        opacity: 0.5;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 700px;
        margin: 0 auto;
        text-align: center;
        padding: 0 15px;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        font-weight: 300;
    }

    .hero-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-primary {
        background-color: #27ae60;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 0.95rem;
    }

    .btn-primary:hover {
        background-color: #219653;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        color: white;
    }

    .btn-secondary {
        background-color: transparent;
        color: white;
        border: 2px solid white;
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 0.95rem;
    }

    .btn-secondary:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        color: white;
    }

    /* Features Section */
    .features {
        padding: 70px 0;
        background-color: #f8f9fa;
    }

    .section-title {
        text-align: center;
        margin-bottom: 40px;
    }

    .section-title h2 {
        font-size: 2.2rem;
        font-weight: 700;
        color: #c0392b;
        margin-bottom: 10px;
        position: relative;
        display: inline-block;
    }

    .section-title h2::after {
        content: '';
        position: absolute;
        width: 60px;
        height: 3px;
        background: linear-gradient(to right, #c0392b, #27ae60);
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
    }

    .section-title p {
        font-size: 1rem;
        color: #666;
        max-width: 650px;
        margin: 0 auto;
    }

    .feature-card {
        background-color: white;
        border-radius: 8px;
        padding: 30px 20px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    }

    .feature-icon {
        width: 65px;
        height: 65px;
        background: linear-gradient(135deg, #c0392b, #27ae60);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 24px;
    }

    .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 12px;
        color: #1abc9c;
    }

    /* About Section */
    .about {
        padding: 70px 0;
        background-color: white;
    }

    .about-img {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .about-img img {
        width: 100%;
        border-radius: 8px;
        transition: transform 0.4s ease;
    }

    .about-img:hover img {
        transform: scale(1.03);
    }

    .about-content h2 {
        font-size: 2.2rem;
        font-weight: 700;
        color: #c0392b;
        margin-bottom: 15px;
        position: relative;
        display: inline-block;
    }

    .about-content h2::after {
        content: '';
        position: absolute;
        width: 45px;
        height: 3px;
        background: linear-gradient(to right, #c0392b, #27ae60);
        bottom: -8px;
        left: 0;
    }

    .about-content p {
        margin-bottom: 15px;
        color: #666;
        line-height: 1.6;
        font-size: 0.95rem;
    }

    .about-list {
        list-style: none;
        padding: 0;
        margin-bottom: 25px;
    }

    .about-list li {
        padding-left: 25px;
        position: relative;
        margin-bottom: 10px;
        color: #666;
        font-size: 0.95rem;
    }

    .about-list li::before {
        content: "\f00c";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        position: absolute;
        left: 0;
        top: 0;
        color: #27ae60;
    }

    /* CTA Section */
    .cta {
        padding: 60px 0;
        background: linear-gradient(135deg,
            rgba(192, 57, 43, 0.9) 0%,
            rgba(192, 57, 43, 0.8) 50%,
            rgba(39, 174, 96, 0.7) 100%),
            url('https://images.unsplash.com/photo-1493246507139-91e8fad9978e?auto=format&fit=crop&w=1470&q=80');
        background-size: cover;
        background-position: center;
        color: white;
        text-align: center;
    }

    .cta h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }

    .cta p {
        font-size: 1rem;
        margin-bottom: 20px;
        max-width: 650px;
        margin-left: auto;
        margin-right: auto;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Scroll Down Animation */
    .scroll-down {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: white;
        font-size: 1.2rem;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 10;
    }

    .scroll-down span {
        font-size: 0.75rem;
        margin-bottom: 3px;
    }

    .scroll-down i {
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-8px);
        }
        60% {
            transform: translateY(-4px);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero" id="home">
    <div class="hero-pattern"></div>
    <div class="hero-content">
        <h1 class="hero-title">Maroc Cultures</h1>
        <p class="hero-subtitle">Découvrez et célébrez la richesse du patrimoine marocain au cœur du Canada</p>
        <div class="hero-buttons">
            <a href="#features" class="btn-primary">
                <i class="fas fa-star"></i> Découvrir nos services
            </a>
            <a href="{% url 'home' %}" class="btn-secondary">
                <i class="fas fa-home"></i> Accéder au site
            </a>
        </div>
    </div>
    <a href="#features" class="scroll-down">
        <span>Découvrir</span>
        <i class="fas fa-chevron-down"></i>
    </a>
</section>

<!-- Features Section -->
<section class="features" id="features">
    <div class="container">
        <div class="section-title">
            <h2>Nos Services</h2>
            <p>Découvrez comment Maroc Cultures peut vous accompagner dans votre parcours culturel</p>
        </div>
        <div class="row g-3">
            <div class="col-md-4 mb-3">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-theater-masks"></i>
                    </div>
                    <h3 class="feature-title">Événements Culturels</h3>
                    <p>Organisation de festivals, expositions et concerts mettant en valeur la richesse culturelle marocaine au Canada.</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h3 class="feature-title">Soutien Communautaire</h3>
                    <p>Accompagnement et ressources pour faciliter l'intégration et l'épanouissement des Marocains au Canada.</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">Assistant Virtuel</h3>
                    <p>Notre chatbot intelligent répond à toutes vos questions sur la culture marocaine et nos services.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about" id="about">
    <div class="container">
        <div class="row align-items-center g-4">
            <div class="col-lg-5 mb-4 mb-lg-0">
                <div class="about-img">
                    <img src="{% static 'img/img_sv-768x659.png' %}" alt="Maroc Cultures">
                </div>
            </div>
            <div class="col-lg-7">
                <div class="about-content">
                    <h2>À Propos de Nous</h2>
                    <p>Maroc Cultures est une association culturelle et communautaire dédiée à la valorisation du patrimoine marocain et au soutien de la diaspora marocaine au Canada.</p>
                    <p>Fondée avec la vision de créer des ponts entre les cultures, notre association œuvre pour :</p>
                    <ul class="about-list">
                        <li>Promouvoir la culture, l'art et les traditions marocaines</li>
                        <li>Accompagner l'intégration et l'épanouissement des Marocains au Canada</li>
                        <li>Renforcer les liens entre le Maroc et sa diaspora</li>
                        <li>Favoriser le dialogue interculturel et la diversité</li>
                    </ul>
                    <a href="{% url 'about' %}" class="btn-primary">
                        <i class="fas fa-info-circle"></i> En savoir plus
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta">
    <div class="container">
        <h2>Besoin d'informations?</h2>
        <p>Notre assistant virtuel est là pour répondre à toutes vos questions sur la culture marocaine, nos événements et nos services.</p>
        <a href="javascript:void(0);" class="btn-primary" id="open-chat-btn">
            <i class="fas fa-comments"></i> Discuter avec notre assistant
        </a>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Open chat when the CTA button is clicked
    document.addEventListener('DOMContentLoaded', function() {
        const openChatBtn = document.getElementById('open-chat-btn');
        if (openChatBtn) {
            openChatBtn.addEventListener('click', function() {
                const chatToggleBtn = document.querySelector('.chat-toggle-btn');
                if (chatToggleBtn) {
                    chatToggleBtn.click();
                }
            });
        }
    });
</script>
{% endblock %}
