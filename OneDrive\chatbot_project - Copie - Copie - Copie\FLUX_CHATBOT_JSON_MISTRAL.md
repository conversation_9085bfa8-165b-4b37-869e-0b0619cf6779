# 🤖 Flux de Traitement Chatbot : JSON → Mistral AI

## 🎯 Principe de Fonctionnement

Le chatbot Maroc Cultures utilise une approche en cascade pour fournir les réponses les plus précises :

1. **🥇 PRIORITÉ** : Donn<PERSON> JSON (faq_data.json)
2. **🥈 FALLBACK** : Intelligence Artificielle Mistral
3. **🥉 SECOURS** : Réponses prédéfinies
4. **🔄 DÉFAUT** : Message d'accueil générique

---

## 📋 ÉTAPE 1 : Recherche dans les Données JSON

### **Fichier Source : `faq_data.json`**
```json
{
  "instruction": "Salutation générale",
  "input": "bonjour salut hello hi bonsoir",
  "output": "Bonjour ! Je suis l'assistant virtuel de Maroc Cultures..."
}
```

### **Algorithme de Correspondance Amélioré**

#### **Phase 1 : Correspondance Exacte**
```python
# Recherche directe des mots-clés
input_keywords = ["bonjour", "salut", "hello", "hi", "bonsoir"]
user_message = "bonjour"

for keyword in input_keywords:
    if keyword in user_message.lower():
        exact_match = True  # ✅ TROUVÉ !
```

#### **Phase 2 : Correspondance Partielle (si Phase 1 échoue)**
```python
# Intersection de mots avec score pondéré
input_score = len(input_common) * 2    # Poids double pour input
instruction_score = len(instruction_common)
total_score = input_score + instruction_score
```

### **Exemples de Correspondances JSON**

| Message Utilisateur | Mot-clé Trouvé | Réponse JSON |
|---------------------|----------------|--------------|
| "bonjour" | ✅ `bonjour` | Salutation générale |
| "événements" | ✅ `événements` | Liste des événements culturels |
| "contact" | ✅ `contact` | Informations de contact |
| "mawazine" | ✅ `mawazine` | Détails du Festival Mawazine |
| "prix" | ✅ `prix` | Informations tarifaires |

---

## 🤖 ÉTAPE 2 : Fallback vers Mistral AI

### **Conditions d'Activation**
```python
if not bot_response:  # Aucune réponse JSON trouvée
    print("🤖 Utilisation de Mistral AI...")
    mistral_response = get_mistral_response_with_context(user_message, user_id)
```

### **Intégration Mistral AI**
```python
# Fichier: mistral_api.py
def get_mistral_response_with_context(user_message, user_id=None):
    # Contexte spécialisé Maroc Cultures
    context = """Tu es l'assistant virtuel de Maroc Cultures, 
    une association culturelle marocaine au Canada..."""
    
    # Appel API Mistral
    response = requests.post(
        "https://api.mistral.ai/v1/chat/completions",
        headers={"Authorization": f"Bearer {API_KEY}"},
        json={
            "model": "mistral-large-latest",
            "messages": [
                {"role": "system", "content": context},
                {"role": "user", "content": user_message}
            ]
        }
    )
```

### **Exemples de Questions pour Mistral AI**

| Message Utilisateur | Traitement |
|---------------------|------------|
| "Parle-moi de l'histoire du Maroc" | ❌ Pas dans JSON → ✅ Mistral AI |
| "Quelles sont les spécialités culinaires de Fès ?" | ❌ Pas dans JSON → ✅ Mistral AI |
| "Comment s'inscrire à un atelier de calligraphie ?" | ❌ Pas dans JSON → ✅ Mistral AI |

---

## 📝 ÉTAPE 3 : Réponses Prédéfinies (Secours)

### **Si Mistral AI Indisponible**
```python
predefined_responses = {
    "date": "📅 Pour connaître les dates...",
    "lieu": "📍 Nos événements se déroulent...",
    "prix": "💰 Les prix varient selon...",
    "tajine": "🍲 Le tajine est un plat emblématique...",
    "couscous": "🥘 Le couscous est le plat national...",
    "marrakech": "🏛️ Marrakech, la 'Perle du Sud'...",
}
```

### **Activation**
```python
except ImportError:  # Module 'requests' non installé
    print("⚠️ Utilisation de réponses prédéfinies...")
    for keyword, response in predefined_responses.items():
        if keyword in user_message.lower():
            bot_response = response
            break
```

---

## 🔄 ÉTAPE 4 : Réponse par Défaut

### **Dernier Recours**
```python
if not bot_response:
    bot_response = """🇲🇦 Bonjour ! Je suis votre assistant culturel 
    Maroc Cultures. Je peux vous parler de l'histoire, la gastronomie, 
    l'artisanat, les villes et traditions du Maroc. 
    Que souhaitez-vous découvrir ?"""
```

---

## 📊 Statistiques de Performance

### **Répartition des Réponses (Estimation)**
- 📋 **JSON** : ~70% des questions courantes
- 🤖 **Mistral AI** : ~25% des questions complexes
- 📝 **Prédéfinies** : ~4% (fallback technique)
- 🔄 **Défaut** : ~1% (cas d'erreur)

### **Temps de Réponse Moyen**
- 📋 **JSON** : < 100ms (instantané)
- 🤖 **Mistral AI** : 1-3 secondes (API externe)
- 📝 **Prédéfinies** : < 50ms (local)
- 🔄 **Défaut** : < 10ms (immédiat)

---

## 🎯 Avantages de cette Approche

### **✅ Précision Maximale**
- Les réponses JSON sont spécifiquement adaptées à Maroc Cultures
- Informations exactes (contacts, horaires, événements)

### **✅ Flexibilité IA**
- Mistral AI gère les questions non prévues
- Réponses contextualisées et naturelles

### **✅ Robustesse**
- Système de fallback en cascade
- Aucune situation sans réponse

### **✅ Performance**
- Réponses JSON ultra-rapides
- Optimisation des coûts API

---

## 🔧 Configuration et Maintenance

### **Enrichissement des Données JSON**
```json
// Ajouter de nouvelles questions/réponses
{
  "instruction": "Nouvelle question",
  "input": "mots-clés séparés espaces",
  "output": "Réponse précise et détaillée"
}
```

### **Monitoring des Performances**
```python
# Logs de debug pour analyser l'utilisation
print(f"✅ Réponse trouvée dans le JSON (score: {highest_score})")
print(f"🤖 Utilisation de Mistral AI...")
print(f"📝 Réponse prédéfinie trouvée pour '{keyword}'")
```

### **Optimisation Continue**
1. **Analyser les logs** pour identifier les questions fréquentes non couvertes
2. **Enrichir le JSON** avec de nouvelles entrées
3. **Ajuster les mots-clés** pour améliorer la correspondance
4. **Monitorer les coûts** Mistral AI

---

## 🚀 Exemple Complet de Traitement

### **Scénario : Utilisateur tape "bonjour"**

```
🔍 Message: 'bonjour', recherche dans JSON...
🎯 Correspondance exacte trouvée: 'bonjour' dans 'Salutation générale'
✅ Réponse trouvée dans le JSON (score: 7)
📄 Instruction: Salutation générale
📄 Réponse: Bonjour ! Je suis l'assistant virtuel de Maroc Cultures...
```

### **Scénario : Utilisateur tape "histoire du Maroc"**

```
🔍 Message: 'histoire du Maroc', recherche dans JSON...
⚠️ Aucune correspondance trouvée dans le JSON
🤖 Aucune réponse JSON trouvée, utilisation de Mistral AI...
👤 User ID: None
📤 Réponse Mistral reçue: L'histoire du Maroc est riche et fascinante...
✅ Mistral AI a répondu avec succès !
```

---

Cette architecture garantit que le chatbot utilise **toujours en priorité les données JSON précises** avant de faire appel à l'intelligence artificielle Mistral pour les questions plus complexes ou non prévues. 🎯
