# Generated by Django 5.2.1 on 2025-05-22 13:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot_app', '0006_remove_user_img'),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Titre')),
                ('description', models.TextField(verbose_name='Description')),
                ('date_start', models.DateField(verbose_name='Date de début')),
                ('date_end', models.DateField(blank=True, null=True, verbose_name='Date de fin')),
                ('time', models.TimeField(blank=True, null=True, verbose_name='Heure')),
                ('location', models.CharField(max_length=200, verbose_name='Lieu')),
                ('image_url', models.URL<PERSON>ield(blank=True, null=True, verbose_name="URL de l'image")),
                ('registration_url', models.URLField(blank=True, null=True, verbose_name="URL d'inscription")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['date_start'],
            },
        ),
    ]
