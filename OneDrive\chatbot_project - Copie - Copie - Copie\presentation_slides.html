<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Présentation Maroc Cultures - PFE</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #C0392B 0%, #E74C3C 50%, #27AE60 100%);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 90%;
            max-width: 1200px;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .slide.active {
            display: block;
            animation: slideIn 0.5s ease-in-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        h2 {
            font-size: 2.5em;
            margin-bottom: 30px;
            color: #F1C40F;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        h3 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #1ABC9C;
        }

        p, li {
            font-size: 1.3em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        ul {
            text-align: left;
            max-width: 800px;
            margin: 0 auto;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 18px;
        }

        .logo {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Titre -->
        <div class="slide active">
            <div class="logo">🇲🇦</div>
            <h1>Maroc Cultures</h1>
            <h3>Portail Culturel avec Chatbot Intelligent</h3>
            <p style="font-size: 1.5em; margin-top: 40px;">Projet de Fin d'Études (PFE)</p>
            <p style="font-size: 1.2em; margin-top: 20px;">Développement Web & Intelligence Artificielle</p>
        </div>

        <!-- Slide 2: Contexte -->
        <div class="slide">
            <h2>🎯 Contexte du Projet</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🎓 Académique</h3>
                    <p>Projet de Fin d'Études en développement web</p>
                </div>
                <div class="feature-card">
                    <h3>🌍 Social</h3>
                    <p>Soutien à la diaspora marocaine au Canada</p>
                </div>
                <div class="feature-card">
                    <h3>🎨 Culturel</h3>
                    <p>Valorisation du patrimoine marocain</p>
                </div>
                <div class="feature-card">
                    <h3>🤖 Technologique</h3>
                    <p>Intégration d'intelligence artificielle</p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Objectifs -->
        <div class="slide">
            <h2>🎯 Objectifs du Portail</h2>
            <ul>
                <li>🤝 <strong>Rassembler</strong> la communauté marocaine au Canada</li>
                <li>🎨 <strong>Promouvoir</strong> la culture et les traditions marocaines</li>
                <li>📅 <strong>Organiser</strong> et diffuser les événements culturels</li>
                <li>💬 <strong>Faciliter</strong> l'intégration via un chatbot intelligent</li>
                <li>🌍 <strong>Créer</strong> des ponts entre les cultures</li>
                <li>📚 <strong>Transmettre</strong> le patrimoine aux nouvelles générations</li>
            </ul>
        </div>

        <!-- Slide 4: Technologies -->
        <div class="slide">
            <h2>🔧 Stack Technologique</h2>
            <div class="tech-stack">
                <div class="tech-item">Django</div>
                <div class="tech-item">Python</div>
                <div class="tech-item">HTML5/CSS3</div>
                <div class="tech-item">JavaScript</div>
                <div class="tech-item">Bootstrap</div>
                <div class="tech-item">Supabase</div>
                <div class="tech-item">API Mistral</div>
                <div class="tech-item">SVG</div>
            </div>
            <p style="margin-top: 40px;">Architecture moderne et scalable pour une expérience utilisateur optimale</p>
        </div>

        <!-- Slide 5: Fonctionnalités -->
        <div class="slide">
            <h2>⚡ Fonctionnalités Principales</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🏠 Portail d'Accueil</h3>
                    <p>Design responsive avec couleurs marocaines</p>
                </div>
                <div class="feature-card">
                    <h3>👥 Gestion Utilisateurs</h3>
                    <p>Inscription, connexion, profils personnalisés</p>
                </div>
                <div class="feature-card">
                    <h3>📅 Événements</h3>
                    <p>Calendrier culturel avec images et détails</p>
                </div>
                <div class="feature-card">
                    <h3>🤖 Chatbot IA</h3>
                    <p>Assistant intelligent avec API Mistral</p>
                </div>
                <div class="feature-card">
                    <h3>⚙️ Administration</h3>
                    <p>Interface de gestion complète</p>
                </div>
                <div class="feature-card">
                    <h3>📱 Responsive</h3>
                    <p>Compatible tous appareils</p>
                </div>
            </div>
        </div>

        <!-- Slide 6: Design -->
        <div class="slide">
            <h2>🎨 Design et Ergonomie</h2>
            <div style="display: flex; justify-content: space-around; align-items: center; margin: 40px 0;">
                <div>
                    <h3>Charte Graphique</h3>
                    <ul style="text-align: left;">
                        <li>🔴 Rouge (#C0392B)</li>
                        <li>🟢 Vert (#27AE60)</li>
                        <li>🔵 Turquoise (#1ABC9C)</li>
                        <li>✨ Motifs marocains</li>
                    </ul>
                </div>
                <div>
                    <h3>Expérience Utilisateur</h3>
                    <ul style="text-align: left;">
                        <li>📱 Design responsive</li>
                        <li>⚡ Chargement rapide</li>
                        <li>🎯 Navigation intuitive</li>
                        <li>♿ Accessibilité</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 7: Impact -->
        <div class="slide">
            <h2>📈 Impact et Bénéfices</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin: 40px 0;">
                <div>
                    <h3>👥 Pour la Communauté</h3>
                    <ul>
                        <li>Centralisation des informations</li>
                        <li>Connexion entre membres</li>
                        <li>Transmission culturelle</li>
                        <li>Support à l'intégration</li>
                    </ul>
                </div>
                <div>
                    <h3>🏢 Pour l'Association</h3>
                    <ul>
                        <li>Visibilité numérique</li>
                        <li>Gestion efficace</li>
                        <li>Attraction de nouveaux membres</li>
                        <li>Réduction des coûts</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 8: Démonstration -->
        <div class="slide">
            <h2>🖥️ Démonstration</h2>
            <p style="font-size: 1.5em; margin: 40px 0;">Présentation en direct du portail</p>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🏠 Page d'Accueil</h3>
                    <p>Navigation et design</p>
                </div>
                <div class="feature-card">
                    <h3>📅 Événements</h3>
                    <p>Liste et détails avec images</p>
                </div>
                <div class="feature-card">
                    <h3>🤖 Chatbot</h3>
                    <p>Interaction en temps réel</p>
                </div>
                <div class="feature-card">
                    <h3>⚙️ Administration</h3>
                    <p>Gestion du contenu</p>
                </div>
            </div>
        </div>

        <!-- Slide 9: Conclusion -->
        <div class="slide">
            <h2>🎓 Conclusion</h2>
            <p style="font-size: 1.4em; margin: 30px 0;">
                Le portail <strong>Maroc Cultures</strong> démontre la maîtrise de :
            </p>
            <ul style="font-size: 1.2em;">
                <li>🔧 <strong>Technologies modernes</strong> (Django, IA, Responsive)</li>
                <li>🎯 <strong>Analyse des besoins</strong> communautaires</li>
                <li>🎨 <strong>Design centré utilisateur</strong></li>
                <li>⚡ <strong>Développement full-stack</strong></li>
                <li>🤖 <strong>Intégration d'IA</strong> conversationnelle</li>
            </ul>
            <p style="font-size: 1.3em; margin-top: 40px; color: #F1C40F;">
                <strong>Une solution complète au service de la culture marocaine</strong>
            </p>
        </div>

        <!-- Slide 10: Questions -->
        <div class="slide">
            <h1>❓</h1>
            <h2>Questions & Discussion</h2>
            <p style="font-size: 1.5em; margin: 40px 0;">
                Merci pour votre attention !
            </p>
            <div style="margin-top: 60px;">
                <p style="font-size: 1.2em;">🇲🇦 <strong>Maroc Cultures</strong></p>
                <p>Portail Culturel avec Chatbot Intelligent</p>
                <p style="margin-top: 20px; font-size: 1.1em;">Projet de Fin d'Études</p>
            </div>
        </div>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">10</span>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Précédent</button>
        <button class="nav-btn" onclick="nextSlide()">Suivant →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Navigation au clavier
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });
    </script>
</body>
</html>
