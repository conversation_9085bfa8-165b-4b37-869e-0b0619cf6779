#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def fix_json_file():
    """Fix the malformed JSON file"""

    # Read the file
    with open('chatbot_app/faq_data.json', 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract all JSON objects from the file
    all_objects = []

    # Find all lines that look like JSON objects
    lines = content.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith('{"instruction"'):
            try:
                # Try to parse as JSON
                obj = json.loads(line)
                all_objects.append(obj)
                print(f"Added object: {obj['instruction'][:50]}...")
            except json.JSONDecodeError as e:
                print(f"Skipping malformed line: {line[:100]}...")

    # Also try to extract from the properly formatted part
    # Look for the array start
    array_start = content.find('[')
    if array_start != -1:
        # Find where the malformed part starts (after the first ']')
        first_close = content.find(']', array_start)
        if first_close != -1:
            valid_part = content[array_start:first_close+1]
            try:
                # Try to parse the valid part
                valid_objects = json.loads(valid_part)
                print(f"Found {len(valid_objects)} valid objects from array part")
                # Add them to the beginning
                all_objects = valid_objects + all_objects
            except json.JSONDecodeError:
                print("Could not parse the array part")

    # Remove duplicates based on instruction
    seen_instructions = set()
    unique_objects = []

    for obj in all_objects:
        if obj.get('instruction') not in seen_instructions:
            unique_objects.append(obj)
            seen_instructions.add(obj.get('instruction'))

    # Write the fixed JSON file
    with open('chatbot_app/faq_data.json', 'w', encoding='utf-8') as f:
        json.dump(unique_objects, f, ensure_ascii=False, indent=2)

    print(f"Fixed JSON file with {len(unique_objects)} unique objects")
    return len(unique_objects)

if __name__ == "__main__":
    fix_json_file()
