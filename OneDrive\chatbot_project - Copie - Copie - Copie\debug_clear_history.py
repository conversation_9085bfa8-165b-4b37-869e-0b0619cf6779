#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import sys

def debug_clear_history():
    """Debug la fonction de suppression d'historique"""
    
    print("🔍 Diagnostic de la suppression d'historique...")
    
    try:
        # Connexion à la base de données
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier les tables existantes
        print("\n📋 Tables existantes:")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        for table in tables:
            print(f"   - {table[0]}")
        
        # 2. Vérifier la structure des tables importantes
        important_tables = [
            'chatbot_app_conversation',
            'chatbot_app_message', 
            'chatbot_app_historique'
        ]
        
        for table_name in important_tables:
            print(f"\n🏗️ Structure de {table_name}:")
            try:
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"   - {col[0]} ({col[1]})")
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        # 3. Compter les données existantes
        print(f"\n📊 Données existantes:")
        
        for table_name in important_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   - {table_name}: {count} enregistrements")
            except Exception as e:
                print(f"   ❌ Erreur pour {table_name}: {e}")
        
        # 4. Vérifier les données d'historique spécifiques
        print(f"\n🔍 Détails de l'historique:")
        try:
            cursor.execute("""
                SELECT h.id, h.session_id, h.id_utilisateur_id, h.date_acces, c.title 
                FROM chatbot_app_historique h 
                LEFT JOIN chatbot_app_conversation c ON h.id_conversation_id = c.id 
                LIMIT 5
            """)
            historiques = cursor.fetchall()
            
            if historiques:
                print("   Derniers historiques:")
                for hist in historiques:
                    print(f"   - ID: {hist[0]}, Session: {hist[1]}, User: {hist[2]}, Date: {hist[3]}, Titre: {hist[4]}")
            else:
                print("   Aucun historique trouvé")
                
        except Exception as e:
            print(f"   ❌ Erreur lecture historique: {e}")
        
        # 5. Vérifier les conversations
        print(f"\n💬 Détails des conversations:")
        try:
            cursor.execute("""
                SELECT id, title, auth_user_id, session_id, created_at 
                FROM chatbot_app_conversation 
                LIMIT 5
            """)
            conversations = cursor.fetchall()
            
            if conversations:
                print("   Dernières conversations:")
                for conv in conversations:
                    print(f"   - ID: {conv[0]}, Titre: {conv[1]}, User: {conv[2]}, Session: {conv[3]}, Date: {conv[4]}")
            else:
                print("   Aucune conversation trouvée")
                
        except Exception as e:
            print(f"   ❌ Erreur lecture conversations: {e}")
        
        # 6. Vérifier les messages
        print(f"\n📝 Détails des messages:")
        try:
            cursor.execute("""
                SELECT id, content, role, conversation_id, is_user, timestamp 
                FROM chatbot_app_message 
                LIMIT 5
            """)
            messages = cursor.fetchall()
            
            if messages:
                print("   Derniers messages:")
                for msg in messages:
                    content_preview = msg[1][:50] + "..." if len(msg[1]) > 50 else msg[1]
                    print(f"   - ID: {msg[0]}, Contenu: {content_preview}, Rôle: {msg[2]}, Conv: {msg[3]}, User: {msg[4]}, Date: {msg[5]}")
            else:
                print("   Aucun message trouvé")
                
        except Exception as e:
            print(f"   ❌ Erreur lecture messages: {e}")
        
        # 7. Test de suppression simulée (sans commit)
        print(f"\n🧪 Test de suppression simulée:")
        
        try:
            # Simuler la suppression pour un utilisateur fictif
            test_user_id = 999999  # ID qui n'existe probablement pas
            
            print(f"   Test pour user_id = {test_user_id}")
            
            # Compter avant
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_historique WHERE id_utilisateur_id = %s", (test_user_id,))
            hist_before = cursor.fetchone()[0]
            print(f"   - Historiques avant: {hist_before}")
            
            cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation WHERE auth_user_id = %s", (test_user_id,))
            conv_before = cursor.fetchone()[0]
            print(f"   - Conversations avant: {conv_before}")
            
            # Test des requêtes de suppression (sans exécuter)
            print(f"   - Requêtes SQL qui seraient exécutées:")
            print(f"     DELETE FROM chatbot_app_historique WHERE id_utilisateur_id = {test_user_id}")
            print(f"     SELECT id FROM chatbot_app_conversation WHERE auth_user_id = {test_user_id}")
            print(f"     DELETE FROM chatbot_app_message WHERE conversation_id IN (...)")
            print(f"     DELETE FROM chatbot_app_conversation WHERE auth_user_id = {test_user_id}")
            
        except Exception as e:
            print(f"   ❌ Erreur test suppression: {e}")
        
        cursor.close()
        conn.close()
        print(f"\n✅ Diagnostic terminé")
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False
    
    return True

if __name__ == "__main__":
    debug_clear_history()
