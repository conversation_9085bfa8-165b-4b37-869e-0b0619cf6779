from django.core.management.base import BaseCommand
from chatbot_app.models import Event
from datetime import datetime

class Command(BaseCommand):
    help = 'Ajouter les événements culturels marocains'

    def handle(self, *args, **options):
        self.stdout.write("🎭 Ajout des événements culturels marocains")
        self.stdout.write("=" * 60)
        
        # Les 3 événements à ajouter
        events_data = [
            {
                'title': 'Festival Marocain de Montréal 2024',
                'description': 'Le plus grand festival culturel marocain au Canada. Découvrez la richesse de la culture marocaine à travers la musique, la danse, l\'artisanat et la gastronomie traditionnelle. Un événement familial avec des spectacles de groupes folkloriques, des ateliers de henné, et des dégustations de thé à la menthe.',
                'date_start': '2024-06-07',
                'date_end': '2024-06-09',
                'time_start': '10:00:00',
                'time_end': '22:00:00',
                'location': '<PERSON><PERSON>, Montréal',
                'price': 0.00,
                'max_participants': 5000,
                'category': 'Festival',
                'organizer': 'Association Culturelle Marocaine de Montréal'
            },
            {
                'title': 'Exposition d\'Art Contemporain Marocain',
                'description': 'Une exposition unique présentant les œuvres d\'artistes marocains contemporains. Peintures, sculptures et installations qui reflètent l\'évolution de l\'art marocain moderne tout en préservant les traditions ancestrales.',
                'date_start': '2024-05-28',
                'date_end': '2024-06-27',
                'time_start': '09:00:00',
                'time_end': '18:00:00',
                'location': 'Galerie d\'Art Contemporain, Centre-ville',
                'price': 15.00,
                'max_participants': 200,
                'category': 'Exposition',
                'organizer': 'Galerie Maghreb Arts'
            },
            {
                'title': 'Concert de Musique Gnawa',
                'description': 'Soirée exceptionnelle de musique Gnawa avec des maîtres musiciens venus directement d\'Essaouira. La musique Gnawa, patrimoine spirituel et musical du Maroc, vous transportera dans un voyage mystique unique.',
                'date_start': '2024-05-31',
                'date_end': '2024-05-31',
                'time_start': '20:00:00',
                'time_end': '23:00:00',
                'location': 'Théâtre Corona, Montréal',
                'price': 45.00,
                'max_participants': 800,
                'category': 'Concert',
                'organizer': 'Productions Culturelles Maghreb'
            }
        ]
        
        created_count = 0
        
        for event_data in events_data:
            # Supprimer l'événement s'il existe déjà
            Event.objects.filter(title=event_data['title']).delete()
            
            try:
                event = Event.objects.create(**event_data)
                self.stdout.write(f"✅ Événement créé: {event.title}")
                self.stdout.write(f"   📅 Date: {event.date_start} - {event.date_end}")
                self.stdout.write(f"   🕐 Heure: {event.time_start} - {event.time_end}")
                self.stdout.write(f"   📍 Lieu: {event.location}")
                self.stdout.write(f"   💰 Prix: {event.price}€")
                self.stdout.write(f"   👥 Participants max: {event.max_participants}")
                self.stdout.write(f"   🏷️ Catégorie: {event.category}")
                self.stdout.write(f"   👤 Organisateur: {event.organizer}")
                self.stdout.write("-" * 60)
                created_count += 1
            except Exception as e:
                self.stdout.write(f"❌ Erreur lors de la création de '{event_data['title']}': {e}")
        
        total_events = Event.objects.count()
        self.stdout.write(f"\n🎉 {created_count} événements créés avec succès!")
        self.stdout.write(f"📊 Total d'événements dans la base: {total_events}")
        
        # Afficher tous les événements
        all_events = Event.objects.all().order_by('date_start')
        if all_events:
            self.stdout.write(f"\n📋 Liste de tous les événements:")
            for event in all_events:
                self.stdout.write(f"   ID: {event.id} | {event.title} | {event.date_start} | {event.location} | {event.price}€")
