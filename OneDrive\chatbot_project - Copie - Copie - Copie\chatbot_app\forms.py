from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User as AuthUser
from .models import User, Event

class UserForm(forms.ModelForm):
    """Formulaire pour gérer les utilisateurs avec le nouveau modèle User"""
    password = forms.CharField(
        widget=forms.PasswordInput(),
        required=False,
        help_text="Laissez vide pour conserver le mot de passe actuel."
    )

    privilege = forms.ChoiceField(
        choices=[('user', 'Utilisateur'), ('admin', 'Administrateur')],
        initial='user',
        required=True
    )

    class Meta:
        model = User
        fields = ['username', 'email', 'mdp', 'privilege']
        widgets = {
            'mdp': forms.PasswordInput(),
        }
        labels = {
            'mdp': 'Mot de passe',
        }

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Ce nom d'utilisateur est déjà utilisé.")
        return username

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("Cette adresse email est déjà utilisée.")
        return email

    def save(self, commit=True):
        user = super().save(commit=False)

        # Si un nouveau mot de passe est fourni, l'utiliser
        password = self.cleaned_data.get('password')
        if password:
            user.mdp = password  # Dans un cas réel, il faudrait hasher le mot de passe

        if commit:
            user.save()

        return user

class RegisterForm(UserCreationForm):
    """Formulaire d'inscription personnalisé basé sur UserCreationForm de Django"""
    email = forms.EmailField(
        max_length=254,
        required=True,
        help_text="Obligatoire. Entrez une adresse email valide."
    )

    class Meta:
        model = AuthUser
        fields = ('username', 'email', 'password1', 'password2')

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if AuthUser.objects.filter(email=email).exists():
            raise forms.ValidationError("Cette adresse email est déjà utilisée.")
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("Cette adresse email est déjà utilisée.")
        return email

class EventForm(forms.ModelForm):
    """Formulaire pour gérer les événements"""
    date_start = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        label="Date de début",
        required=True
    )
    date_end = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        label="Date de fin",
        required=False
    )
    time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time'}),
        label="Heure",
        required=False
    )

    class Meta:
        model = Event
        fields = ['title', 'description', 'date_start', 'date_end', 'time', 'location', 'image_url', 'registration_url']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
        }
        labels = {
            'title': 'Titre',
            'description': 'Description',
            'location': 'Lieu',
            'image_url': "URL de l'image",
            'registration_url': "URL d'inscription"
        }
