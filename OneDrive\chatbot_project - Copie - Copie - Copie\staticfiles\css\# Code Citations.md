# Code Citations

## License: unknown
https://github.com/NumericFactory/velibeo/tree/80c883e9226616b96991bee4dcb74d55fa41c4c4/index.html

```
-->
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <
```


## License: unknown
https://github.com/ervspogi20/myspamassage/tree/9b6aeb8d25200cee360f4e20ed7b780ea84ab3de/myspamassage/templates/booknow.html

```
/a>
            <nav>
                <ul class="nav-links">
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li><a href="{% url '
```


## License: unknown
https://github.com/jjlabrador/ETSII-git/tree/f4ee55cbce90fa3c598f803797400d3a6c459957/DSI/Practicas/Practica7_Django/static/templates/_footer.html

```
a href="{% url 'about' %}">About</a></li>
                    <li><a href="{% url 'contact' %}">Contact</a></li>
                    <li><a
```


## License: unknown
https://github.com/endthestart/senex/tree/e058614e10c5673caac87309fc3b13554f99d439/senex/templates/base.html

```
li>
                    <li><a href="{% url 'about' %}">About</a></li>
                    <li><a href="{% url 'contact' %}">Contact</a></li
```


## License: unknown
https://github.com/FreeSauce/locations/tree/0adb847b4ea72633034436cb0f666d3ad7bcac34/freeartmovement/templates/open-pages/base_no_auth.html

```
' %}">About</a></li>
                    <li><a href="{% url 'contact' %}">Contact</a></li>
                    <li><a href="{% url 'login'
```

 