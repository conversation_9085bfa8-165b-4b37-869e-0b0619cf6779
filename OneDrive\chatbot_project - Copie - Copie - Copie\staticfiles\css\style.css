/* Styles pour la navbar améliorée */
.navbar {
    transition: all 0.3s ease;
    padding: 1.2rem 0;
}

.navbar-shrink {
    padding: 0.5rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: rgba(44, 62, 80, 0.95) !important;
    backdrop-filter: blur(10px);
}

.nav-pro {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-links a {
    position: relative;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #1abc9c;
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }
    
    .nav-pro {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #2c3e50;
        flex-direction: column;
        padding: 1rem 0;
        clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
        transition: clip-path 0.4s ease;
    }
    
    .nav-pro.active {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
    
    .nav-links {
        flex-direction: column;
        width: 100%;
        text-align: center;
    }
}

/* Styles améliorés pour le chatbot */
.chat-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-title {
    background: linear-gradient(135deg, #1877f2, #3b5998);
    color: white;
    padding: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-title::before {
    content: '\f4ad';  /* Code FontAwesome pour une bulle de chat */
    font-family: 'Font Awesome 5 Free';
    margin-right: 10px;
    font-size: 1.2rem;
}

#chat-box {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: #f8f9fa;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23e0e0e0' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.message {
    margin-bottom: 1.2rem;
    max-width: 80%;
    position: relative;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.bot {
    align-self: flex-start;
    background-color: #f0f0f0;
    color: #333;
    border-radius: 18px 18px 18px 4px;
    padding: 12px 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.user {
    align-self: flex-end;
    background-color: #dcf8c6;
    color: #333;
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
    margin-left: auto;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.bot::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 20px;
    height: 20px;
    background-color: #f0f0f0;
    border-bottom-right-radius: 15px;
    z-index: -1;
}

.message.user::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: #dcf8c6;
    border-bottom-left-radius: 15px;
    z-index: -1;
}

#chat-form {
    display: flex;
    padding: 1rem;
    background-color: white;
    border-top: 1px solid #eaeaea;
}

#user-input {
    flex: 1;
    padding: 0.8rem 1.2rem;
    border: 1px solid #ddd;
    border-radius: 24px;
    margin-right: 0.8rem;
    font-size: 0.95rem;
    transition: border-color 0.3s;
}

#user-input:focus {
    outline: none;
    border-color: #1877f2;
    box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.2);
}

button.btn {
    background: #1877f2;
    color: white;
    border: none;
    border-radius: 24px;
    padding: 0.8rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

button.btn:hover {
    background: #166fe5;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button.btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Styles pour le formulaire d'inscription */
.form-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-weight: 700;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #1877f2;
    box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.2);
}

.form-btn {
    display: block;
    width: 100%;
    padding: 1rem;
    background-color: #1877f2;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 1rem;
}

.form-btn:hover {
    background-color: #166fe5;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-footer {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.9rem;
    color: #666;
}

.form-footer a {
    color: #1877f2;
    text-decoration: none;
    font-weight: 500;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Styles généraux améliorés */
body {
    font-family: 'Poppins', 'Segoe UI', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

a {
    color: #1877f2;
    text-decoration: none;
    transition: color 0.3s;
}

a:hover {
    color: #166fe5;
}

/* Footer amélioré */
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0;
    margin-top: 3rem;
}

.footer h4 {
    color: white;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s;
}

.footer-links a:hover {
    color: white;
}

.copyright {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
    color: rgba(255, 255, 255, 0.7);
}