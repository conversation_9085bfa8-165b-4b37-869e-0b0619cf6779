import requests
import json
import os
from django.conf import settings

def get_mistral_response(user_message, conversation_history=None):
    """
    Fonction pour obtenir une réponse de l'API Mistral AI
    Optimisée pour la culture marocaine
    """
    try:
        # Configuration de l'API Mistral depuis les variables d'environnement
        api_key = os.getenv('MISTRAL_API_KEY', 'TyWSqM7VMMzjBygUrTeNS0SuZicudsD2')
        model = os.getenv('MISTRAL_MODEL', 'mistral-small-latest')
        url = "https://api.mistral.ai/v1/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # Prompt système spécialisé pour Maroc Cultures
        system_prompt = """Tu es un assistant culturel expert du Maroc, représentant l'organisation "Maroc Cultures".

🇲🇦 TON RÔLE :
- Expert en culture, histoire, traditions, gastronomie, et patrimoine marocain
- Guide touristique virtuel pour le Maroc
- Ambassadeur de la richesse culturelle marocaine

📋 TES SPÉCIALITÉS :
- His<PERSON> du Maroc (dynasties, événements marquants)
- Villes impériales (Fès, Marrakech, Meknès, Rabat)
- Gastronomie (tajine, couscous, pâtisseries)
- Artisanat (tapis, poterie, bijoux)
- Musique et danse (chaâbi, gnawa, ahidous)
- Festivals et événements culturels
- Langues (arabe, amazigh, français)
- Architecture (mosquées, riads, kasbahs)

🎯 STYLE DE RÉPONSE :
- Chaleureux et accueillant
- Informatif mais accessible
- Utilise des émojis appropriés
- Encourage la découverte du Maroc
- Réponds en français principalement

⚠️ LIMITES :
- Ne réponds QUE aux questions sur le Maroc et sa culture
- Si la question n'est pas liée au Maroc, redirige poliment vers la culture marocaine
- Reste factuel et respectueux des traditions"""

        # Construire l'historique de conversation
        messages = [{"role": "system", "content": system_prompt}]

        # Ajouter l'historique récent si disponible (max 5 derniers échanges)
        if conversation_history:
            for msg in conversation_history[-10:]:  # Derniers 10 messages
                if msg.get('role') and msg.get('content'):
                    messages.append({
                        "role": msg['role'],
                        "content": msg['content']
                    })

        # Ajouter le message actuel
        messages.append({"role": "user", "content": user_message})

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": 300,
            "temperature": 0.7,
            "top_p": 0.9,
            "stream": False
        }

        response = requests.post(url, headers=headers, json=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            bot_response = result['choices'][0]['message']['content'].strip()

            # Ajouter une signature Maroc Cultures
            if len(bot_response) > 50:  # Seulement pour les réponses substantielles
                bot_response += "\n\n🇲🇦 *Maroc Cultures - Votre guide culturel du Maroc*"

            return bot_response

        elif response.status_code == 401:
            return "🔑 Erreur d'authentification API. Veuillez vérifier la clé Mistral."
        elif response.status_code == 429:
            return "⏳ Trop de requêtes. Veuillez patienter un moment avant de réessayer."
        else:
            return f"❌ Erreur API ({response.status_code}). Veuillez réessayer plus tard."

    except requests.exceptions.Timeout:
        return "⏰ Délai d'attente dépassé. Veuillez réessayer."
    except requests.exceptions.ConnectionError:
        return "🌐 Problème de connexion. Vérifiez votre connexion internet."
    except Exception as e:
        print(f"Erreur API Mistral: {e}")
        return "🔧 Une erreur technique s'est produite. Notre équipe technique a été notifiée."

def test_mistral_connection():
    """
    Teste la connexion à l'API Mistral
    """
    try:
        test_message = "Bonjour, peux-tu me parler brièvement du Maroc ?"
        response = get_mistral_response(test_message)

        if "Erreur" in response or "erreur" in response:
            return False, response
        else:
            return True, "✅ Connexion Mistral AI réussie !"

    except Exception as e:
        return False, f"❌ Échec du test : {e}"

def get_cultural_suggestion(topic="général"):
    """
    Obtient une suggestion culturelle sur un sujet spécifique
    """
    suggestions = {
        "gastronomie": "Parle-moi d'un plat traditionnel marocain et sa préparation",
        "musique": "Présente-moi un style musical traditionnel du Maroc",
        "artisanat": "Décris un artisanat traditionnel marocain",
        "histoire": "Raconte-moi un événement marquant de l'histoire du Maroc",
        "villes": "Présente-moi une ville marocaine et ses spécificités",
        "festivals": "Parle-moi d'un festival ou événement culturel marocain",
        "général": "Donne-moi une information culturelle intéressante sur le Maroc"
    }

    prompt = suggestions.get(topic, suggestions["général"])
    return get_mistral_response(prompt)

def get_mistral_response_with_context(user_message, user_id=None):
    """
    Version avec contexte utilisateur pour l'intégration Django
    """
    # Récupérer l'historique de conversation si user_id fourni
    conversation_history = []

    if user_id:
        try:
            from .models import Message
            # Récupérer les 5 derniers messages de l'utilisateur
            recent_messages = Message.objects.filter(
                conversation__user_id=user_id
            ).order_by('-timestamp')[:10]

            for msg in reversed(recent_messages):
                conversation_history.append({
                    'role': 'user' if msg.is_user else 'assistant',
                    'content': msg.content
                })
        except Exception as e:
            print(f"Erreur récupération historique: {e}")

    return get_mistral_response(user_message, conversation_history)