{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}
{% if user_form.instance.id %}
Modifier l'utilisateur - Maroc Cultures
{% else %}
Ajouter un utilisateur - Maroc Cultures
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* User Form Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .form-container {
        padding: 15px 0;
    }

    .form-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 8px 12px;
        border-radius: 5px;
        margin-bottom: 12px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    }

    .form-header h1 {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1px;
    }

    .form-header p {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 0.75rem;
    }

    .form-header .btn-outline-light {
        border: 1px solid rgba(255, 255, 255, 0.5);
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .form-header .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .form-card {
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
        margin-bottom: 12px;
        overflow: hidden;
    }

    .form-card-header {
        background-color: #f8f9fa;
        padding: 6px 10px;
        border-bottom: 1px solid #eee;
    }

    .form-card-header h2 {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #1abc9c;
    }

    .form-card-body {
        padding: 12px 10px;
    }

    .form-group {
        margin-bottom: 10px;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 2px;
        color: #333;
        font-size: 0.75rem;
    }

    .form-control {
        border-radius: 3px;
        padding: 5px 8px;
        border: 1px solid #ddd;
        transition: all 0.2s;
        font-size: 0.8rem;
        height: auto;
    }

    .form-control:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 0.1rem rgba(39, 174, 96, 0.15);
    }

    .form-check-input:checked {
        background-color: #27ae60;
        border-color: #27ae60;
    }

    .btn-submit {
        background-color: #27ae60;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: 500;
        transition: all 0.2s;
        font-size: 0.8rem;
    }

    .btn-submit:hover {
        background-color: #219653;
        transform: translateY(-1px);
    }

    .btn-cancel {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: 500;
        transition: all 0.2s;
        margin-right: 6px;
        font-size: 0.8rem;
    }

    .btn-cancel:hover {
        background-color: #5a6268;
        transform: translateY(-1px);
    }

    .form-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 12px;
    }

    .form-error {
        color: #c0392b;
        font-size: 0.7rem;
        margin-top: 1px;
    }

    .form-help {
        color: #6c757d;
        font-size: 0.7rem;
        margin-top: 1px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container form-container">
    <div class="form-header d-flex justify-content-between align-items-center">
        <div>
            {% if user_form.instance.id %}
            <h1><i class="fas fa-user-edit me-1"></i>Modifier l'utilisateur</h1>
            <p>Modifiez les informations de l'utilisateur</p>
            {% else %}
            <h1><i class="fas fa-user-plus me-1"></i>Ajouter un utilisateur</h1>
            <p>Créez un nouvel utilisateur dans le système</p>
            {% endif %}
        </div>
        <div>
            <a href="{% url 'admin_dashboard' %}" class="btn btn-sm btn-outline-light">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-5">
            <div class="form-card">
                <div class="form-card-header">
                    {% if user_form.instance.id %}
                    <h2><i class="fas fa-user-edit me-1"></i>Informations de l'utilisateur</h2>
                    {% else %}
                    <h2><i class="fas fa-user-plus me-1"></i>Informations de l'utilisateur</h2>
                    {% endif %}
                </div>
                <div class="form-card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2 px-3">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="form-group">
                            <label for="id_username" class="form-label">Nom d'utilisateur</label>
                            <input type="text" name="username" id="id_username" class="form-control {% if form.username.errors %}is-invalid{% endif %}" value="{{ form.username.value|default:'' }}" required>
                            {% if form.username.errors %}
                            <div class="form-error">{{ form.username.errors.0 }}</div>
                            {% endif %}
                            <div class="form-help">Le nom d'utilisateur doit être unique.</div>
                        </div>

                        <div class="form-group">
                            <label for="id_email" class="form-label">Adresse email</label>
                            <input type="email" name="email" id="id_email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" value="{{ form.email.value|default:'' }}" required>
                            {% if form.email.errors %}
                            <div class="form-error">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="id_mdp" class="form-label">Mot de passe</label>
                            <input type="password" name="password" id="id_mdp" class="form-control {% if form.mdp.errors %}is-invalid{% endif %}" {% if not user_form.instance.id %}required{% endif %}>
                            {% if form.mdp.errors %}
                            <div class="form-error">{{ form.mdp.errors.0 }}</div>
                            {% endif %}
                            {% if user_form.instance.id %}
                            <div class="form-help">Laissez vide pour conserver le mot de passe actuel.</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="id_privilege" class="form-label">Privilège</label>
                            <select name="privilege" id="id_privilege" class="form-control">
                                <option value="user" {% if user_form.instance.privilege == 'user' %}selected{% endif %}>Utilisateur</option>
                                <option value="admin" {% if user_form.instance.privilege == 'admin' %}selected{% endif %}>Administrateur</option>
                            </select>
                        </div>

                        <div class="form-footer">
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-save me-1"></i>Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
