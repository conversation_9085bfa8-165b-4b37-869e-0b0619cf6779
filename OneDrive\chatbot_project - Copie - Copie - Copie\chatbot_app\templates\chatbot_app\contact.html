{% extends 'chatbot_app/base.html' %}

{% block title %}Contact - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Styles pour la page de contact */
    .contact-section {
        padding: 50px 0; /* Réduit de 80px à 50px */
        position: relative;
        background-color: #f9f9f9;
    }

    .contact-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,20 L20,0 L40,20 L20,40 Z" fill="none" stroke="rgba(192, 57, 43, 0.05)" stroke-width="1"/></svg>');
        background-size: 40px 40px;
        opacity: 0.5;
        z-index: 0;
    }

    .contact-header {
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.9) 0%, rgba(192, 57, 43, 0.8) 50%, rgba(39, 174, 96, 0.7) 100%);
        padding: 40px 0; /* Réduit de 60px à 40px */
        margin-bottom: 40px; /* Réduit de 60px à 40px */
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .contact-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .contact-header::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="%23f9f9f9" opacity="1"></path></svg>');
        background-size: cover;
        z-index: 1;
    }

    .contact-title {
        font-size: 2.5rem; /* Réduit de 3rem à 2.5rem */
        font-weight: 800;
        margin-bottom: 15px; /* Réduit de 20px à 15px */
        position: relative;
        display: inline-block;
        z-index: 2;
        animation: fadeInDown 0.8s ease-out; /* Animation plus rapide */
    }

    .contact-title::after {
        content: '';
        position: absolute;
        width: 0;
        height: 3px; /* Réduit de 4px à 3px */
        background: white;
        bottom: -8px; /* Réduit de -10px à -8px */
        left: 50%;
        transform: translateX(-50%);
        animation: expandWidth 1s ease-out forwards; /* Animation plus rapide */
        animation-delay: 0.4s; /* Délai réduit */
    }

    @keyframes expandWidth {
        0% { width: 0; }
        100% { width: 70px; } /* Réduit de 80px à 70px */
    }

    .contact-subtitle {
        font-size: 1.1rem; /* Réduit de 1.2rem à 1.1rem */
        max-width: 750px; /* Réduit de 800px à 750px */
        margin: 0 auto;
        line-height: 1.6; /* Réduit de 1.8 à 1.6 */
        position: relative;
        z-index: 2;
        animation: fadeInUp 0.8s ease-out; /* Animation plus rapide */
        animation-delay: 0.2s; /* Délai réduit */
        opacity: 0;
        animation-fill-mode: forwards;
    }

    .contact-form-container {
        position: relative;
        z-index: 1;
    }

    .contact-card {
        border-radius: 12px; /* Réduit de 15px à 12px */
        overflow: hidden;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* Réduit l'ombre */
        transition: all 0.3s ease;
        border: none;
        position: relative;
        background-color: white;
    }

    .contact-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px; /* Réduit de 5px à 4px */
        background: linear-gradient(90deg, #c0392b, #27ae60);
    }

    .contact-form-header {
        background-color: #27ae60;
        color: white;
        padding: 15px; /* Réduit de 20px à 15px */
        text-align: center;
        font-weight: 700;
        font-size: 1.3rem; /* Réduit de 1.5rem à 1.3rem */
    }

    .contact-form {
        padding: 20px; /* Réduit de 30px à 20px */
    }

    .form-group {
        margin-bottom: 20px; /* Réduit de 25px à 20px */
        position: relative;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
        font-size: 0.95rem;
    }

    .form-control {
        height: 45px; /* Réduit de 50px à 45px */
        border-radius: 6px; /* Réduit de 8px à 6px */
        border: 2px solid #e0e0e0;
        padding: 8px 12px; /* Réduit de 10px 15px à 8px 12px */
        font-size: 0.95rem; /* Réduit de 1rem à 0.95rem */
        transition: all 0.3s;
        background-color: #f9f9f9;
    }

    .form-control:hover {
        border-color: #ccc;
        background-color: #f5f5f5;
    }

    .form-control:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2); /* Réduit de 3px à 2px */
        outline: none;
        background-color: white;
    }

    .form-group label {
        position: relative;
        display: inline-block;
    }

    .form-group label::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        background-color: #27ae60;
        bottom: -3px; /* Réduit de -4px à -3px */
        left: 0;
        transition: width 0.3s ease;
    }

    .form-group:focus-within label::after {
        width: 100%;
    }

    textarea.form-control {
        min-height: 120px; /* Réduit de 150px à 120px */
        resize: vertical;
    }

    .contact-btn {
        background-color: #27ae60;
        color: white;
        border: none;
        border-radius: 30px; /* Réduit de 50px à 30px */
        padding: 10px 25px; /* Réduit de 12px 30px à 10px 25px */
        font-size: 0.95rem; /* Réduit de 1rem à 0.95rem */
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-block;
        text-align: center;
    }

    .contact-btn:hover {
        background-color: #219653;
        transform: translateY(-2px); /* Réduit de -3px à -2px */
        box-shadow: 0 8px 15px rgba(39, 174, 96, 0.3); /* Réduit l'ombre */
    }

    .contact-info-card {
        background-color: white;
        border-radius: 12px; /* Réduit de 15px à 12px */
        overflow: hidden;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* Réduit l'ombre */
        height: 100%;
        position: relative;
    }

    .contact-info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px; /* Réduit de 5px à 4px */
        background: linear-gradient(90deg, #c0392b, #27ae60);
    }

    .contact-info-header {
        background-color: #c0392b;
        color: white;
        padding: 15px; /* Réduit de 20px à 15px */
        text-align: center;
        font-weight: 700;
        font-size: 1.3rem; /* Réduit de 1.5rem à 1.3rem */
    }

    .contact-info-content {
        padding: 20px; /* Réduit de 30px à 20px */
    }

    .contact-info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px; /* Réduit de 25px à 20px */
        padding: 8px; /* Réduit de 10px à 8px */
        border-radius: 6px; /* Réduit de 8px à 6px */
        transition: all 0.3s ease;
    }

    .contact-info-item:hover {
        background-color: rgba(39, 174, 96, 0.05);
        transform: translateX(3px); /* Réduit de 5px à 3px */
    }

    .contact-info-icon {
        width: 40px; /* Réduit de 50px à 40px */
        height: 40px; /* Réduit de 50px à 40px */
        background-color: rgba(39, 174, 96, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px; /* Réduit de 15px à 12px */
        color: #27ae60;
        font-size: 1rem; /* Réduit de 1.2rem à 1rem */
        transition: all 0.3s ease;
    }

    .contact-info-item:hover .contact-info-icon {
        background-color: #27ae60;
        color: white;
        transform: scale(1.05); /* Réduit de 1.1 à 1.05 */
    }

    .contact-info-text {
        flex: 1;
    }

    .contact-info-text h4 {
        margin: 0 0 4px; /* Réduit de 5px à 4px */
        font-size: 1rem; /* Réduit de 1.1rem à 1rem */
        color: #333;
    }

    .contact-info-text p {
        margin: 0;
        color: #666;
        line-height: 1.4; /* Réduit de 1.5 à 1.4 */
        font-size: 0.95rem; /* Ajout d'une taille de police légèrement réduite */
    }

    .social-links {
        display: flex;
        margin-top: 15px; /* Réduit de 20px à 15px */
    }

    .social-link {
        width: 35px; /* Réduit de 40px à 35px */
        height: 35px; /* Réduit de 40px à 35px */
        background-color: #f0f0f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px; /* Réduit de 10px à 8px */
        color: #333;
        transition: all 0.3s;
        text-decoration: none;
        font-size: 0.9rem; /* Ajout d'une taille de police légèrement réduite */
    }

    .social-link:hover {
        background-color: #27ae60;
        color: white;
        transform: translateY(-2px); /* Réduit de -3px à -2px */
    }

    .moroccan-pattern {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 80px; /* Réduit de 100px à 80px */
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><path d="M0,20 L20,0 L40,20 L20,40 Z" fill="none" stroke="rgba(192, 57, 43, 0.1)" stroke-width="1"/><path d="M20,10 L30,20 L20,30 L10,20 Z" fill="none" stroke="rgba(39, 174, 96, 0.1)" stroke-width="1"/></svg>');
        background-size: 35px 35px; /* Réduit de 40px à 35px */
        opacity: 0.5;
        z-index: -1;
    }
</style>
{% endblock %}

{% block content %}
<!-- Contact Header Section -->
<section class="contact-header">
    <div class="container">
        <h1 class="contact-title">Contactez-nous</h1>
        <p class="contact-subtitle">
            Nous sommes à votre écoute ! N'hésitez pas à nous contacter pour toute question, suggestion ou demande d'information concernant nos activités et services.
        </p>
    </div>
</section>

<section class="contact-section">
    <div class="container contact-form-container">
        <div class="row">
            <div class="col-lg-7 mb-4 mb-lg-0">
                <div class="contact-card" style="animation: fadeInLeft 1s ease-out;">
                    <div class="contact-form-header">
                        <i class="fas fa-envelope me-2"></i> Envoyez-nous un message
                    </div>
                    <div class="contact-form">
                        <form method="post">
                            {% csrf_token %}

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">Nom complet</label>
                                        <input type="text" class="form-control" id="name" name="name" placeholder="Votre nom" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Adresse email</label>
                                        <input type="email" class="form-control" id="email" name="email" placeholder="Votre email" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="subject">Sujet</label>
                                <input type="text" class="form-control" id="subject" name="subject" placeholder="Sujet de votre message">
                            </div>

                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea class="form-control" id="message" name="message" placeholder="Votre message" required></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="contact-btn">
                                    <i class="fas fa-paper-plane me-2"></i> Envoyer le message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-5">
                <div class="contact-info-card" style="animation: fadeInRight 1s ease-out;">
                    <div class="contact-info-header">
                        <i class="fas fa-info-circle me-2"></i> Nos coordonnées
                    </div>
                    <div class="contact-info-content">
                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-info-text">
                                <h4>Adresse</h4>
                                <p>456 Avenue Culturelle, Montréal, QC H2X 3Y7, Canada</p>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div class="contact-info-text">
                                <h4>Téléphone</h4>
                                <p>+****************</p>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-info-text">
                                <h4>Email</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-info-text">
                                <h4>Heures d'ouverture</h4>
                                <p>Lundi - Vendredi: 9h00 - 17h00<br>Samedi: 10h00 - 15h00</p>
                            </div>
                        </div>

                        <h4 class="mt-4 mb-3">Suivez-nous</h4>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="moroccan-pattern"></div>
</section>

<!-- Map Section -->
<section class="map-section py-4"> <!-- Réduit de py-5 à py-4 -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="map-container">
                    <div class="map-card" style="animation: fadeInUp 0.8s ease-out;"> <!-- Animation plus rapide -->
                        <div class="map-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3><i class="fas fa-map-marked-alt me-2"></i> Notre emplacement</h3>
                                <a href="https://www.google.com/maps/search/?api=1&query=45.5117,-73.5573"
                                   target="_blank"
                                   class="btn btn-sm btn-light">
                                    <i class="fas fa-external-link-alt me-1"></i> Ouvrir dans Maps
                                </a>
                            </div>
                        </div>
                        <div class="map-content">
                            <div class="map-frame">
                                <!-- Google Maps Embed -->
                                <iframe
                                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2795.6847!2d-73.5573!3d45.5117!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDXCsDMwJzQyLjEiTiA3M8KwMzMnMjYuMyJX!5e0!3m2!1sfr!2sca!4v1234567890"
                                    width="100%"
                                    height="400"
                                    style="border: none;"
                                    allowfullscreen=""
                                    loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade">
                                </iframe>

                                <!-- Fallback: OpenStreetMap si Google Maps ne fonctionne pas -->
                                <noscript>
                                    <iframe
                                        src="https://www.openstreetmap.org/export/embed.html?bbox=-73.5673%2C45.5017%2C-73.5473%2C45.5217&layer=mapnik&marker=45.5117%2C-73.5573"
                                        width="100%"
                                        height="400"
                                        style="border: none;">
                                    </iframe>
                                </noscript>
                            </div>
                        </div>

                        <!-- Informations de transport -->
                        <div class="transport-info p-3 bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-subway me-2 text-primary"></i> Transport en commun</h6>
                                    <p class="small mb-2">Métro: Station Place-des-Arts (ligne verte)</p>
                                    <p class="small mb-0">Bus: Lignes 15, 55, 129</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-car me-2 text-success"></i> Stationnement</h6>
                                    <p class="small mb-2">Stationnement payant disponible</p>
                                    <p class="small mb-0">Accessible aux personnes à mobilité réduite</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .map-section {
        background-color: #f9f9f9;
        position: relative;
    }

    .map-container {
        position: relative;
        z-index: 1;
    }

    .map-card {
        border-radius: 12px; /* Réduit de 15px à 12px */
        overflow: hidden;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* Réduit l'ombre */
        background-color: white;
        position: relative;
    }

    .map-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px; /* Réduit de 5px à 4px */
        background: linear-gradient(90deg, #c0392b, #27ae60);
        z-index: 2;
    }

    .map-header {
        background-color: #27ae60;
        color: white;
        padding: 12px 15px; /* Réduit de 15px 20px à 12px 15px */
        font-weight: 600;
    }

    .map-header h3 {
        margin: 0;
        font-size: 1.2rem; /* Réduit de 1.3rem à 1.2rem */
        color: white;
    }

    .map-content {
        padding: 0;
    }

    .map-frame {
        width: 100%;
        height: 400px; /* Réduit de 450px à 400px */
        border: none;
    }
</style>
{% endblock %}
