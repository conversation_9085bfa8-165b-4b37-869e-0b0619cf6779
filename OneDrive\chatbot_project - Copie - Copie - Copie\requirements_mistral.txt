# Requirements pour Maroc Cultures Chatbot avec Mistral AI
# Core Django
Django==5.2.1
python-dotenv==1.1.0

# Base de données
PyMySQL==1.1.1
mysql-connector-python==9.3.0
mysqlclient==2.2.7
psycopg2-binary==2.9.10
dj-database-url==2.3.0

# API et HTTP
requests==2.32.3
httpx==0.28.1
httpcore==1.0.9
urllib3==2.4.0

# Mistral AI
mistralai==1.7.0

# Utilitaires
python-dateutil==2.9.0.post0
six==1.17.0
certifi==2025.4.26
charset-normalizer==3.4.2
idna==3.10

# Type checking et validation
pydantic==2.11.4
pydantic_core==2.33.2
typing_extensions==4.13.2
typing-inspection==0.4.0
annotated-types==0.7.0
eval_type_backport==0.2.2

# Async support
anyio==4.9.0
sniffio==1.3.1
h11==0.16.0

# SQL parsing
sqlparse==0.5.3

# ASGI
asgiref==3.8.1

# Timezone data
tzdata==2025.2
