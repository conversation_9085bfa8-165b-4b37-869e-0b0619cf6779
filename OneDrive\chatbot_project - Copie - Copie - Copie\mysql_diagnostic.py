#!/usr/bin/env python3
"""
Diagnostic MySQL pour le projet Chatbot Maroc Cultures
Ce script aide à diagnostiquer les problèmes de connexion MySQL
"""

import socket
import subprocess
import sys

def check_mysql_packages():
    """Vérifie les packages MySQL Python"""
    print("🔍 Vérification des packages MySQL Python...")
    print("=" * 50)
    
    packages = {
        'mysql.connector': 'mysql-connector-python',
        'MySQLdb': 'mysqlclient', 
        'pymysql': 'PyMySQL'
    }
    
    available = []
    for package, pip_name in packages.items():
        try:
            __import__(package)
            print(f"✅ {package} ({pip_name}): Disponible")
            available.append(package)
        except ImportError:
            print(f"❌ {package} ({pip_name}): Non disponible")
    
    return available

def check_mysql_port():
    """Vérifie si le port MySQL est ouvert"""
    print("\n🔍 Vérification du port MySQL...")
    print("=" * 50)
    
    host = 'localhost'
    port = 3306
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 secondes timeout
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} est ouvert sur {host}")
            return True
        else:
            print(f"❌ Port {port} est fermé sur {host}")
            return False
    except Exception as e:
        print(f"❌ Erreur lors du test du port: {e}")
        return False

def check_mysql_service():
    """Vérifie si le service MySQL est en cours d'exécution"""
    print("\n🔍 Vérification du service MySQL...")
    print("=" * 50)
    
    try:
        # Essayer de vérifier le service MySQL sur Windows
        result = subprocess.run(['sc', 'query', 'MySQL'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            if 'RUNNING' in result.stdout:
                print("✅ Service MySQL est en cours d'exécution")
                return True
            else:
                print("❌ Service MySQL n'est pas en cours d'exécution")
                print("💡 Démarrez MySQL depuis les Services Windows ou XAMPP")
                return False
        else:
            print("❌ Service MySQL non trouvé")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout lors de la vérification du service")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_simple_connection():
    """Test de connexion simple avec timeout"""
    print("\n🔍 Test de connexion MySQL...")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        print("Tentative de connexion à MySQL...")
        
        # Configuration avec timeout
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'connection_timeout': 5,  # 5 secondes timeout
            'autocommit': True
        }
        
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ Connexion MySQL réussie!")
            
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📊 Version MySQL: {version}")
            
            # Vérifier les bases de données
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            db_names = [db[0] for db in databases]
            
            print(f"🗄️ Bases de données disponibles: {len(databases)}")
            for db in db_names:
                print(f"  - {db}")
            
            # Vérifier si notre base existe
            if 'chatbot_maroc_cultures' in db_names:
                print("✅ Base de données 'chatbot_maroc_cultures' existe")
            else:
                print("❌ Base de données 'chatbot_maroc_cultures' n'existe pas")
                print("💡 Vous devez créer la base de données")
            
            cursor.close()
            connection.close()
            return True
            
    except ImportError:
        print("❌ Package mysql-connector-python non disponible")
        return False
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def provide_solutions():
    """Fournit des solutions selon les problèmes détectés"""
    print("\n💡 Solutions possibles:")
    print("=" * 50)
    
    print("Si MySQL n'est pas installé:")
    print("  1. Téléchargez MySQL Community Server: https://dev.mysql.com/downloads/mysql/")
    print("  2. Ou installez XAMPP: https://www.apachefriends.org/")
    print()
    
    print("Si MySQL est installé mais ne démarre pas:")
    print("  1. Ouvrez les Services Windows (services.msc)")
    print("  2. Cherchez 'MySQL' et démarrez le service")
    print("  3. Ou démarrez MySQL depuis XAMPP Control Panel")
    print()
    
    print("Si le port 3306 est bloqué:")
    print("  1. Vérifiez le pare-feu Windows")
    print("  2. Vérifiez si un autre service utilise le port 3306")
    print("  3. Essayez de changer le port MySQL")
    print()
    
    print("Si les packages Python manquent:")
    print("  pip install mysql-connector-python")
    print("  pip install mysqlclient")
    print("  pip install PyMySQL")

def main():
    """Fonction principale de diagnostic"""
    print("🚀 Diagnostic MySQL - Chatbot Maroc Cultures")
    print("=" * 60)
    
    # Vérifications
    packages_ok = check_mysql_packages()
    port_ok = check_mysql_port()
    service_ok = check_mysql_service()
    
    if packages_ok and port_ok:
        connection_ok = test_simple_connection()
    else:
        connection_ok = False
    
    # Résumé
    print("\n📋 Résumé du diagnostic:")
    print("=" * 50)
    print(f"Packages Python MySQL: {'✅' if packages_ok else '❌'}")
    print(f"Port MySQL (3306): {'✅' if port_ok else '❌'}")
    print(f"Service MySQL: {'✅' if service_ok else '❌'}")
    print(f"Connexion MySQL: {'✅' if connection_ok else '❌'}")
    
    if all([packages_ok, port_ok, service_ok, connection_ok]):
        print("\n🎉 Tout fonctionne! Vous pouvez utiliser MySQL avec Django.")
        print("Prochaines étapes:")
        print("  1. python manage.py migrate")
        print("  2. python manage.py createsuperuser")
        print("  3. python manage.py runserver")
    else:
        provide_solutions()

if __name__ == "__main__":
    main()
