# 🧪 QUESTIONS DE TEST POUR CAPTURES CHATBOT

## 🎯 **OBJECTIF**
<PERSON>er le chatbot avec des questions variées pour obtenir des captures d'écran représentatives de toutes les fonctionnalités.

---

## 📋 **SÉQUENCE DE TEST RECOMMANDÉE**

### **🔄 1. TEST SYSTÈME HYBRIDE**

#### **📋 Questions JSON (Réponses Rapides)**
```
1. "Bonjour"
   → Réponse d'accueil personnalisée

2. "date"
   → Informations sur les dates d'événements

3. "lieu"
   → Lieux des manifestations

4. "prix"
   → Informations tarifaires

5. "horaire"
   → Horaires des spectacles

6. "contact"
   → Coordonnées de l'association
```

#### **🤖 Questions Mistral AI (Réponses Intelligentes)**
```
1. "Parle-moi du tajine marocain"
   → Description détaillée du plat emblématique

2. "Quelle est l'histoire de Marrakech ?"
   → Contexte historique de la ville impériale

3. "Qu'est-ce que la musique gnawa ?"
   → Explication de ce style musical traditionnel

4. "Comment préparer un couscous royal ?"
   → Recette et traditions culinaires

5. "Décris l'artisanat de Fès"
   → Savoir-faire artisanal de la ville
```

#### **💡 Questions Fallback (Réponses Prédéfinies)**
```
1. "artisanat"
   → Description générale de l'artisanat marocain

2. "musique"
   → Présentation de la diversité musicale

3. "marrakech"
   → Informations sur la Perle du Sud

4. "festival"
   → Description des festivals organisés
```

---

## 🎬 **SCÉNARIOS DE CAPTURE**

### **📸 Capture 1 : Interface Vide**
- **Action** : Ouvrir /chat/
- **État** : Interface propre, prête à l'utilisation
- **Éléments visibles** :
  - Zone de conversation vide
  - Champ de saisie avec placeholder
  - Bouton d'envoi
  - Design responsive

### **📸 Capture 2 : Première Interaction**
```
Utilisateur : "Bonjour"
Chatbot : "Bonjour ! Je suis l'assistant virtuel de Maroc Cultures..."
```
- **Montre** : Système de réponse JSON rapide
- **Temps** : < 1 seconde

### **📸 Capture 3 : Question Simple**
```
Utilisateur : "date"
Chatbot : "📅 Pour connaître les dates de nos événements..."
```
- **Montre** : Gestion des mots-clés simples
- **Fonctionnalité** : Score adaptatif (1 mot = score min 1)

### **📸 Capture 4 : Question Culturelle Complexe**
```
Utilisateur : "Parle-moi du tajine aux olives et citrons confits"
Chatbot : "🍲 Le tajine aux olives et citrons confits est un délicieux..."
```
- **Montre** : Intelligence artificielle Mistral en action
- **Temps** : 3-5 secondes
- **Qualité** : Réponse détaillée et contextuelle

### **📸 Capture 5 : Conversation Continue**
```
Utilisateur : "Et comment le préparer ?"
Chatbot : "Pour préparer ce tajine traditionnel..."
```
- **Montre** : Contexte conversationnel maintenu
- **Fonctionnalité** : Référence à la question précédente

### **📸 Capture 6 : Historique de Conversation**
- **Action** : Scroll vers le haut
- **Montre** : Plusieurs échanges sauvegardés
- **Fonctionnalité** : Historique complet accessible

---

## 🎯 **QUESTIONS SPÉCIFIQUES PAR DOMAINE**

### **🍽️ Gastronomie Marocaine**
```
1. "Qu'est-ce que le couscous royal ?"
2. "Recette du tajine aux pruneaux"
3. "Comment préparer le thé à la menthe ?"
4. "Pâtisseries traditionnelles marocaines"
5. "Spécialités de Ramadan"
```

### **🎨 Artisanat et Culture**
```
1. "Tapis berbères de l'Atlas"
2. "Poterie de Salé"
3. "Zellige de Fès"
4. "Bijoux en argent du Sud"
5. "Cuir de Marrakech"
```

### **🎵 Musique et Arts**
```
1. "Instruments de musique gnawa"
2. "Chaâbi populaire"
3. "Musique andalouse"
4. "Ahidous berbère"
5. "Festivals de musique"
```

### **🏛️ Villes et Histoire**
```
1. "Médina de Fès"
2. "Place Jemaa el-Fna"
3. "Kasbah des Oudayas"
4. "Hassan II Casablanca"
5. "Jardins de Majorelle"
```

### **📅 Événements Maroc Cultures**
```
1. "Festival Mawazine 2025"
2. "Génération Mawazine"
3. "Théâtre des Cultures"
4. "Comment participer ?"
5. "Inscription événements"
```

---

## 📱 **TESTS RESPONSIVE**

### **💻 Desktop (1920x1080)**
- **Interface** : Pleine largeur
- **Chat** : Zone conversation large
- **Saisie** : Champ étendu
- **Navigation** : Menu complet

### **📱 Tablette (768x1024)**
- **Interface** : Adaptée largeur moyenne
- **Chat** : Zone optimisée
- **Saisie** : Taille intermédiaire
- **Navigation** : Menu réduit ou hamburger

### **📱 Mobile (375x667)**
- **Interface** : Plein écran
- **Chat** : Zone maximisée
- **Saisie** : Optimisée tactile
- **Navigation** : Menu hamburger

---

## 🔧 **TESTS TECHNIQUES**

### **⚡ Performance**
```
1. Question JSON → Mesurer temps < 100ms
2. Question Mistral → Mesurer temps < 5s
3. Question fallback → Mesurer temps < 200ms
4. Charge multiple → Tester plusieurs questions rapides
```

### **🛡️ Robustesse**
```
1. Question vide → Gestion erreur
2. Caractères spéciaux → Validation
3. Message très long → Limitation
4. Spam rapide → Protection
```

### **🔄 Fallback**
```
1. Simuler erreur Mistral → Fallback automatique
2. Question inconnue → Réponse par défaut
3. Timeout → Gestion gracieuse
```

---

## 📊 **LOGS À SURVEILLER**

### **🔍 Pendant les Tests**
```
📋 Recherche dans le fichier JSON pour: [question]...
🔍 Message: '[question]' (X mot(s)), score minimum requis: Y
✅ Réponse trouvée dans le JSON (score: Z, min requis: Y)
📄 Réponse JSON: [début de la réponse]...
```

### **🤖 Appels Mistral**
```
⚠️ Aucune correspondance suffisante dans le JSON
🤖 Aucune réponse JSON trouvée, utilisation de Mistral AI...
👤 User ID: None
📤 Réponse Mistral reçue: [début de la réponse]...
✅ Mistral AI a répondu avec succès !
```

### **💡 Fallback**
```
❌ Erreur Mistral API: [erreur]
⚠️ Module 'requests' non installé, utilisation de réponses prédéfinies...
✅ Réponse prédéfinie trouvée pour '[mot-clé]'
```

---

## 🎨 **CONSEILS POUR CAPTURES RÉUSSIES**

### **📐 Composition**
- **Cadrage** : Inclure interface complète
- **Messages** : 3-4 échanges visibles
- **Scroll** : Position optimale
- **Éléments** : Boutons et champs visibles

### **⏱️ Timing**
- **Attendre** : Réponse complète avant capture
- **Animations** : Laisser finir les transitions
- **Chargement** : S'assurer que tout est affiché

### **🎯 Points d'Intérêt**
- **Types de réponses** : JSON vs Mistral vs Fallback
- **Temps de réponse** : Rapidité du système
- **Qualité** : Pertinence des réponses
- **Interface** : Design et ergonomie

---

## ✅ **CHECKLIST DE TEST**

### **🧪 Fonctionnalités Testées**
- [ ] Réponses JSON rapides
- [ ] Intelligence Mistral AI
- [ ] Fallback prédéfini
- [ ] Contexte conversationnel
- [ ] Gestion d'erreurs
- [ ] Interface responsive

### **📸 Captures Réalisées**
- [ ] Interface vide
- [ ] Première interaction
- [ ] Question simple (JSON)
- [ ] Question complexe (Mistral)
- [ ] Conversation continue
- [ ] Historique complet
- [ ] Versions mobile/tablette

### **📊 Métriques Observées**
- [ ] Temps de réponse JSON < 100ms
- [ ] Temps de réponse Mistral < 5s
- [ ] Taux de succès 100%
- [ ] Logs détaillés fonctionnels

---

**🇲🇦 Maroc Cultures - Tests complets pour des captures d'écran parfaites ! ✨**

**Utilisez ces questions pour démontrer toute la puissance de votre chatbot intelligent !** 🤖
