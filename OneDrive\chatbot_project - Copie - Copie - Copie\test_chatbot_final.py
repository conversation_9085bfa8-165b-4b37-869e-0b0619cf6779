#!/usr/bin/env python
"""
Test final du chatbot
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
import json

def test_chatbot_final():
    """Test final du chatbot avec les corrections"""
    print("🤖 Test final du chatbot Maroc Cultures")
    print("=" * 50)
    
    # Créer un client de test Django
    client = Client()
    
    # Messages de test
    test_messages = [
        "bonjour",
        "salut",
        "événements",
        "contact",
        "merci",
        "au revoir",
        "aide",
        "festival"
    ]
    
    success_count = 0
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test {i}: '{message}'")
        
        try:
            # Envoyer une requête POST
            response = client.post('/process_message/', {
                'message': message
            })
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    bot_response = data.get('response', 'Pas de réponse')
                    conversation_id = data.get('conversation_id', 'N/A')
                    
                    print(f"   ✅ Réponse: {bot_response[:80]}...")
                    print(f"   📋 Conversation ID: {conversation_id}")
                    success_count += 1
                except json.JSONDecodeError:
                    print(f"   ❌ Réponse non-JSON: {response.content.decode()[:80]}...")
            else:
                print(f"   ❌ Erreur HTTP: {response.content.decode()[:80]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print(f"\n📊 Résultats: {success_count}/{len(test_messages)} tests réussis")
    
    if success_count == len(test_messages):
        print("🎉 Tous les tests ont réussi ! Le chatbot fonctionne parfaitement.")
    elif success_count > 0:
        print("⚠️ Certains tests ont réussi, le chatbot fonctionne partiellement.")
    else:
        print("❌ Aucun test n'a réussi, il y a encore des problèmes.")

if __name__ == "__main__":
    test_chatbot_final()
