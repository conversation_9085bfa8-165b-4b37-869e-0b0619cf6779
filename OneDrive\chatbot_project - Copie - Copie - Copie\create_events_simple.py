#!/usr/bin/env python3
"""
Script simple pour créer des événements culturels marocains
"""

import pymysql
from datetime import datetime, timedelta

def create_events():
    """Créer des événements directement dans la base de données"""
    
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='',
        database='chatbot_maroc_cultures',
        charset='utf8mb4'
    )
    
    try:
        cursor = connection.cursor()
        
        print("🎭 Création d'événements culturels marocains")
        print("=" * 50)
        
        # Supprimer les anciens événements d'exemple
        cursor.execute("DELETE FROM chatbot_app_event WHERE title LIKE '%Festival%' OR title LIKE '%Exposition%' OR title LIKE '%Concert%' OR title LIKE '%Atelier%'")
        
        # Dates pour les événements
        today = datetime.now().date()
        
        events = [
            (
                'Festival Marocain de Montréal 2024',
                'Le plus grand festival culturel marocain au Canada. Découvrez la richesse de la culture marocaine à travers la musique, la danse, l\'artisanat et la gastronomie traditionnelle.',
                (today + timedelta(days=15)).strftime('%Y-%m-%d'),
                (today + timedelta(days=17)).strftime('%Y-%m-%d'),
                '10:00:00',
                '22:00:00',
                'Parc Jean-Drapeau, Montréal',
                0.00,
                5000,
                'Festival',
                'Association Culturelle Marocaine de Montréal',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ),
            (
                'Exposition d\'Art Contemporain Marocain',
                'Une exposition unique présentant les œuvres d\'artistes marocains contemporains. Peintures, sculptures et installations.',
                (today + timedelta(days=5)).strftime('%Y-%m-%d'),
                (today + timedelta(days=35)).strftime('%Y-%m-%d'),
                '09:00:00',
                '18:00:00',
                'Galerie d\'Art Contemporain, Centre-ville',
                15.00,
                200,
                'Exposition',
                'Galerie Maghreb Arts',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ),
            (
                'Concert de Musique Gnawa',
                'Soirée exceptionnelle de musique Gnawa avec des maîtres musiciens venus directement d\'Essaouira.',
                (today + timedelta(days=8)).strftime('%Y-%m-%d'),
                (today + timedelta(days=8)).strftime('%Y-%m-%d'),
                '20:00:00',
                '23:00:00',
                'Théâtre Corona, Montréal',
                45.00,
                800,
                'Concert',
                'Productions Culturelles Maghreb',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ),
            (
                'Atelier de Cuisine Marocaine',
                'Apprenez à préparer les plats emblématiques de la cuisine marocaine : tajine, couscous, pastilla.',
                (today + timedelta(days=12)).strftime('%Y-%m-%d'),
                (today + timedelta(days=12)).strftime('%Y-%m-%d'),
                '14:00:00',
                '18:00:00',
                'École Culinaire Internationale',
                85.00,
                25,
                'Atelier',
                'Chef Amina Benali',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ),
            (
                'Soirée Contes et Légendes du Maroc',
                'Une soirée magique dédiée aux contes traditionnels marocains avec conteurs professionnels.',
                (today + timedelta(days=20)).strftime('%Y-%m-%d'),
                (today + timedelta(days=20)).strftime('%Y-%m-%d'),
                '19:30:00',
                '22:00:00',
                'Bibliothèque Centrale, Salle des Conférences',
                12.00,
                150,
                'Spectacle',
                'Cercle des Conteurs Maghrébins',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ),
            (
                'Marché Artisanal Marocain',
                'Découvrez l\'artisanat authentique du Maroc : tapis berbères, poteries, bijoux en argent, maroquinerie.',
                (today + timedelta(days=25)).strftime('%Y-%m-%d'),
                (today + timedelta(days=27)).strftime('%Y-%m-%d'),
                '10:00:00',
                '19:00:00',
                'Place des Arts, Esplanade',
                0.00,
                3000,
                'Marché',
                'Coopérative des Artisans Marocains',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
        ]
        
        # Insérer les événements
        insert_query = """
        INSERT INTO chatbot_app_event 
        (title, description, date_start, date_end, time_start, time_end, location, price, max_participants, category, organizer, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        created_count = 0
        for event in events:
            try:
                cursor.execute(insert_query, event)
                print(f"✅ Événement créé: {event[0]}")
                print(f"   📅 Date: {event[2]} - {event[3]}")
                print(f"   📍 Lieu: {event[6]}")
                print(f"   💰 Prix: {event[7]}€")
                print("-" * 50)
                created_count += 1
            except Exception as e:
                print(f"❌ Erreur: {e}")
        
        connection.commit()
        
        # Vérifier le nombre total d'événements
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_event")
        total_events = cursor.fetchone()[0]
        
        print(f"\n🎉 {created_count} événements créés avec succès!")
        print(f"📊 Total d'événements dans la base: {total_events}")
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    create_events()
