# Script PowerShell pour lancer le serveur Maroc Cultures avec Mistral AI

Write-Host "🇲🇦 Maroc Cultures - Démarrage du serveur Django avec Mistral AI" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Aller dans le répertoire du projet
$projectPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $projectPath

Write-Host "📁 Répertoire actuel: $PWD" -ForegroundColor Yellow

Write-Host ""
Write-Host "🔧 Activation de l'environnement virtuel..." -ForegroundColor Cyan
& ".\venv\Scripts\Activate.ps1"

Write-Host ""
Write-Host "🚀 Démarrage du serveur Django..." -ForegroundColor Green
Write-Host "Serveur accessible sur: http://127.0.0.1:8000/" -ForegroundColor White

Write-Host ""
Write-Host "📝 Pages disponibles:" -ForegroundColor Yellow
Write-Host "  - Page d'accueil: http://127.0.0.1:8000/home/" -ForegroundColor White
Write-Host "  - Chat Mistral AI: http://127.0.0.1:8000/chat/" -ForegroundColor White
Write-Host "  - Historique: http://127.0.0.1:8000/chat/history/" -ForegroundColor White
Write-Host "  - Admin: http://127.0.0.1:8000/admin-maroc-cultures/dashboard/" -ForegroundColor White

Write-Host ""
Write-Host "⚠️  Appuyez sur Ctrl+C pour arrêter le serveur" -ForegroundColor Red
Write-Host ""

# Lancer le serveur Django
python manage.py runserver

Write-Host ""
Write-Host "👋 Serveur arrêté. Au revoir !" -ForegroundColor Green
