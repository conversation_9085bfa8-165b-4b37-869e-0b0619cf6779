import json
from mistral_api import get_mistral_response

# Charger la configuration
with open('config.json', 'r', encoding='utf-8') as config_file:
    config = json.load(config_file)

# Charger les données FAQ
with open('faq_data.json', 'r', encoding='utf-8') as faq_file:
    faq_data = json.load(faq_file)

def get_response(question):
    # Vérifier si la question correspond à une entrée dans la FAQ
    for item in faq_data:
        if question.lower() in item["question"].lower():
            return item["answer"]
    
    # Si aucune correspondance n'est trouvée, utiliser Mistral API
    try:
        return get_mistral_response(question)
    except Exception as e:
        print(f"Erreur lors de l'appel à Mistral API: {e}")
        # En cas d'erreur, retourner le message par défaut
        return config["default_messages"][config["default_language"]]

# Exemple d'utilisation
if __name__ == "__main__":
    while True:
        user_question = input("Posez votre question (ou 'quitter' pour sortir): ")
        if user_question.lower() == 'quitter':
            break
        response = get_response(user_question)
        print(f"\nRéponse: {response}\n")