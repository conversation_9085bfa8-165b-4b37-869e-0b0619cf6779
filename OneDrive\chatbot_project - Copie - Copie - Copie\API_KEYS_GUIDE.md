# 🔑 Guide d'Obtention des Clés API - Maroc Cultures

## 🌤️ **1. OpenWeatherMap API (GRATUIT)**
**Pour : Données météo du Maroc**

### Étapes :
1. Aller sur : https://openweathermap.org/api
2. C<PERSON>r sur "Sign Up" (gratuit)
3. Confirmer votre email
4. <PERSON>er dans "API Keys" dans votre dashboard
5. Copier votre clé API

### Utilisation :
```env
OPENWEATHER_API_KEY=********************************
```

### Limites gratuites :
- 1,000 appels/jour
- 60 appels/minute

---

## 🗺️ **2. Google Maps API (GRATUIT avec limites)**
**Pour : Cartes interactives et géolocalisation**

### Étapes :
1. Aller sur : https://console.cloud.google.com/
2. Créer un nouveau projet ou sélectionner un existant
3. Activer l'API "Maps JavaScript API"
4. <PERSON><PERSON> dans "Credentials" → "Create Credentials" → "API Key"
5. <PERSON>re<PERSON><PERSON> la clé (optionnel mais recommandé)

### Utilisation :
```env
GOOGLE_MAPS_API_KEY=AIzaSyBvOkBwGyRlMilyE-IdPFUBItaVKFunVdc
```

### Limites gratuites :
- $200 de crédit gratuit/mois
- ~28,000 chargements de carte/mois

---

## 📧 **3. Gmail SMTP (GRATUIT)**
**Pour : Envoi d'emails automatiques**

### Étapes :
1. Activer la vérification en 2 étapes sur votre compte Google
2. Aller dans : https://myaccount.google.com/apppasswords
3. Sélectionner "Mail" et votre appareil
4. Générer le mot de passe d'application

### Utilisation :
```env
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=abcd efgh ijkl mnop
```

---

## 🤖 **4. Hugging Face API (GRATUIT)**
**Pour : IA et traitement du langage naturel**

### Étapes :
1. Aller sur : https://huggingface.co/
2. Créer un compte gratuit
3. Aller dans Settings → Access Tokens
4. Créer un nouveau token

### Utilisation :
```env
HUGGINGFACE_API_KEY=hf_aBcDeFgHiJkLmNoPqRsTuVwXyZ1234567890
```

### Limites gratuites :
- 30,000 caractères/mois
- Modèles gratuits disponibles

---

## 📱 **5. Firebase (GRATUIT)**
**Pour : Notifications push et analytics**

### Étapes :
1. Aller sur : https://console.firebase.google.com/
2. Créer un nouveau projet
3. Aller dans Project Settings → General
4. Copier la configuration Web

### Utilisation :
```env
FIREBASE_API_KEY=AIzaSyBvOkBwGyRlMilyE-IdPFUBItaVKFunVdc
FIREBASE_PROJECT_ID=maroc-cultures-12345
```

---

## 📊 **6. Google Analytics (GRATUIT)**
**Pour : Statistiques de site web**

### Étapes :
1. Aller sur : https://analytics.google.com/
2. Créer un compte et une propriété
3. Copier l'ID de mesure (GA4)

### Utilisation :
```env
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

---

## 🔐 **7. Clés de Sécurité (À générer)**
**Pour : JWT et chiffrement**

### Génération automatique :
```python
# Exécuter dans le terminal Python
import secrets
print("JWT_SECRET_KEY:", secrets.token_urlsafe(32))
print("ENCRYPTION_KEY:", secrets.token_urlsafe(32))
```

### Utilisation :
```env
JWT_SECRET_KEY=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
ENCRYPTION_KEY=xyz987wvu654tsr321qpo098nml765kji432hgf109edc876ba
```

---

## 💰 **APIs Payantes (Optionnelles)**

### 🤖 **OpenAI API**
- Site : https://platform.openai.com/
- Prix : ~$0.002/1K tokens
- Très puissant pour le chatbot

### 🌍 **Google Translate API**
- Site : https://cloud.google.com/translate
- Prix : $20/1M caractères
- Pour traduction automatique

---

## 🚀 **Configuration Rapide**

### 1. **APIs Essentielles (GRATUITES)**
```env
OPENWEATHER_API_KEY=your_key_here
GOOGLE_MAPS_API_KEY=your_key_here
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
```

### 2. **APIs Avancées (GRATUITES)**
```env
HUGGINGFACE_API_KEY=your_key_here
FIREBASE_API_KEY=your_key_here
GOOGLE_ANALYTICS_ID=your_id_here
```

### 3. **Sécurité (OBLIGATOIRE)**
```env
JWT_SECRET_KEY=generated_secret_key
ENCRYPTION_KEY=generated_encryption_key
```

---

## ⚠️ **Sécurité Important**

1. **Ne jamais commiter le fichier .env**
2. **Ajouter .env au .gitignore**
3. **Utiliser des variables d'environnement en production**
4. **Restreindre les clés API par domaine/IP**
5. **Surveiller l'usage des APIs**

---

## 🎯 **Priorités d'Implémentation**

### **Phase 1 (Immédiat)**
- ✅ OpenWeatherMap (météo)
- ✅ Gmail SMTP (emails)
- ✅ Clés de sécurité

### **Phase 2 (Semaine suivante)**
- ✅ Google Maps (cartes)
- ✅ Google Analytics (stats)

### **Phase 3 (Optionnel)**
- ✅ Hugging Face (IA avancée)
- ✅ Firebase (notifications)

**Total coût : 0€ pour toutes les fonctionnalités de base !** 🇲🇦✨
