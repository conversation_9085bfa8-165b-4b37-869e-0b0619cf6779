# 📊 DIAGRAMMES DE CAS D'USAGE - MAROC CULTURES

## 🎯 **1. VUE D'ENSEMBLE DU SYSTÈME**

### **1.1 Acteurs Principaux**
- **👤 Visiteur** : Utilisateur non authentifié
- **🔐 Utilisateur Inscrit** : Membre de la communauté
- **👨‍💼 Administrateur** : Gestionnaire de la plateforme
- **🎭 Artiste** : Participant aux événements
- **🤖 Système Mistral** : API d'intelligence artificielle

### **1.2 Système Global**
```
┌─────────────────────────────────────────────────────────────┐
│                    MAROC CULTURES                           │
│                 Plateforme Culturelle                      │
├─────────────────────────────────────────────────────────────┤
│  🌐 Interface Web    🤖 Chatbot IA    📅 Événements       │
│  👤 Gestion Users   🛠️ Administration  📊 Analytics        │
└─────────────────────────────────────────────────────────────┘
```

---

## 👤 **2. CAS D'USAGE - VISITEUR**

### **2.1 Diagramme Visiteur**
```
                    👤 VISITEUR
                        │
        ┌───────────────┼───────────────┐
        │               │               │
   🏠 Consulter     💬 Utiliser     📅 Découvrir
   Page Accueil     Chatbot        Événements
        │               │               │
        │               │               │
   ┌────▼────┐     ┌────▼────┐     ┌────▼────┐
   │Présentation│   │Questions│     │Liste    │
   │Association │   │Culture  │     │Festivals│
   │Services    │   │Marocaine│     │Spectacles│
   └─────────────┘  └─────────────┘  └─────────────┘
```

### **2.2 Cas d'Usage Détaillés - Visiteur**

#### **CU-V-001 : Consulter Page d'Accueil**
- **Acteur** : Visiteur
- **Préconditions** : Accès internet
- **Déclencheur** : Visite du site web
- **Scénario principal** :
  1. Affichage de la landing page
  2. Lecture des sections informatives
  3. Découverte des services proposés
  4. Accès au bouton chat

#### **CU-V-002 : Utiliser le Chatbot**
- **Acteur** : Visiteur
- **Préconditions** : Page web chargée
- **Déclencheur** : Clic sur bouton chat
- **Scénario principal** :
  1. Ouverture interface chat
  2. Saisie question culturelle
  3. Traitement par système hybride
  4. Réception réponse personnalisée
  5. Continuation conversation

#### **CU-V-003 : Découvrir les Événements**
- **Acteur** : Visiteur
- **Préconditions** : Navigation sur le site
- **Déclencheur** : Clic menu événements
- **Scénario principal** :
  1. Affichage liste événements
  2. Consultation détails Festival Mawazine
  3. Lecture programme artistique
  4. Visualisation dates et lieux

---

## 🔐 **3. CAS D'USAGE - UTILISATEUR INSCRIT**

### **3.1 Diagramme Utilisateur Inscrit**
```
                🔐 UTILISATEUR INSCRIT
                        │
        ┌───────────────┼───────────────┐
        │               │               │
   🔑 S'Authentifier 💬 Chat Avancé  📅 Gérer Événements
        │               │               │
        │               │               │
   ┌────▼────┐     ┌────▼────┐     ┌────▼────┐
   │Login    │     │Historique│     │Inscription│
   │Register │     │Personnel │     │Réservation│
   │Profile  │     │Contexte  │     │Suivi     │
   └─────────────┘  └─────────────┘  └─────────────┘
```

### **3.2 Cas d'Usage Détaillés - Utilisateur**

#### **CU-U-001 : S'Inscrire**
- **Acteur** : Visiteur → Utilisateur
- **Préconditions** : Formulaire d'inscription
- **Déclencheur** : Désir de créer un compte
- **Scénario principal** :
  1. Remplissage formulaire (nom, email, mot de passe)
  2. Validation des données
  3. Création compte Django Auth
  4. Envoi email de confirmation
  5. Redirection vers page d'accueil

#### **CU-U-002 : Se Connecter**
- **Acteur** : Utilisateur inscrit
- **Préconditions** : Compte existant
- **Déclencheur** : Besoin d'authentification
- **Scénario principal** :
  1. Saisie email/mot de passe
  2. Vérification credentials
  3. Création session sécurisée
  4. Accès fonctionnalités personnalisées

#### **CU-U-003 : Consulter Historique Chat**
- **Acteur** : Utilisateur connecté
- **Préconditions** : Conversations précédentes
- **Déclencheur** : Accès page historique
- **Scénario principal** :
  1. Affichage conversations sauvegardées
  2. Recherche dans l'historique
  3. Reprise conversation interrompue
  4. Export des échanges

#### **CU-U-004 : S'Inscrire à un Événement**
- **Acteur** : Utilisateur connecté
- **Préconditions** : Événement disponible
- **Déclencheur** : Intérêt pour un festival
- **Scénario principal** :
  1. Consultation détails événement
  2. Vérification disponibilité
  3. Sélection options (tarifs, dates)
  4. Confirmation inscription
  5. Réception confirmation email

---

## 👨‍💼 **4. CAS D'USAGE - ADMINISTRATEUR**

### **4.1 Diagramme Administrateur**
```
                👨‍💼 ADMINISTRATEUR
                        │
        ┌───────────────┼───────────────┐
        │               │               │
   📊 Dashboard     👥 Gestion Users  📅 Gestion Événements
        │               │               │
        │               │               │
   ┌────▼────┐     ┌────▼────┐     ┌────▼────┐
   │Métriques│     │CRUD     │     │CRUD     │
   │Analytics│     │Modération│     │Planning │
   │Monitoring│     │Export   │     │Promotion│
   └─────────────┘  └─────────────┘  └─────────────┘
```

### **4.2 Cas d'Usage Détaillés - Administrateur**

#### **CU-A-001 : Accéder au Dashboard**
- **Acteur** : Administrateur
- **Préconditions** : Droits d'administration
- **Déclencheur** : URL admin directe
- **Scénario principal** :
  1. Navigation vers dashboard admin
  2. Affichage métriques principales
  3. Consultation statistiques temps réel
  4. Identification points d'attention

#### **CU-A-002 : Gérer les Utilisateurs**
- **Acteur** : Administrateur
- **Préconditions** : Accès admin
- **Déclencheur** : Modération nécessaire
- **Scénario principal** :
  1. Consultation liste utilisateurs
  2. Recherche/filtrage par critères
  3. Examen profils et activités
  4. Actions (modification, suspension)
  5. Documentation des décisions

#### **CU-A-003 : Gérer les Événements**
- **Acteur** : Administrateur
- **Préconditions** : Droits de gestion
- **Déclencheur** : Nouveau festival à créer
- **Scénario principal** :
  1. Création nouvel événement
  2. Saisie détails (dates, lieux, artistes)
  3. Upload images et médias
  4. Publication sur la plateforme
  5. Suivi inscriptions

#### **CU-A-004 : Analyser Performance Chatbot**
- **Acteur** : Administrateur
- **Préconditions** : Données d'usage
- **Déclencheur** : Révision périodique
- **Scénario principal** :
  1. Consultation logs détaillés
  2. Analyse taux de succès (JSON/Mistral/Fallback)
  3. Identification questions fréquentes
  4. Optimisation base de connaissances

---

## 🤖 **5. CAS D'USAGE - SYSTÈME CHATBOT**

### **5.1 Diagramme Système Chatbot**
```
                🤖 SYSTÈME CHATBOT
                        │
        ┌───────────────┼───────────────┐
        │               │               │
   📋 Recherche JSON 🧠 Mistral AI   💡 Fallback
        │               │               │
        │               │               │
   ┌────▼────┐     ┌────▼────┐     ┌────▼────┐
   │Score    │     │API Call │     │Réponses │
   │Matching │     │Context  │     │Prédéfinies│
   │Fast     │     │Intelligent│   │Robuste  │
   └─────────────┘  └─────────────┘  └─────────────┘
```

### **5.2 Cas d'Usage Détaillés - Système**

#### **CU-S-001 : Traiter Question Simple**
- **Acteur** : Système
- **Préconditions** : Message utilisateur reçu
- **Déclencheur** : Mot-clé simple ("date", "lieu")
- **Scénario principal** :
  1. Analyse du message (1 mot)
  2. Recherche dans faq_data.json
  3. Score minimum = 1
  4. Correspondance trouvée
  5. Retour réponse JSON rapide

#### **CU-S-002 : Traiter Question Complexe**
- **Acteur** : Système
- **Préconditions** : Pas de correspondance JSON
- **Déclencheur** : Question culturelle détaillée
- **Scénario principal** :
  1. Échec recherche JSON
  2. Appel API Mistral AI
  3. Envoi contexte conversation
  4. Réception réponse intelligente
  5. Ajout signature Maroc Cultures

#### **CU-S-003 : Gestion d'Erreur**
- **Acteur** : Système
- **Préconditions** : Échec API Mistral
- **Déclencheur** : Timeout ou erreur réseau
- **Scénario principal** :
  1. Détection erreur Mistral
  2. Activation fallback prédéfini
  3. Recherche mot-clé culturel
  4. Retour réponse de secours
  5. Log de l'incident

---

## 🔄 **6. INTERACTIONS SYSTÈME**

### **6.1 Flux Principal de Conversation**
```
👤 Utilisateur
    │ Question
    ▼
🤖 Système Chatbot
    │ 1. Recherche JSON
    ├─ ✅ Trouvé → Réponse rapide
    │ 2. Appel Mistral AI
    ├─ ✅ Succès → Réponse intelligente  
    │ 3. Fallback prédéfini
    └─ ✅ Toujours → Réponse de secours
    │
    ▼ Réponse
👤 Utilisateur
```

### **6.2 Matrice d'Interactions**
| Acteur | Système | Action | Résultat |
|--------|---------|--------|----------|
| Visiteur | Web | Consulte | Information |
| Visiteur | Chatbot | Question | Réponse culturelle |
| Utilisateur | Auth | Login | Session |
| Utilisateur | Événements | Inscription | Confirmation |
| Admin | Dashboard | Gestion | Contrôle |
| Système | Mistral | API Call | Réponse IA |

---

## ✅ **7. VALIDATION DES CAS D'USAGE**

### **7.1 Tests de Validation**
- ✅ **CU-V-002** : Chatbot répond aux visiteurs
- ✅ **CU-U-001** : Inscription fonctionnelle
- ✅ **CU-U-002** : Connexion sécurisée
- ✅ **CU-A-001** : Dashboard accessible
- ✅ **CU-S-001** : Questions simples traitées
- ✅ **CU-S-002** : Questions complexes via Mistral

### **7.2 Couverture Fonctionnelle**
- **Chatbot** : 100% des scénarios couverts
- **Authentification** : 100% opérationnel
- **Événements** : 90% implémenté
- **Administration** : 85% fonctionnel

---

**🇲🇦 Maroc Cultures - Diagrammes de cas d'usage complets pour une architecture robuste ! ✨**
