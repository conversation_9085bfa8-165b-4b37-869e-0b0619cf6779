import mysql.connector
from mysql.connector import Error

print("🔍 Testing MySQL connection...")

try:
    # Test connection to MySQL server (without specifying database first)
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password=''
    )
    
    if connection.is_connected():
        print("✅ Connected to MySQL server!")
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"📊 MySQL Version: {version}")
        
        # Check if database exists
        cursor.execute("SHOW DATABASES LIKE 'chatbot_maroc_cultures'")
        db_exists = cursor.fetchone()
        
        if db_exists:
            print("✅ Database 'chatbot_maroc_cultures' exists!")
            
            # Connect to the specific database
            cursor.execute("USE chatbot_maroc_cultures")
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"🗄️ Current database: {current_db}")
            
            # Show tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 Number of tables: {len(tables)}")
            
            if tables:
                print("📝 Existing tables:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("ℹ️ No tables found (normal if migrations haven't been run)")
                
        else:
            print("❌ Database 'chatbot_maroc_cultures' does not exist!")
            print("💡 Creating database...")
            cursor.execute("CREATE DATABASE chatbot_maroc_cultures CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ Database created successfully!")
        
        cursor.close()
        connection.close()
        print("✅ MySQL connection test completed successfully!")
        
except Error as e:
    print(f"❌ MySQL Error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")

print("\n🎯 Next steps:")
print("1. Run Django migrations: python manage.py migrate")
print("2. Create superuser: python manage.py createsuperuser")
print("3. Start server: python manage.py runserver")
