import os
from pathlib import Path

# Configuration PyMySQL pour compatibilité avec Django
try:
    import pymysql
    pymysql.install_as_MySQLdb()
except ImportError:
    pass

# Tentative d'importation des packages optionnels
try:
    import dj_database_url
    from dotenv import load_dotenv
    # Charger les variables d'environnement depuis le fichier .env
    load_dotenv()
    SUPABASE_ENABLED = True
except ImportError:
    SUPABASE_ENABLED = False

# Supprimez ou commentez cette ligne problématique
# LANGUAGE_CODE=fr
# Gardez uniquement la ligne correcte ci-dessous
LANGUAGE_CODE = 'fr-fr'
TIME_ZONE = 'Europe/Paris'

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-your-secret-key-here'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []
STATIC_URL = '/static/'
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'chatbot_app',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'chatbot_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'chatbot_project.wsgi.application'

# Database
# Configuration de base de données avec support MySQL, PostgreSQL et SQLite

# Configuration par défaut (SQLite) pour le développement local
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Configuration MySQL - Force MySQL configuration
# Temporarily force MySQL configuration for testing
# DATABASES['default'] = {
#     'ENGINE': 'chatbot_project.mysql_backend',  # Use our custom backend
#     'NAME': 'chatbot_maroc_cultures',
#     'USER': 'root',
#     'PASSWORD': '',
#     'HOST': 'localhost',
#     'PORT': '3306',
#     'OPTIONS': {
#         'charset': 'utf8mb4',
#         'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#         'isolation_level': None,
#     },
#     'ATOMIC_REQUESTS': False,
# }

# Configuration MySQL si les variables d'environnement sont définies (backup)
# db_engine = os.getenv('DB_ENGINE')
# if db_engine == 'mysql':
#     DATABASES['default'] = {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': os.getenv('DB_NAME', 'chatbot_maroc_cultures'),
#         'USER': os.getenv('DB_USER', 'root'),
#         'PASSWORD': os.getenv('DB_PASSWORD', ''),
#         'HOST': os.getenv('DB_HOST', 'localhost'),
#         'PORT': os.getenv('DB_PORT', '3306'),
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#             'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#         },
#     }

# Utiliser DATABASE_URL si défini (pour Supabase PostgreSQL ou MySQL)
if SUPABASE_ENABLED:
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        DATABASES['default'] = dj_database_url.parse(database_url)

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
        'OPTIONS': {
            'user_attributes': ('username', 'email'),
            'max_similarity': 0.7,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,  # Réduit de 8 à 6 caractères
        }
    },
    # Commenté pour permettre des mots de passe courants
    # {
    #     'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    # },
    # Commenté pour permettre des mots de passe numériques
    # {
    #     'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    # },
]

# Internationalization
LANGUAGE_CODE = 'fr-fr'
TIME_ZONE = 'Europe/Paris'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# Supprimez les lignes 24-26 et gardez uniquement cette configuration
STATIC_URL = 'static/'
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'chatbot_app/static'),
]

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
