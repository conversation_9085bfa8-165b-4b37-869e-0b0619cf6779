#!/usr/bin/env python
"""
Test pour vérifier que l'effacement de l'historique fonctionne
"""
import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from django.test import Client
import json
import pymysql

def test_clear_history():
    """Test pour vérifier que l'effacement de l'historique fonctionne"""
    print("🗑️ Test de l'effacement de l'historique")
    print("=" * 50)
    
    # Créer un client de test Django
    client = Client()
    
    # Test 1: Créer quelques conversations d'abord
    print("\n📝 Étape 1: Création de conversations de test...")
    
    test_messages = ["bonjour", "comment ça va ?", "merci"]
    
    for i, message in enumerate(test_messages, 1):
        print(f"   Envoi du message {i}: '{message}'")
        response = client.post('/process_message/', {'message': message})
        if response.status_code == 200:
            print(f"   ✅ Message envoyé")
        else:
            print(f"   ❌ Erreur: {response.status_code}")
    
    # Test 2: Vérifier qu'il y a des données avant suppression
    print(f"\n🔍 Étape 2: Vérification des données avant suppression...")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count_before = cursor.fetchone()[0]
        print(f"   📊 Conversations avant: {conv_count_before}")
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count_before = cursor.fetchone()[0]
        print(f"   📊 Messages avant: {msg_count_before}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        return
    
    # Test 3: Tester l'effacement
    print(f"\n🗑️ Étape 3: Test de l'effacement...")
    
    try:
        response = client.post('/chat/history/clear/')
        
        if response.status_code == 302:  # Redirection après succès
            print(f"   ✅ Requête d'effacement envoyée (Code: {response.status_code})")
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return
    
    # Test 4: Vérifier que les données ont été supprimées
    print(f"\n🔍 Étape 4: Vérification après suppression...")
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='chatbot_maroc_cultures'
        )
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_conversation")
        conv_count_after = cursor.fetchone()[0]
        print(f"   📊 Conversations après: {conv_count_after}")
        
        cursor.execute("SELECT COUNT(*) FROM chatbot_app_message")
        msg_count_after = cursor.fetchone()[0]
        print(f"   📊 Messages après: {msg_count_after}")
        
        cursor.close()
        conn.close()
        
        # Vérifier si la suppression a fonctionné
        if conv_count_after < conv_count_before and msg_count_after < msg_count_before:
            print(f"   ✅ Suppression réussie !")
            print(f"   📉 Conversations supprimées: {conv_count_before - conv_count_after}")
            print(f"   📉 Messages supprimés: {msg_count_before - msg_count_after}")
        else:
            print(f"   ⚠️ La suppression n'a pas fonctionné comme attendu")
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
    
    # Test 5: Vérifier la page d'historique
    print(f"\n🌐 Étape 5: Vérification de la page d'historique...")
    
    try:
        response = client.get('/chat/history/')
        
        if response.status_code == 200:
            print(f"   ✅ Page d'historique accessible")
            
            content = response.content.decode('utf-8')
            if 'no-history' in content or 'Vous n\'avez pas encore' in content:
                print(f"   ✅ Page affiche correctement l'absence d'historique")
            else:
                print(f"   ⚠️ La page pourrait encore afficher des données")
                
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print(f"\n🎯 Test terminé !")
    print(f"   Vous pouvez vérifier manuellement à: http://127.0.0.1:8000/chat/history/")

if __name__ == "__main__":
    test_clear_history()
