import os
import sys
import django
from datetime import date, time

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def add_3_events():
    """Ajoute 3 nouveaux événements culturels marocains"""
    
    print("🎭 Ajout de 3 nouveaux événements culturels marocains...")
    print("=" * 60)
    
    # Événement 1: Festival Gnawa de Casablanca
    try:
        event1, created1 = Event.objects.get_or_create(
            title="Festival Gnawa de Casablanca",
            defaults={
                'description': "Découvrez la musique spirituelle Gnawa, patrimoine ancestral du Maroc. Concerts de maîtres musiciens, ateliers de percussion traditionnelle et cérémonies mystiques. Un voyage musical unique au cœur de la culture afro-marocaine.",
                'date_start': date(2025, 8, 10),
                'date_end': date(2025, 8, 12),
                'time': time(19, 0),
                'location': "Théâtre Mohammed V, Casablanca",
                'image_url': "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800",
                'registration_url': "https://festival-gnawa-casablanca.ma/inscription"
            }
        )
        if created1:
            print(f"✅ Événement créé: {event1.title}")
            print(f"   📅 Date: {event1.date_start} - {event1.date_end}")
            print(f"   🕐 Heure: {event1.time}")
            print(f"   📍 Lieu: {event1.location}")
        else:
            print(f"ℹ️ Événement existe déjà: {event1.title}")
        print("-" * 60)
    except Exception as e:
        print(f"❌ Erreur événement 1: {e}")

    # Événement 2: Exposition d'Art Berbère Contemporain
    try:
        event2, created2 = Event.objects.get_or_create(
            title="Exposition d'Art Berbère Contemporain",
            defaults={
                'description': "Une exposition exceptionnelle mettant en valeur l'art berbère moderne. Peintures, sculptures, bijoux traditionnels revisités par des artistes contemporains. Rencontres avec les artistes et ateliers de calligraphie tifinagh.",
                'date_start': date(2025, 9, 5),
                'date_end': date(2025, 9, 25),
                'time': time(10, 0),
                'location': "Galerie d'Art Moderne, Rabat",
                'image_url': "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800",
                'registration_url': "https://galerie-art-rabat.ma/expo-berbere"
            }
        )
        if created2:
            print(f"✅ Événement créé: {event2.title}")
            print(f"   📅 Date: {event2.date_start} - {event2.date_end}")
            print(f"   🕐 Heure: {event2.time}")
            print(f"   📍 Lieu: {event2.location}")
        else:
            print(f"ℹ️ Événement existe déjà: {event2.title}")
        print("-" * 60)
    except Exception as e:
        print(f"❌ Erreur événement 2: {e}")

    # Événement 3: Soirée Gastronomique Marocaine
    try:
        event3, created3 = Event.objects.get_or_create(
            title="Soirée Gastronomique Marocaine",
            defaults={
                'description': "Une soirée dégustation exceptionnelle avec les meilleurs chefs marocains. Menu traditionnel revisité : tajines, couscous royal, pâtisseries orientales. Spectacle de danse orientale et musique andalouse en accompagnement.",
                'date_start': date(2025, 10, 15),
                'date_end': date(2025, 10, 15),
                'time': time(18, 30),
                'location': "Restaurant La Mamounia, Marrakech",
                'image_url': "https://images.unsplash.com/photo-1544025162-d76694265947?w=800",
                'registration_url': "https://lamounia-marrakech.com/soiree-gastronomique"
            }
        )
        if created3:
            print(f"✅ Événement créé: {event3.title}")
            print(f"   📅 Date: {event3.date_start}")
            print(f"   🕐 Heure: {event3.time}")
            print(f"   📍 Lieu: {event3.location}")
        else:
            print(f"ℹ️ Événement existe déjà: {event3.title}")
        print("-" * 60)
    except Exception as e:
        print(f"❌ Erreur événement 3: {e}")

    # Résumé
    total_events = Event.objects.count()
    created_count = sum([created1, created2, created3])
    
    print(f"\n🎉 Résumé: {created_count} nouveaux événements ajoutés!")
    print(f"📊 Total d'événements dans la base: {total_events}")
    
    # Afficher tous les événements
    print(f"\n📋 Liste de tous les événements:")
    for event in Event.objects.all().order_by('date_start'):
        print(f"   • {event.title} - {event.date_start} - {event.location}")

if __name__ == "__main__":
    add_3_events()
