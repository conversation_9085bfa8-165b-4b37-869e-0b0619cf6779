<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Fond dégradé -->
  <defs>
    <linearGradient id="defaultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#C0392B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E74C3C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27AE60;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#F39C12;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  
  <!-- Fond -->
  <rect width="400" height="300" fill="url(#defaultGradient)"/>
  
  <!-- Cercle central -->
  <circle cx="200" cy="150" r="80" fill="url(#centerGradient)"/>
  
  <!-- Motifs géométriques marocains -->
  <g opacity="0.4" stroke="#fff" stroke-width="2" fill="none">
    <!-- Motifs dans les coins -->
    <g transform="translate(50,50)">
      <circle cx="0" cy="0" r="20"/>
      <polygon points="0,-15 10,-5 0,5 -10,-5"/>
    </g>
    
    <g transform="translate(350,50)">
      <circle cx="0" cy="0" r="15"/>
      <polygon points="0,-10 7,-3 0,3 -7,-3"/>
    </g>
    
    <g transform="translate(50,250)">
      <circle cx="0" cy="0" r="15"/>
      <polygon points="0,-10 7,-3 0,3 -7,-3"/>
    </g>
    
    <g transform="translate(350,250)">
      <circle cx="0" cy="0" r="20"/>
      <polygon points="0,-15 10,-5 0,5 -10,-5"/>
    </g>
  </g>
  
  <!-- Étoile centrale -->
  <g transform="translate(200,150)">
    <polygon points="0,-25 7,-7 25,-7 12,3 20,20 0,10 -20,20 -12,3 -25,-7 -7,-7" 
             fill="#fff" opacity="0.9"/>
  </g>
  
  <!-- Texte -->
  <text x="200" y="220" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Événement Culturel
  </text>
  <text x="200" y="245" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="14">
    Maroc Cultures
  </text>
  
  <!-- Ornements -->
  <g opacity="0.7">
    <rect x="100" y="270" width="200" height="2" fill="#F1C40F"/>
    <circle cx="110" cy="271" r="3" fill="#F1C40F"/>
    <circle cx="200" cy="271" r="4" fill="#F1C40F"/>
    <circle cx="290" cy="271" r="3" fill="#F1C40F"/>
  </g>
</svg>
