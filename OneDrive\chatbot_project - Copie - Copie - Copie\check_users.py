import pymysql

try:
    # Connexion à MySQL
    connection = pymysql.connect(
        host='127.0.0.1',
        user='root',
        password='',
        database='maroc_cultures_db',
        port=3306
    )
    
    print("✅ Connexion réussie à MySQL!")
    
    # Création d'un curseur
    cursor = connection.cursor()
    
    # Vérification des utilisateurs dans auth_user
    print("\n👥 Utilisateurs dans auth_user :")
    cursor.execute("SELECT id, username, email, is_superuser, is_staff, is_active FROM auth_user")
    users = cursor.fetchall()
    if users:
        for user in users:
            user_id, username, email, is_superuser, is_staff, is_active = user
            print(f"- ID: {user_id}, Nom d'utilisateur: {username}, Email: {email}")
            print(f"  Statut: {'Superutilisateur' if is_superuser else 'Utilisateur normal'}, {'Staff' if is_staff else 'Non-staff'}, {'Actif' if is_active else 'Inactif'}")
    else:
        print("Aucun utilisateur trouvé dans auth_user")
    
    # Vérification des utilisateurs dans chatbot_app_user
    print("\n👥 Utilisateurs dans chatbot_app_user :")
    cursor.execute("SELECT id, username, email, first_name, last_name, is_active FROM chatbot_app_user")
    users = cursor.fetchall()
    if users:
        for user in users:
            user_id, username, email, first_name, last_name, is_active = user
            print(f"- ID: {user_id}, Nom d'utilisateur: {username}, Email: {email}")
            print(f"  Nom: {first_name} {last_name}, {'Actif' if is_active else 'Inactif'}")
    else:
        print("Aucun utilisateur trouvé dans chatbot_app_user")
    
    # Fermeture de la connexion
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ Erreur: {e}")
