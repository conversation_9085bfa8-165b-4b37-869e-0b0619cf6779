{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}
{% if event_form.instance.id %}
Modifier l'événement - Maroc Cultures
{% else %}
Ajouter un événement - Maroc Cultures
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Event Form Styles */
    body {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .form-container {
        padding: 10px 0;
    }

    .form-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 6px 10px;
        border-radius: 4px;
        margin-bottom: 8px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }

    .form-header h1 {
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 0;
    }

    .form-header p {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 0.7rem;
    }

    .form-header .btn-outline-light {
        border: 1px solid rgba(255, 255, 255, 0.5);
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .form-header .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .form-card {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
        margin-bottom: 10px;
        overflow: hidden;
    }

    .form-card-header {
        background-color: #f8f9fa;
        padding: 6px 10px;
        border-bottom: 1px solid #eee;
    }

    .form-card-header h2 {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #1abc9c;
    }

    .form-card-body {
        padding: 10px;
    }

    .form-group {
        margin-bottom: 8px;
    }

    .form-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: #555;
        margin-bottom: 2px;
    }

    .form-control {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 3px;
        border: 1px solid #ddd;
        height: calc(1.5em + 0.5rem + 2px);
    }

    textarea.form-control {
        height: auto;
    }

    .form-control:focus {
        border-color: #1abc9c;
        box-shadow: 0 0 0 0.1rem rgba(26, 188, 156, 0.15);
    }

    .form-error {
        color: #e74c3c;
        font-size: 0.65rem;
        margin-top: 2px;
    }

    .form-help {
        color: #6c757d;
        font-size: 0.65rem;
        margin-top: 1px;
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }

    .btn-submit {
        background-color: #1abc9c;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 3px;
        border: none;
    }

    .btn-submit:hover {
        background-color: #16a085;
    }

    .btn-cancel {
        background-color: #f8f9fa;
        color: #555;
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 3px;
        border: 1px solid #ddd;
        text-decoration: none;
    }

    .btn-cancel:hover {
        background-color: #e9ecef;
    }

    .form-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -3px;
        margin-left: -3px;
    }

    .form-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding-right: 3px;
        padding-left: 3px;
    }

    @media (max-width: 768px) {
        .form-col {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container form-container">
    <div class="form-header d-flex justify-content-between align-items-center">
        <div>
            {% if event_form.instance.id %}
            <h1><i class="fas fa-calendar-edit me-1"></i>Modifier l'événement</h1>
            <p>Modifiez les informations de l'événement</p>
            {% else %}
            <h1><i class="fas fa-calendar-plus me-1"></i>Ajouter un événement</h1>
            <p>Créez un nouvel événement dans le système</p>
            {% endif %}
        </div>
        <div>
            <a href="{% url 'admin_events_list' %}" class="btn btn-sm btn-outline-light">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-7">
            <div class="form-card">
                <div class="form-card-header">
                    {% if event_form.instance.id %}
                    <h2><i class="fas fa-calendar-edit me-1"></i>Informations de l'événement</h2>
                    {% else %}
                    <h2><i class="fas fa-calendar-plus me-1"></i>Informations de l'événement</h2>
                    {% endif %}
                </div>
                <div class="form-card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger py-2 px-3">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="form-group">
                            <label for="id_title" class="form-label">Titre</label>
                            <input type="text" name="title" id="id_title" class="form-control {% if form.title.errors %}is-invalid{% endif %}" value="{{ event_form.title.value|default:'' }}" required>
                            {% if form.title.errors %}
                            <div class="form-error">{{ form.title.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="id_description" class="form-label">Description</label>
                            <textarea name="description" id="id_description" rows="3" class="form-control {% if form.description.errors %}is-invalid{% endif %}" required>{{ event_form.description.value|default:'' }}</textarea>
                            {% if form.description.errors %}
                            <div class="form-error">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="id_date_start" class="form-label">Date de début</label>
                                    <input type="date" name="date_start" id="id_date_start" class="form-control {% if form.date_start.errors %}is-invalid{% endif %}" value="{{ event_form.date_start.value|date:'Y-m-d'|default:'' }}" required>
                                    {% if form.date_start.errors %}
                                    <div class="form-error">{{ form.date_start.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="id_date_end" class="form-label">Date de fin (optionnel)</label>
                                    <input type="date" name="date_end" id="id_date_end" class="form-control {% if form.date_end.errors %}is-invalid{% endif %}" value="{{ event_form.date_end.value|date:'Y-m-d'|default:'' }}">
                                    {% if form.date_end.errors %}
                                    <div class="form-error">{{ form.date_end.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="id_time" class="form-label">Heure (optionnel)</label>
                                    <input type="time" name="time" id="id_time" class="form-control {% if form.time.errors %}is-invalid{% endif %}" value="{{ event_form.time.value|time:'H:i'|default:'' }}">
                                    {% if form.time.errors %}
                                    <div class="form-error">{{ form.time.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="id_location" class="form-label">Lieu</label>
                                    <input type="text" name="location" id="id_location" class="form-control {% if form.location.errors %}is-invalid{% endif %}" value="{{ event_form.location.value|default:'' }}" required>
                                    {% if form.location.errors %}
                                    <div class="form-error">{{ form.location.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="id_image_url" class="form-label">URL de l'image (optionnel)</label>
                            <input type="url" name="image_url" id="id_image_url" class="form-control {% if form.image_url.errors %}is-invalid{% endif %}" value="{{ event_form.image_url.value|default:'' }}">
                            {% if form.image_url.errors %}
                            <div class="form-error">{{ form.image_url.errors.0 }}</div>
                            {% endif %}
                            <div class="form-help">URL d'une image pour illustrer l'événement.</div>
                        </div>

                        <div class="form-group">
                            <label for="id_registration_url" class="form-label">URL d'inscription (optionnel)</label>
                            <input type="url" name="registration_url" id="id_registration_url" class="form-control {% if form.registration_url.errors %}is-invalid{% endif %}" value="{{ event_form.registration_url.value|default:'' }}">
                            {% if form.registration_url.errors %}
                            <div class="form-error">{{ form.registration_url.errors.0 }}</div>
                            {% endif %}
                            <div class="form-help">URL pour l'inscription à l'événement.</div>
                        </div>

                        <div class="form-actions">
                            <a href="{% url 'admin_events_list' %}" class="btn-cancel">Annuler</a>
                            <button type="submit" class="btn-submit">
                                {% if event_form.instance.id %}
                                <i class="fas fa-save me-1"></i> Enregistrer
                                {% else %}
                                <i class="fas fa-plus me-1"></i> Ajouter
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
