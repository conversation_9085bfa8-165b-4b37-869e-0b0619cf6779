{% extends 'chatbot_app/base.html' %}
{% load static %}

{% block title %}Historique des conversations - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Chat History Styles - Interface très compacte */
    .history-container {
        padding: 15px 0;
        min-height: calc(100vh - 150px);
    }

    .history-header {
        background: linear-gradient(to right, rgba(192, 57, 43, 0.9), rgba(39, 174, 96, 0.8));
        color: white;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .history-header h1 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .history-header p {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 0.8rem;
    }

    .conversation-date {
        background-color: #f8f9fa;
        padding: 6px 10px;
        border-radius: 4px;
        margin: 10px 0 8px;
        font-weight: 600;
        color: #1abc9c;
        border-left: 2px solid #1abc9c;
        font-size: 0.8rem;
    }

    .message-container {
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;
    }

    .message {
        max-width: 70%;
        padding: 8px 12px;
        border-radius: 12px;
        margin-bottom: 4px;
        position: relative;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        font-size: 0.85rem;
        line-height: 1.3;
    }

    .message-time {
        font-size: 0.7rem;
        color: #999;
        margin-top: 3px;
        display: block;
    }

    .user-message-container {
        align-items: flex-end;
    }

    .bot-message-container {
        align-items: flex-start;
    }

    .user-message {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        border-bottom-right-radius: 5px;
        align-self: flex-end;
    }

    .bot-message {
        background-color: #f0f2f5;
        color: #333;
        border-bottom-left-radius: 5px;
        align-self: flex-start;
    }

    .no-history {
        text-align: center;
        padding: 20px 0;
        color: #777;
    }

    .no-history i {
        font-size: 2rem;
        color: #ddd;
        margin-bottom: 8px;
        display: block;
    }

    .no-history p {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }



    .back-to-chat {
        display: inline-block;
        margin-top: 10px;
        color: #27ae60;
        text-decoration: none;
        font-weight: 500;
        font-size: 0.85rem;
    }

    .back-to-chat:hover {
        text-decoration: underline;
    }



    /* Responsive adjustments */
    @media (max-width: 768px) {
        .message {
            max-width: 90%;
        }

        .modal-content {
            margin: 20px;
            width: calc(100% - 40px);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container history-container">
    <div class="history-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-history me-2"></i>Historique des conversations</h1>
                <p>Retrouvez toutes vos conversations avec notre assistant</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'chat' %}" class="btn btn-success btn-sm">
                    <i class="fas fa-comments me-1"></i> Nouveau Chat
                </a>
            </div>
        </div>
    </div>

    {% if conversations_data %}
        {% for date, conversations in conversations_data.items %}
            <div class="conversation-date">
                <i class="far fa-calendar-alt me-2"></i> {{ date }}
            </div>

            {% for conversation_data in conversations %}
                {% for message in conversation_data.messages %}
                    <div class="message-container {% if message.role == 'user' %}user-message-container{% else %}bot-message-container{% endif %}">
                        <div class="message {% if message.role == 'user' %}user-message{% else %}bot-message{% endif %}">
                            {{ message.content }}
                            <span class="message-time">{{ message.time|time:"H:i" }}</span>
                        </div>
                    </div>
                {% endfor %}
            {% endfor %}
        {% endfor %}
    {% else %}
        <div class="no-history">
            <i class="fas fa-comment-slash"></i>
            {% if message %}
                <p>{{ message }}</p>
            {% else %}
                <p>Vous n'avez pas encore de conversations enregistrées.</p>
                <p>Commencez à discuter avec notre assistant pour voir votre historique ici.</p>
            {% endif %}

            <a href="{% url 'chat' %}" class="back-to-chat">
                <i class="fas fa-arrow-left me-1"></i> Retour au chat
            </a>
        </div>
    {% endif %}
</div>


{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Initialisation de la page historique...');
        console.log('✅ Page historique chargée avec succès');
    });
</script>
{% endblock %}
