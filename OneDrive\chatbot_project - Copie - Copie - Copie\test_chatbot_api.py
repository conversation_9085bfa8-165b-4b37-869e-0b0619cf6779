#!/usr/bin/env python
"""
Script pour tester l'API du chatbot
"""
import requests
import json

def test_chatbot_api():
    """Test l'API du chatbot avec différents messages"""
    
    # URL de l'API du chatbot
    url = "http://127.0.0.1:8000/process_message/"
    
    # Messages de test
    test_messages = [
        "bonjour",
        "salut",
        "événements",
        "contact",
        "merci",
        "au revoir"
    ]
    
    print("🤖 Test de l'API du chatbot")
    print("=" * 50)
    
    # Créer une session pour maintenir les cookies
    session = requests.Session()
    
    # D'abord, obtenir le token CSRF
    try:
        csrf_response = session.get("http://127.0.0.1:8000/chat/")
        if csrf_response.status_code == 200:
            print("✅ Page de chat accessible")
            
            # Extraire le token CSRF de la réponse
            csrf_token = None
            if 'csrftoken' in session.cookies:
                csrf_token = session.cookies['csrftoken']
            
            print(f"🔑 Token CSRF: {csrf_token[:10]}..." if csrf_token else "❌ Pas de token CSRF")
            
            # Tester chaque message
            for i, message in enumerate(test_messages, 1):
                print(f"\n📝 Test {i}: '{message}'")
                
                # Données à envoyer
                data = {
                    'message': message,
                    'csrfmiddlewaretoken': csrf_token
                }
                
                # Headers
                headers = {
                    'X-CSRFToken': csrf_token,
                    'Referer': 'http://127.0.0.1:8000/chat/'
                }
                
                try:
                    # Envoyer la requête POST
                    response = session.post(url, data=data, headers=headers)
                    
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            bot_response = result.get('response', 'Pas de réponse')
                            conversation_id = result.get('conversation_id', 'N/A')
                            
                            print(f"   ✅ Réponse: {bot_response[:100]}...")
                            print(f"   📋 Conversation ID: {conversation_id}")
                        except json.JSONDecodeError:
                            print(f"   ❌ Réponse non-JSON: {response.text[:100]}...")
                    else:
                        print(f"   ❌ Erreur HTTP: {response.text[:100]}...")
                        
                except requests.exceptions.RequestException as e:
                    print(f"   ❌ Erreur de requête: {e}")
        else:
            print(f"❌ Impossible d'accéder à la page de chat: {csrf_response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
    
    print("\n✅ Tests terminés")

if __name__ == "__main__":
    test_chatbot_api()
