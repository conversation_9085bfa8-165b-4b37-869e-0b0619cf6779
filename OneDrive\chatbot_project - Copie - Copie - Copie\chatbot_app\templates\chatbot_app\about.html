{% extends 'chatbot_app/base.html' %}
{% load static %}
{% block title %}À propos - Maroc Cultures{% endblock %}

{% block extra_css %}
<style>
    /* Header Section */
    .about-header {
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.9) 0%, rgba(192, 57, 43, 0.8) 50%, rgba(39, 174, 96, 0.7) 100%);
        padding: 50px 0; /* Réduit de 80px à 50px */
        margin-bottom: 40px; /* Réduit de 60px à 40px */
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .about-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .about-title {
        font-size: 2.5rem; /* Réduit de 3rem à 2.5rem */
        font-weight: 800;
        margin-bottom: 15px; /* Réduit de 20px à 15px */
        position: relative;
        display: inline-block;
    }

    .about-title::after {
        content: '';
        position: absolute;
        width: 70px; /* Réduit de 80px à 70px */
        height: 3px; /* Réduit de 4px à 3px */
        background: white;
        bottom: -8px; /* Réduit de -10px à -8px */
        left: 50%;
        transform: translateX(-50%);
    }

    .about-subtitle {
        font-size: 1.1rem; /* Réduit de 1.2rem à 1.1rem */
        max-width: 750px; /* Réduit de 800px à 750px */
        margin: 0 auto;
        line-height: 1.6; /* Réduit de 1.8 à 1.6 */
    }

    /* Card Styles */
    .about-card {
        background-color: white;
        border-radius: 12px; /* Réduit de 15px à 12px */
        overflow: hidden;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* Réduit l'ombre */
        transition: all 0.3s ease;
        height: 100%;
        border: none;
        position: relative;
    }

    .about-card:hover {
        transform: translateY(-5px); /* Réduit de -10px à -5px */
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15); /* Réduit l'ombre */
    }

    .about-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px; /* Réduit de 5px à 4px */
        background: linear-gradient(90deg, #c0392b, #27ae60);
    }

    .card-icon {
        width: 60px; /* Réduit de 70px à 60px */
        height: 60px; /* Réduit de 70px à 60px */
        background: linear-gradient(135deg, #c0392b, #27ae60);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px; /* Réduit de 20px à 15px */
        color: white;
        font-size: 24px; /* Réduit de 30px à 24px */
    }

    .section-title {
        font-size: 2rem; /* Réduit de 2.2rem à 2rem */
        font-weight: 700;
        margin-bottom: 30px; /* Réduit de 40px à 30px */
        color: #c0392b;
        text-align: center;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        width: 50px; /* Réduit de 60px à 50px */
        height: 3px;
        background: linear-gradient(to right, #c0392b, #27ae60);
        bottom: -12px; /* Réduit de -15px à -12px */
        left: 50%;
        transform: translateX(-50%);
    }

    .card-title {
        font-size: 1.6rem; /* Réduit de 1.8rem à 1.6rem */
        font-weight: 700;
        margin-bottom: 15px; /* Réduit de 20px à 15px */
        color: #c0392b;
        text-align: center;
    }

    .card-text {
        color: #555;
        line-height: 1.6; /* Réduit de 1.8 à 1.6 */
        font-size: 1rem; /* Réduit de 1.1rem à 1rem */
    }

    /* Values Section */
    .value-item {
        padding: 20px; /* Réduit de 30px à 20px */
        text-align: center;
        transition: all 0.3s ease;
    }

    .value-item:hover {
        transform: translateY(-3px); /* Réduit de -5px à -3px */
    }

    .value-icon {
        width: 70px; /* Réduit de 80px à 70px */
        height: 70px; /* Réduit de 80px à 70px */
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.1), rgba(39, 174, 96, 0.1));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px; /* Réduit de 20px à 15px */
        color: #27ae60;
        font-size: 24px; /* Réduit de 30px à 24px */
    }

    .value-title {
        font-size: 1.2rem; /* Réduit de 1.3rem à 1.2rem */
        font-weight: 600;
        margin-bottom: 10px; /* Réduit de 15px à 10px */
        color: #c0392b;
    }

    .value-text {
        color: #555;
        line-height: 1.5; /* Réduit de 1.7 à 1.5 */
        font-size: 0.95rem; /* Ajout d'une taille de police légèrement réduite */
    }

    /* Team Section */
    .team-section {
        padding: 40px 0; /* Réduit de 60px à 40px */
        background-color: #f8f9fa;
        margin-top: 40px; /* Réduit de 60px à 40px */
        border-radius: 12px; /* Réduit de 15px à 12px */
    }

    .moroccan-pattern {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100px; /* Réduit de 120px à 100px */
        height: 100px; /* Réduit de 120px à 100px */
        opacity: 0.05;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2l-2 2h-2z'/%3E%3C/g%3E%3C/svg%3E");
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="about-header">
    <div class="container">
        <h1 class="about-title">À propos de Maroc Cultures</h1>
        <p class="about-subtitle">
            Maroc Cultures est une organisation dédiée à la promotion de la culture marocaine et à la valorisation de la communauté marocaine au Canada. Elle agit comme un acteur central de la concertation entre les différents organismes et institutions œuvrant pour le développement et l'intégration des Marocains dans la société canadienne.
        </p>
    </div>
</section>

<div class="container">
    <!-- Mission & Vision Section -->
    <div class="row mb-4"> <!-- Réduit de mb-5 à mb-4 -->
        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="about-card">
                <div class="card-body p-4"> <!-- Réduit de p-5 à p-4 -->
                    <div class="card-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 class="card-title">Notre Mission</h3>
                    <p class="card-text">
                        Promouvoir la richesse et la diversité de la culture marocaine au Canada à travers des événements culturels, des expositions, des conférences et des activités communautaires.
                    </p>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>

        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="about-card">
                <div class="card-body p-4"> <!-- Réduit de p-5 à p-4 -->
                    <div class="card-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="card-title">Notre Vision</h3>
                    <p class="card-text">
                        Créer des ponts entre les cultures marocaine et canadienne, favoriser le dialogue interculturel et contribuer à l'enrichissement mutuel des communautés.
                    </p>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>
    </div>

    <!-- Values Section -->
    <h2 class="section-title">Nos Valeurs</h2>
    <div class="row mb-4"> <!-- Réduit de mb-5 à mb-4 -->
        <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="value-item">
                <div class="value-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h4 class="value-title">Solidarité</h4>
                <p class="value-text">
                    Soutenir les membres de la communauté marocaine dans leur intégration et leur développement au sein de la société canadienne.
                </p>
            </div>
        </div>

        <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="value-item">
                <div class="value-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h4 class="value-title">Diversité</h4>
                <p class="value-text">
                    Célébrer la richesse culturelle du Maroc et promouvoir la diversité au sein de la société canadienne multiculturelle.
                </p>
            </div>
        </div>

        <div class="col-md-4 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="value-item">
                <div class="value-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h4 class="value-title">Inclusion</h4>
                <p class="value-text">
                    Favoriser la participation de tous et créer des espaces d'échange ouverts et accessibles pour tous les membres de la communauté.
                </p>
            </div>
        </div>
    </div>

    <!-- History Section -->
    <div class="row mb-4"> <!-- Réduit de mb-5 à mb-4 -->
        <div class="col-12">
            <div class="about-card">
                <div class="card-body p-4"> <!-- Réduit de p-5 à p-4 -->
                    <h3 class="card-title">Notre Histoire</h3>
                    <p class="card-text">
                        Fondée en 2020, Maroc Cultures est née de la volonté de créer un espace dédié à la culture marocaine au Canada. Notre organisation a commencé avec un petit groupe de passionnés et s'est rapidement développée pour devenir un acteur incontournable de la scène culturelle marocaine au Canada.
                    </p>
                    <p class="card-text">
                        Aujourd'hui, nous sommes fiers de rassembler une communauté dynamique et engagée, et de contribuer activement au rayonnement de la culture marocaine dans toute sa diversité.
                    </p>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}