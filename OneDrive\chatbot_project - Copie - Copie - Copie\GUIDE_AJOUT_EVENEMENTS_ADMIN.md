# 👨‍💼 GUIDE AJOUT ÉVÉNEMENTS VIA INTERFACE ADMIN

## 🎯 **ACCÈS À L'INTERFACE D'ADMINISTRATION**

### **🔗 URLs d'Accès**
- **Interface Admin Custom** : http://127.0.0.1:8000/admin-maroc-cultures/dashboard/
- **Interface Django Admin** : http://127.0.0.1:8000/admin/

### **🔐 Connexion**
- **Utilisateur** : admin (ou votre compte admin)
- **Mot de passe** : Votre mot de passe admin

---

## 📝 **MÉTHODE 1 : AJOUT AUTOMATIQUE (RECOMMANDÉ)**

### **🚀 Script Automatique**
1. **Double-cliquer** sur `ajouter_evenements.bat`
2. **Attendre** l'exécution du script
3. **Vérifier** les résultats affichés
4. **Consulter** les événements sur le site

### **✅ Avantages**
- ✅ **Rapide** : Tous les événements en une fois
- ✅ **Complet** : 8 événements culturels 2025
- ✅ **Formaté** : Descriptions avec émojis et structure
- ✅ **Cohérent** : Données validées et testées

---

## 📋 **MÉTHODE 2 : AJOUT MANUEL VIA INTERFACE**

### **🎭 Étapes d'Ajout Manuel**

#### **1. Accéder à la Gestion des Événements**
```
http://127.0.0.1:8000/admin/ → Events → Add Event
```

#### **2. Remplir le Formulaire**

##### **📝 Champs Obligatoires**
- **Title** : Titre de l'événement
- **Description** : Description complète (copier depuis EXEMPLES_EVENEMENTS_2025.md)
- **Date start** : Date de début (format : YYYY-MM-DD)
- **Location** : Lieu de l'événement

##### **📅 Champs Optionnels**
- **Date end** : Date de fin (si événement sur plusieurs jours)
- **Time** : Heure de début (format : HH:MM)
- **Image url** : URL de l'image (laisser vide si pas d'image)
- **Registration url** : URL d'inscription

#### **3. Exemple Concret - Festival Mawazine 2025**

```
Title: Festival Mawazine 2025 - Rythmes du Monde

Description: 
Le plus grand festival musical d'Afrique revient pour sa 19ème édition ! 
Mawazine 2025 promet une programmation exceptionnelle avec des artistes 
internationaux de renom et les plus grandes stars de la musique marocaine. 
Plus de 2 millions de spectateurs attendus sur 7 scènes à travers Rabat.

🎵 Programmation :
- Scène Bouregreg : Artistes internationaux
- Scène OLM Souissi : Stars arabes et marocaines  
- Scène Nahda : Musique du monde
- Scène Chellah : Patrimoine musical marocain
- Scènes Salé : Découvertes et jeunes talents

🎫 Entrée gratuite sur la plupart des scènes
💫 Concerts payants pour les têtes d'affiche internationales

Date start: 2025-05-23
Date end: 2025-05-31
Time: 20:00
Location: Rabat - Multiples scènes (Bouregreg, OLM Souissi, Nahda, Chellah)
Image url: https://example.com/mawazine2025.jpg
Registration url: https://www.festivalmawazine.ma/inscription
```

#### **4. Sauvegarder**
- **Cliquer** sur "Save" ou "Enregistrer"
- **Vérifier** que l'événement apparaît dans la liste
- **Répéter** pour les autres événements

---

## 🎪 **LISTE DES 8 ÉVÉNEMENTS À AJOUTER**

### **🎵 Événements Musicaux**
1. **Festival Mawazine 2025** (Mai 23-31)
2. **Génération Mawazine 2025** (Février-Mai)
3. **Festival Gnawa de Rabat 2025** (Juillet 18-20)
4. **Festival de Musique Andalouse 2025** (Octobre 12-15)

### **🎭 Événements Culturels**
5. **Festival du Théâtre des Cultures 2025** (Mars 15-25)
6. **Exposition Artisanat Royal Marocain** (Avril-Juin)
7. **Nuits Ramadan Culturelles 2025** (Mars)
8. **Journées du Patrimoine Marocain 2025** (Septembre 15-17)

---

## 📊 **FORMATS DE DONNÉES**

### **📅 Dates**
```
Format : YYYY-MM-DD
Exemples :
- 2025-05-23 (23 mai 2025)
- 2025-03-15 (15 mars 2025)
- 2025-12-31 (31 décembre 2025)
```

### **🕐 Heures**
```
Format : HH:MM (24h)
Exemples :
- 20:00 (8h du soir)
- 14:30 (2h30 de l'après-midi)
- 09:00 (9h du matin)
```

### **🌐 URLs**
```
Format : https://www.exemple.com/page
Exemples :
- https://www.festivalmawazine.ma/inscription
- https://www.theatre-cultures.ma/billetterie
- Laisser vide si pas d'URL
```

---

## ✅ **VÉRIFICATION APRÈS AJOUT**

### **🌐 Vérifier sur le Site**
1. **Aller sur** : http://127.0.0.1:8000/home/
2. **Faire défiler** jusqu'à "Événements à Venir"
3. **Vérifier** que les événements apparaissent
4. **Tester** les liens et informations

### **👨‍💼 Vérifier dans l'Admin**
1. **Retourner** sur l'interface admin
2. **Consulter** la liste des événements
3. **Modifier** si nécessaire
4. **Supprimer** les doublons éventuels

---

## 🛠️ **DÉPANNAGE**

### **❌ Problèmes Courants**

#### **"Erreur de format de date"**
- **Solution** : Utiliser le format YYYY-MM-DD
- **Exemple** : 2025-05-23 au lieu de 23/05/2025

#### **"Champ obligatoire manquant"**
- **Solution** : Remplir Title, Description, Date start, Location
- **Vérifier** que tous les champs requis sont complétés

#### **"Événement non visible sur le site"**
- **Solution** : Vérifier que l'événement est sauvegardé
- **Actualiser** la page du site (F5)
- **Vérifier** les dates (événements futurs uniquement)

### **🔧 Solutions Techniques**

#### **Interface Admin Inaccessible**
```bash
# Créer un superutilisateur
python manage.py createsuperuser

# Redémarrer le serveur
python manage.py runserver
```

#### **Événements Non Affichés**
```python
# Vérifier en console Django
python manage.py shell
>>> from chatbot_app.models import Event
>>> Event.objects.all()
>>> Event.objects.count()
```

---

## 📈 **BONNES PRATIQUES**

### **📝 Rédaction des Descriptions**
- **Utiliser des émojis** pour rendre attractif
- **Structurer** avec des sections claires
- **Inclure** les informations pratiques
- **Mentionner** les partenaires prestigieux

### **📅 Planification des Dates**
- **Vérifier** les dates réelles des festivals
- **Éviter** les conflits de calendrier
- **Prévoir** des événements étalés sur l'année

### **🎨 Présentation**
- **Titres accrocheurs** avec année
- **Descriptions complètes** mais lisibles
- **Lieux précis** et reconnaissables
- **URLs valides** ou laisser vide

---

## 🎯 **RÉSULTAT ATTENDU**

Après ajout des 8 événements, votre site aura :

### **🏠 Page d'Accueil**
- **Section "Événements à Venir"** remplie
- **Événements attractifs** avec descriptions riches
- **Calendrier culturel** complet pour 2025

### **👨‍💼 Interface Admin**
- **Gestion complète** des événements
- **Modification facile** des informations
- **Ajout simple** de nouveaux événements

### **🎭 Expérience Utilisateur**
- **Contenu riche** et informatif
- **Événements authentiques** marocains
- **Informations pratiques** complètes

---

**🇲🇦 Maroc Cultures - Interface d'administration enrichie avec des événements culturels authentiques ! ✨**

**Choisissez la méthode qui vous convient : automatique pour la rapidité, manuelle pour le contrôle total !** 🎭📅
