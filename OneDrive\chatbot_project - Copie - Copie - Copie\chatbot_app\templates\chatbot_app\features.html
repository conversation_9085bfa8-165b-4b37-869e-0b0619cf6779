{% extends 'chatbot_app/base.html' %}
{% load static %}
{% block title %}Fonctionnalités - MarocCultures{% endblock %}

{% block extra_css %}
<style>
    .features-header {
        background: linear-gradient(135deg, rgba(192, 57, 43, 0.9) 0%, rgba(192, 57, 43, 0.8) 50%, rgba(39, 174, 96, 0.7) 100%);
        padding: 35px 0; /* Réduit de 60px à 35px */
        margin-bottom: 30px; /* Réduit de 50px à 30px */
        color: white;
        text-align: center;
        border-radius: 0 0 50% 50% / 15px; /* Réduit de 20px à 15px */
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); /* Réduit l'ombre */
    }

    .features-title {
        font-size: 2.2rem; /* Réduit de 2.8rem à 2.2rem */
        font-weight: 800;
        margin-bottom: 10px; /* Réduit de 15px à 10px */
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .features-subtitle {
        font-size: 1rem; /* Réduit de 1.2rem à 1rem */
        max-width: 650px; /* Réduit de 700px à 650px */
        margin: 0 auto;
        opacity: 0.9;
    }

    .feature-card {
        background-color: white;
        border-radius: 12px; /* Réduit de 15px à 12px */
        overflow: hidden;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* Réduit l'ombre */
        transition: all 0.3s ease;
        height: 100%;
        border: none;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px); /* Réduit de -10px à -5px */
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15); /* Réduit l'ombre */
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px; /* Réduit de 5px à 4px */
        background: linear-gradient(90deg, #c0392b, #27ae60);
    }

    .feature-icon {
        width: 60px; /* Réduit de 70px à 60px */
        height: 60px; /* Réduit de 70px à 60px */
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px; /* Réduit de 20px à 15px */
        font-size: 1.8rem; /* Réduit de 2rem à 1.8rem */
    }

    .feature-title {
        font-size: 1.3rem; /* Réduit de 1.5rem à 1.3rem */
        font-weight: 700;
        margin-bottom: 10px; /* Réduit de 15px à 10px */
        color: #c0392b;
    }

    .feature-text {
        color: #555;
        line-height: 1.6; /* Réduit de 1.7 à 1.6 */
    }

    .feature-list {
        padding-left: 15px; /* Réduit de 20px à 15px */
        margin-top: 10px; /* Réduit de 15px à 10px */
    }

    .feature-list li {
        margin-bottom: 8px; /* Réduit de 10px à 8px */
        position: relative;
        padding-left: 5px;
        font-size: 0.95rem; /* Ajout d'une taille de police légèrement réduite */
    }

    .feature-list li::before {
        content: "•";
        color: #27ae60;
        font-weight: bold;
        display: inline-block;
        width: 1em;
        margin-left: -1em;
    }

    .feature-card-body {
        padding: 20px; /* Réduit de 30px à 20px */
    }

    .moroccan-pattern {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100px; /* Réduit de 120px à 100px */
        height: 100px; /* Réduit de 120px à 100px */
        opacity: 0.05;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2l-2 2h-2z'/%3E%3C/g%3E%3C/svg%3E");
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="features-header">
    <div class="container">
        <h1 class="features-title">Fonctionnalités de MarocCultures</h1>
        <p class="features-subtitle">Découvrez tous les services que nous proposons pour faciliter votre expérience culturelle marocaine au Canada</p>
    </div>
</section>

<!-- Features Section -->
<div class="container py-3"> <!-- Réduit de py-4 à py-3 -->
    <div class="row g-3"> <!-- Ajout de g-3 pour réduire l'espacement entre les cartes -->
        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="feature-card">
                <div class="feature-card-body">
                    <div class="feature-icon">
                        <i class="fas fa-robot" style="color: #c0392b;"></i>
                    </div>
                    <h3 class="feature-title">Chatbot Intelligent</h3>
                    <p class="feature-text">Notre site intègre un chatbot intelligent pour répondre à vos questions concernant l'intégration, les événements culturels ou les démarches administratives au Canada.</p>
                    <a href="javascript:void(0);" class="btn btn-sm" style="background-color: #27ae60; color: white;" id="open-chat-btn">
                        <i class="fas fa-comments"></i> Essayer maintenant
                    </a>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>

        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="feature-card">
                <div class="feature-card-body">
                    <div class="feature-icon">
                        <i class="fas fa-book-open" style="color: #27ae60;"></i>
                    </div>
                    <h3 class="feature-title">Ressources Culturelles et Communautaires</h3>
                    <p class="feature-text">Accédez à une bibliothèque de ressources utiles, incluant :</p>
                    <ul class="feature-list">
                        <li>Guides d'intégration pour nouveaux arrivants</li>
                        <li>Informations sur les démarches administratives</li>
                        <li>Archives d'événements culturels (photos, vidéos, articles)</li>
                    </ul>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>

        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="feature-card">
                <div class="feature-card-body">
                    <div class="feature-icon">
                        <i class="fas fa-users" style="color: #c0392b;"></i>
                    </div>
                    <h3 class="feature-title">Communauté Marocaine Active</h3>
                    <p class="feature-text">Rejoignez une communauté engagée d'organisations et de bénévoles marocains au Canada :</p>
                    <ul class="feature-list">
                        <li>Échange d'expériences</li>
                        <li>Partenariats culturels et sociaux</li>
                        <li>Participation à des événements et initiatives locales</li>
                    </ul>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>

        <div class="col-lg-6 mb-3"> <!-- Réduit de mb-4 à mb-3 -->
            <div class="feature-card">
                <div class="feature-card-body">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-alt" style="color: #27ae60;"></i>
                    </div>
                    <h3 class="feature-title">Calendrier des Événements</h3>
                    <p class="feature-text">Consultez et participez aux activités culturelles : festivals, expositions, conférences, soirées marocaines...</p>
                    <a href="{% url 'events_list' %}" class="btn btn-sm" style="background-color: #27ae60; color: white;">
                        <i class="fas fa-eye"></i> Voir les événements
                    </a>
                </div>
                <div class="moroccan-pattern"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Open chat when the button is clicked
    document.addEventListener('DOMContentLoaded', function() {
        const openChatBtn = document.getElementById('open-chat-btn');
        if (openChatBtn) {
            openChatBtn.addEventListener('click', function() {
                const chatToggleBtn = document.querySelector('.chat-toggle-btn');
                if (chatToggleBtn) {
                    chatToggleBtn.click();
                }
            });
        }
    });
</script>
{% endblock %}