#!/usr/bin/env python
"""
Script pour mettre à jour les images des événements avec des URLs fonctionnelles
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_project.settings')
django.setup()

from chatbot_app.models import Event

def update_event_images():
    """Met à jour les images des événements avec des URLs fonctionnelles"""
    
    # Mapping des titres d'événements vers des images Unsplash de haute qualité
    image_mapping = {
        "Festival Marocain de Montréal": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Soirée Ramadan: Iftar communautaire": "https://images.unsplash.com/photo-1542816417-0983c9c9ad53?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Nuits Ramadan Culturelles 2025 - Traditions et Spiritualité": "https://images.unsplash.com/photo-1542816417-0983c9c9ad53?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Festival du Théâtre des Cultures - Édition 2025": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Exposition \"Artisanat Royal Marocain - Patrimoine Millénaire\"": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Festival Mawazine 2025 - Rythmes du Monde": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Festival Marocain de Montréal 2024": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Soirée Culturelle Marocaine": "https://images.unsplash.com/photo-1542816417-0983c9c9ad53?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "Concert de Musique Gnawa": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
    
    # Images par défaut pour différents types d'événements
    default_images = {
        "festival": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "concert": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "exposition": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "soiree": "https://images.unsplash.com/photo-1542816417-0983c9c9ad53?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "theatre": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        "default": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
    
    try:
        events = Event.objects.all()
        updated_count = 0
        
        print("🖼️ Mise à jour des images des événements...")
        print("=" * 60)
        
        for event in events:
            old_image = event.image_url
            
            # Chercher une image spécifique pour cet événement
            if event.title in image_mapping:
                new_image = image_mapping[event.title]
            else:
                # Déterminer le type d'événement et assigner une image par défaut
                title_lower = event.title.lower()
                if "festival" in title_lower:
                    new_image = default_images["festival"]
                elif "concert" in title_lower or "musique" in title_lower:
                    new_image = default_images["concert"]
                elif "exposition" in title_lower:
                    new_image = default_images["exposition"]
                elif "soirée" in title_lower or "soiree" in title_lower:
                    new_image = default_images["soiree"]
                elif "théâtre" in title_lower or "theatre" in title_lower:
                    new_image = default_images["theatre"]
                else:
                    new_image = default_images["default"]
            
            # Mettre à jour seulement si l'image a changé
            if old_image != new_image:
                event.image_url = new_image
                event.save()
                updated_count += 1
                
                print(f"✅ {event.title}")
                print(f"   📅 Date: {event.date_start}")
                print(f"   📍 Lieu: {event.location}")
                print(f"   🖼️ Ancienne image: {old_image or 'Aucune'}")
                print(f"   🖼️ Nouvelle image: {new_image}")
                print("-" * 60)
            else:
                print(f"ℹ️ {event.title} - Image déjà à jour")
        
        print(f"\n🎉 Mise à jour terminée!")
        print(f"📊 {updated_count} événement(s) mis à jour sur {events.count()} total")
        
        if updated_count > 0:
            print("\n💡 Les images sont maintenant visibles sur votre site!")
            print("🔄 Actualisez votre navigateur pour voir les changements.")
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = update_event_images()
    if success:
        print("\n✅ Script exécuté avec succès!")
    else:
        print("\n❌ Erreur lors de l'exécution du script!")
        sys.exit(1)
